import 'dart:convert';
import 'dart:developer';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;

class AiChatApis {



  static String basUrl = "https://project.trogon.info/edutalim/api/";

  static Future ai_chat_messages() async {

    var url = Uri.parse('${basUrl}ai_features/ai_chat_messages?auth_token=${GetStorage().read('auth_token').toString()}');
    log(url.toString());
    http.Response response = await http.get(url, headers: {"content-type": "application/json"});
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';

    }
  }

  static Future<dynamic> ai_send_message(String message,) async {
    var url = Uri.parse('${basUrl}ai_features/ai_send_message');
    print(url);
    try {
      var request = http.MultipartRequest('POST', url);

      request.fields['message'] = message.toString();
      request.fields['auth_token'] = GetStorage().read('auth_token') ?? '';

      var streamedResponse = await request.send();

      var response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        var responseData = jsonDecode(utf8.decode(response.bodyBytes));
        return responseData;
      } else {
        return 'failed: ${response.statusCode} - ${response.reasonPhrase}';
      }
    } catch (e) {
      return 'failed:$e';
    }
  }
}
