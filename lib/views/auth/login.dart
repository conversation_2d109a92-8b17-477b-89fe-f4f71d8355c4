import 'package:edutalim/views/auth/register.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../controllers/logincontroller.dart';

class Login extends StatefulWidget {
  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  Logincontroller controller = Get.put(Logincontroller());

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Logincontroller>(builder: (controller) {
      return GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          backgroundColor: textwhiteColor,
          // appBar: AppBar(
          //   toolbarHeight: 0,
          //   backgroundColor: primaryColor,
          //   surfaceTintColor: primaryColor,
          // ),
          body: Container(
            width: Get.width,
            height: Get.height,
            // padding: EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              // crossAxisAlignment: CrossAxisAlignment.center,
              // mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: Get.height / 4,
                  width: Get.width,
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  decoration: BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage("assets/bg/signup_bg.png"),
                          fit: BoxFit.cover)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 30,
                      ),
                      // IconButton(onPressed: (){, icon: icon)
                      Text(
                        "Sign in",
                        style: TextStyle(
                            color: textwhiteColor,
                            fontWeight: FontWeight.bold,
                            fontSize: Get.width / 16,
                            fontFamily: font_bold),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Text(
                        "Sign in to continue your Education  journey.",
                        style: TextStyle(
                            color: textwhiteColor, fontSize: Get.width / 34),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          children: [
                            SizedBox(
                              height: 20,
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 10),
                              alignment: Alignment.centerLeft,
                              // child: Text("Login",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w700,fontSize: Get.width/15),),
                              child: Text(
                                "Phone No",
                                style: TextStyle(
                                    color: textblackColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: Get.width / 28),
                              ),
                            ),
                            SizedBox(
                              height: 13,
                            ),
                            Container(
                              height: 80,
                              decoration: BoxDecoration(
                                  color: Colors.transparent,
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(5),
                                      topRight: Radius.circular(5))),
                              // padding: EdgeInsets.only(left: MediaQuery.of(context).size.width/10,right: MediaQuery.of(context).size.width/10,top: 10),
                              width: MediaQuery.of(context).size.width * .9,
                              child: IntlPhoneField(
                                dropdownIconPosition: IconPosition.trailing,
                                flagsButtonMargin: EdgeInsets.only(left: 10),
                                controller: controller.phoneclr,
                                style: const TextStyle(
                                    fontSize: 16, color: Colors.black),
                                decoration: InputDecoration(
                                  // counterText: '',
                                  fillColor: Colors.white,
                                  filled: true,
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: textblackColor.withOpacity(0.4),
                                        width: 0.6),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(5)),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: textblackColor.withOpacity(0.4),
                                        width: 0.5),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(5)),
                                  ),
                                  border: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: Colors.black54, width: 0.1),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(5)),
                                  ),

                                  errorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: Colors.red, width: 0.5),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10)),
                                  ),

                                  focusedErrorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                        color: Colors.red, width: 0.5),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10)),
                                  ),
                                  hintStyle: const TextStyle(
                                      fontSize: 16, color: Colors.black38),
                                  hintText: "Mobile Number",
                                ),
                                initialCountryCode: 'IN',
                                onChanged: (phone) {
                                  // print(phone.completeNumber);
                                  // print(phone.isValidNumber());
                                },
                                onCountryChanged: (country_code) {
                                  // print('code--------------${country_code.code.toString()}');
                                  // print('dialCode--------------${country_code.dialCode.toString()}');
                                  // print('flag--------------${country_code.flag.toString()}');
                                  // print('regionCode--------------${country_code.regionCode.toString()}');
                                  // print('fullCountryCode--------------${country_code.fullCountryCode.toString()}');
                                  print(
                                      'fullCountryCode--------------${country_code.name.toString()}');
                                  setState(() {
                                    controller.countrycode_number =
                                        country_code.dialCode.toString();
                                  });
                                },
                                // onChanged:
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            SizedBox(
                              height: 25,
                            ),
                            common_button_gradient_secondary(
                              onPressed: () {
                                // if(controller.phoneclr.text.toString() == '9946801100'){
                                //   Get.to(()=> OtpPage(phone: '', userId: '', country_code: '', from: '',));
                                // }
                                // else{
                                //   toast_info('User Not Found');
                                // }
                                controller.get_login();
                              },
                              borderRadius: BorderRadius.circular(8),
                              width: Get.width * .9,
                              child: controller.isLoading
                                  ? Container(
                                      height: 25,
                                      width: 25,
                                      child: CircularProgressIndicator(
                                        color: textwhiteColor,
                                      ),
                                    )
                                  : Text(
                                      "Continue",
                                      style: TextStyle(
                                          color: textwhiteColor,
                                          fontWeight: FontWeight.w600,
                                          fontFamily: font_semibold,
                                          fontSize: Get.width / 25),
                                    ),
                            ),

                            SizedBox(
                              height: 20,
                            ),
                            // GestureDetector(
                            //   onTap: (){
                            //     launchURL('https://prepscale.com/privacy-policy-2/');
                            //   },
                            //   child: Container(
                            //       width: Get.width/1.1,
                            //       color: Colors.transparent,
                            //       child: Wrap(
                            //         alignment: WrapAlignment.center,
                            //         children: [
                            //           Text("By continuing you are accepting our  ",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w400,fontSize: Get.width/30),),
                            //           // SizedBox(width: 3,),
                            //           Text("Privacy Policy ",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w700,fontSize: Get.width/30),),
                            //           // SizedBox(width: 3,),
                            //           Text("and ",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w400,fontSize: Get.width/30),),
                            //           // SizedBox(width: 3,),
                            //           Text("Terms & Conditions",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w700,fontSize: Get.width/30),),
                            //         ],
                            //       )
                            //   ),
                            // ),
                            //
                            Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text("New to $appName ?"),
                                    GestureDetector(
                                      onTap: () {
                                        Get.to(register());
                                      },
                                      child: Text(
                                        " Sign Up",
                                        style: TextStyle(
                                            color: primaryColor,
                                            fontFamily: font_bold),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                )
                              ],
                            ),

                            // GestureDetector(
                            //   onTap: (){
                            //     Get.to(register());
                            //   },
                            //   child: Container(
                            //     decoration: BoxDecoration(
                            //         color: textwhiteColor,
                            //         borderRadius: BorderRadius.circular(10),
                            //         border: Border.all(color: primaryColor)
                            //     ),
                            //     width: Get.width,
                            //     alignment: Alignment.center,
                            //     padding: EdgeInsets.all(15),
                            //     child: Text("Create New Account",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w600,fontSize: Get.width/25),),
                            //   ),
                            // ),
                            SizedBox(
                              height: 40,
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}
