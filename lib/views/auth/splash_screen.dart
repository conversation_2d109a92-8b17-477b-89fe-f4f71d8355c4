import 'package:get/get.dart';
import 'package:flutter/material.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';

import '../../controllers/commoncontroller.dart';

class SplashScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => StartState();
}

class StartState extends State<SplashScreen> {
  Commoncontroller controller = Get.put(Commoncontroller());

  @override
  void initState() {
    super.initState();
    controller.get_appversion();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: textwhiteColor,
      body: Stack(
        children: [
          AppBar(toolbarHeight: 0, backgroundColor: textwhiteColor, surfaceTintColor: textwhiteColor),
          Container(
            width: Get.width,
            height: Get.height,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [textwhiteColor, textwhiteColor], begin: Alignment.topCenter, end: Alignment.bottomCenter),
            ),
            child: Center(
                child: Stack(
              children: [
                Positioned(
                    top: Get.height / 3,
                    left: 0,
                    right: 0,
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.all(10),
                          child: ShowUp(
                            delay: 1000,
                            child: Image.asset("assets/logo/logo.png", width: Get.height / 3),
                          ),
                        ),
                      ],
                    )),
              ],
            )),
          ),
        ],
      ),
    );
  }
}
