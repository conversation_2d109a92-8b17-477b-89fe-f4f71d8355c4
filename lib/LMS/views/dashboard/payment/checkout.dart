// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:stockex/controller/planscontroller.dart';
//
// import '../../../components/constants.dart';
// import 'package:edutalim/components/utils.dart';
//
// class checkout extends StatefulWidget {
//   Map plan;
//
//   checkout({super.key, required this.plan});
//
//   @override
//   State<checkout> createState() => _checkoutState();
// }
//
// class _checkoutState extends State<checkout> {
//   Plancontroller controller = Get.put(Plancontroller());
//
//   bool is_redeemed = false, coupon_applied = false, createorderLoading = false;
//   int final_amount = 0, total_amount = 0;
//   TextEditingController coupon_code = TextEditingController();
//   List coupon_data = [];
//
//   @override
//   void initState() {
//     final_amount = int.parse(widget.plan['payable_amount'].toString());
//     total_amount = int.parse(widget.plan['payable_amount'].toString());
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     var mode = GetStorage().read('dark_mode');
//
//     return GetBuilder<Plancontroller>(builder: (context) {
//       return Scaffold(
//         backgroundColor: mode.toString() == "1" ? primaryColor : Colors.white,
//         body: Stack(
//           children: [
//             Container(
//               decoration: const BoxDecoration(
//                 image: DecorationImage(image: AssetImage('assets/logo/logo_transp.png'), opacity: 0.05, fit: BoxFit.fitHeight),
//               ),
//               width: Get.width,
//               height: Get.height,
//               child: SingleChildScrollView(
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     AppBar(
//                       backgroundColor: Color(0xff00FFFFFF),
//                       surfaceTintColor: Color(0xff00FFFFFF),
//                       elevation: 0,
//                       iconTheme: IconThemeData(color: mode.toString() == "1" ? Colors.white : Colors.black),
//                       automaticallyImplyLeading: true,
//                       centerTitle: true,
//                       title: Text("COMPLETE PAYMENT", style: TextStyle(color: mode.toString() == "1" ? Colors.white : Colors.black, fontSize: Get.width / 22)),
//                     ),
//                     Container(
//                       padding: EdgeInsets.symmetric(vertical: 20),
//                       child: ClipRRect(
//                         child: Image.asset(mode.toString() == "1" ? 'assets/logo/logo_transp_white.png' : 'assets/logo/logo_transp.png', width: Get.width / 3),
//                       ),
//                     ),
//                     SizedBox(height: 10),
//                     Container(
//                       decoration: BoxDecoration(borderRadius: BorderRadius.circular(20)),
//                       width: Get.width / 1.1,
//                       padding: EdgeInsets.all(15),
//                       child: Column(
//                         children: [
//                           Container(
//                             width: Get.width / 1.2,
//                             child: Text(
//                               "${widget.plan['title']}",
//                               style: TextStyle(fontFamily: 'poppins_regular', fontSize: Get.width / 18, fontWeight: FontWeight.w600, color: mode.toString() == "1" ? Colors.white : textblackColor),
//                               textAlign: TextAlign.center,
//                             ),
//                           ),
//
//                           SizedBox(height: 25),
//
//                           Container(
//                             width: Get.width,
//                             padding: EdgeInsets.all(10),
//                             decoration: BoxDecoration(color: mode.toString() == "1" ? Colors.white.withValues(alpha: 0.3) : Colors.white, borderRadius: BorderRadius.circular(20), border: Border.all(color: textblackColor, width: 0.2)),
//                             child: Column(
//                               children: [
//                                 SizedBox(height: 5),
//                                 Container(
//                                   padding: EdgeInsets.symmetric(horizontal: 5),
//                                   child: Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Row(
//                                         children: [
//                                           Image.asset('assets/icons/coin.png', width: 18, height: 18),
//                                           Text(' Available Coins', style: TextStyle(color: mode.toString() == "1" ? Colors.white : Colors.black)),
//                                         ],
//                                       ),
//                                       Text(
//                                         "${widget.plan['balance_points']}",
//                                         style: TextStyle(fontWeight: FontWeight.w800, color: mode.toString() == "1" ? Colors.white : Colors.black),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 SizedBox(height: 10),
//                                 Container(
//                                   padding: EdgeInsets.symmetric(horizontal: 5),
//                                   child: Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Row(
//                                         children: [
//                                           Icon(Icons.currency_rupee, size: 18, color: mode.toString() == "1" ? Colors.white70 : Colors.black54),
//                                           Text(' Available Coins Balance', style: TextStyle(color: mode.toString() == "1" ? Colors.white : Colors.black)),
//                                         ],
//                                       ),
//                                       Text(
//                                         "₹${widget.plan['balance_in_rupees']}",
//                                         style: TextStyle(fontWeight: FontWeight.w600, fontFamily: '', color: mode.toString() == "1" ? Colors.white : Colors.black),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 SizedBox(height: 10),
//                                 Container(
//                                   padding: EdgeInsets.symmetric(horizontal: 5),
//                                   child: Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Row(
//                                         children: [
//                                           Icon(Icons.currency_rupee, size: 18, color: mode.toString() == "1" ? Colors.white70 : Colors.black54),
//                                           Text(' Applicable amount', style: TextStyle(color: mode.toString() == "1" ? Colors.white : Colors.black)),
//                                         ],
//                                       ),
//                                       Text(
//                                         "₹${widget.plan['applicable_amount'].toString()}",
//                                         style: TextStyle(fontWeight: FontWeight.w800, color: mode.toString() == "1" ? Colors.white : Colors.black, fontFamily: ''),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 SizedBox(height: 10),
//                                 if (is_redeemed)
//                                   Column(
//                                     children: [
//                                       SizedBox(height: 10),
//                                       Container(
//                                         padding: EdgeInsets.symmetric(horizontal: 5),
//                                         child: Row(
//                                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                           children: [
//                                             const Row(
//                                               children: [
//                                                 Icon(Icons.check, size: 18, color: Colors.green),
//                                                 Text(' Redeemed amount', style: TextStyle(color: Colors.green)),
//                                               ],
//                                             ),
//                                             Text(
//                                               "₹${widget.plan['applicable_amount'].toString()}",
//                                               style: TextStyle(fontWeight: FontWeight.w800, color: Colors.green, fontFamily: ''),
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 // if (coupon_applied)
//                                 //   Column(
//                                 //     children: [
//                                 //       SizedBox(
//                                 //         height: 10,
//                                 //       ),
//                                 //       Container(
//                                 //         padding: EdgeInsets.symmetric(horizontal: 5),
//                                 //         child: Row(
//                                 //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                 //           children: [
//                                 //             Row(
//                                 //               children: [
//                                 //                 Icon(Icons.check, size: 18, color: Colors.green),
//                                 //                 Text(' Coupon applied', style: TextStyle(color: Colors.green)),
//                                 //               ],
//                                 //             ),
//                                 //             Text(
//                                 //               "₹" + coupon_data[0]['coupon_discount_amount'].toString(),
//                                 //               style: TextStyle(fontWeight: FontWeight.w800, color: Colors.green),
//                                 //             ),
//                                 //           ],
//                                 //         ),
//                                 //       ),
//                                 //     ],
//                                 //   ),
//                                 SizedBox(height: 15),
//                                 Container(
//                                   margin: EdgeInsets.symmetric(horizontal: 5),
//                                   decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), border: Border.all(color: mode.toString() == "1" ? Colors.white60 : primaryColor, width: 0.5)),
//                                   padding: EdgeInsets.only(top: 10, bottom: 15),
//                                   child: Column(
//                                     children: [
//                                       Row(
//                                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                         children: [
//                                           Container(
//                                             padding: EdgeInsets.only(left: 10),
//                                             child: Text(
//                                               "Redeem ₹${widget.plan['applicable_amount']}",
//                                               style: TextStyle(fontWeight: FontWeight.w500, fontSize: Get.width / 22, color: mode.toString() == "1" ? Colors.white : Colors.black38),
//                                             ),
//                                           ),
//                                           Transform.scale(
//                                             scale: 0.7,
//                                             child: CupertinoSwitch(
//                                               activeColor: primaryColor,
//                                               thumbColor: is_redeemed ? textwhiteColor.withOpacity(0.9) : textwhiteColor,
//                                               value: is_redeemed,
//                                               inactiveTrackColor: mode.toString() == "1" ? Colors.white24 : Colors.black54,
//                                               onChanged: (v) {
//                                                 if (coupon_applied) {
//                                                   toast_warning('You can only apply a coupon code or redeem coins at a time');
//                                                 } else {
//                                                   print(widget.plan['applicable_amount'].toString() + "-------1--");
//                                                   print(final_amount.toString() + "-------2--");
//                                                   setState(() {
//                                                     if (int.parse(widget.plan['applicable_amount'].toString()) >= final_amount) {
//                                                       toast_warning(widget.plan['redeem_text'].toString());
//                                                     } else if (widget.plan['can_redeem'].toString() == "1") {
//                                                       if (is_redeemed) {
//                                                         is_redeemed = false;
//                                                         final_amount = final_amount + int.parse(widget.plan['applicable_amount'].toString());
//                                                       } else {
//                                                         is_redeemed = true;
//                                                         final_amount = final_amount - int.parse(widget.plan['applicable_amount'].toString());
//                                                       }
//                                                     } else {
//                                                       toast_warning('Not applicable');
//                                                     }
//                                                   });
//                                                 }
//                                               },
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                                       const SizedBox(height: 10),
//                                       Container(
//                                         padding: const EdgeInsets.symmetric(horizontal: 20),
//                                         decoration: BoxDecoration(color: textwhiteColor),
//                                         child: Text(
//                                           widget.plan['redeem_text'],
//                                           style: TextStyle(fontWeight: FontWeight.w300, fontSize: Get.width / 30, color: widget.plan['can_redeem'].toString() == '0' || int.parse(widget.plan['applicable_amount'].toString()) >= final_amount ? Colors.orange : Colors.black),
//                                           textAlign: TextAlign.center,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 if ((total_amount - final_amount) > 0)
//                                   Column(
//                                     children: [
//                                       SizedBox(height: 10),
//                                       Container(
//                                         padding: EdgeInsets.symmetric(horizontal: 5, vertical: 15),
//                                         child: Text(
//                                           ' You will save ' "₹" + (total_amount - final_amount).toString() + " on this purchase",
//                                           style: TextStyle(fontWeight: FontWeight.w500, color: Colors.green),
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 SizedBox(height: 15),
//                                 Container(
//                                   padding: EdgeInsets.symmetric(horizontal: 8),
//                                   child: Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Container(
//                                         child: Text("Total Payable", style: TextStyle(fontFamily: '', fontSize: Get.width / 24, fontWeight: FontWeight.w500)),
//                                       ),
//                                       Container(
//                                         child: Text("₹$final_amount/-", style: TextStyle(fontFamily: '', fontSize: Get.width / 15, fontWeight: FontWeight.w700)),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                                 SizedBox(height: 10),
//                               ],
//                             ),
//                           ),
//
//                           SizedBox(height: 30),
//
//                           // coupon_applied ?
//                           // Container(
//                           //   decoration: BoxDecoration(color: textwhiteColor,borderRadius: BorderRadius.circular(10)),
//                           //   padding: EdgeInsets.all(17),
//                           //   child: Row(
//                           //     children: [
//                           //       Icon(Icons.check_circle,color: Colors.green,size: 18,),
//                           //       SizedBox(width: 5,),
//                           //       Container(
//                           //         child: Text(coupon_data[0]['message'].toString(),style: TextStyle(fontSize: Get.width/29,color: Colors.green),),
//                           //       ),
//                           //     ],
//                           //   ),
//                           // )
//                           //     :
//                           // Container(
//                           //   width: Get.width,
//                           //   child: Row(
//                           //     mainAxisAlignment: MainAxisAlignment.center,
//                           //     children: [
//                           //       Expanded(
//                           //         child: Container(
//                           //           // height: 40,
//                           //           child: TextField(
//                           //             readOnly: is_redeemed ? true : false,
//                           //             style: TextStyle(fontSize: 15.0,height: 1.5,color: Colors.black),
//                           //             controller: coupon_code,
//                           //             decoration: InputDecoration(
//                           //               focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.black38,width: 0.5),borderRadius: BorderRadius.horizontal(left: Radius.circular(10.0))),
//                           //               enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.black38,width: 0.5),borderRadius: BorderRadius.horizontal(left: Radius.circular(10.0))),
//                           //               filled: true,
//                           //               hintStyle: TextStyle(color: const Color(0xFFA1A3AB),fontFamily: "poppins_regular"),
//                           //               hintText: "Coupon Code",
//                           //               fillColor: Colors.white,
//                           //               contentPadding: const EdgeInsets.symmetric(vertical: 10.0,horizontal: 20),
//                           //             ),
//                           //           ),
//                           //         ),
//                           //       ),
//                           //
//                           //       GestureDetector(
//                           //         onTap: (){
//                           //           if(is_redeemed)
//                           //           {
//                           //             toast_warning('You can only apply a coupon code or redeem coins at a time');
//                           //           }else{
//                           //             get_applycoupon();
//                           //           }
//                           //         },
//                           //         child:  Container(
//                           //           decoration: BoxDecoration(
//                           //             color: is_redeemed ? primaryColor.withOpacity(0.3) : primaryColor,
//                           //             borderRadius: BorderRadius.horizontal(right: Radius.circular(10.0)),
//                           //           ),
//                           //           padding: EdgeInsets.all(14),
//                           //           child: Text("Apply",style: TextStyle(color: Colors.white,fontWeight: FontWeight.w700),),
//                           //         ),
//                           //       ),
//                           //
//                           //     ],
//                           //   ),
//                           // ),
//
//                           SizedBox(height: 20),
//
//                           GestureDetector(
//                             onTap: () {
//                               controller.get_create_order(
//                                 widget.plan['phone'].toString(),
//                                 widget.plan['email'].toString(),
//                                 widget.plan['desc'].toString(),
//                                 final_amount,
//                                 widget.plan['razorpay_api_key'].toString(),
//                                 widget.plan['razorpay_api_secret_key'].toString(),
//                                 widget.plan['id'].toString(),
//                                 is_redeemed ? "1" : "0",
//                                 is_redeemed ? widget.plan['applicable_amount'].toString() : "0",
//                               );
//                             },
//                             child: Container(
//                               decoration: BoxDecoration(color: primaryColor, borderRadius: BorderRadius.circular(10)),
//                               width: Get.width,
//                               alignment: Alignment.center,
//                               padding: const EdgeInsets.all(15),
//                               child: createorderLoading
//                                   ? Container(
//                                       height: 23,
//                                       width: 23,
//                                       margin: const EdgeInsets.all(2),
//                                       child: CircularProgressIndicator(color: textwhiteColor),
//                                     )
//                                   : Text("Buy Now", style: TextStyle(color: Colors.white, fontWeight: FontWeight.w700, fontSize: Get.width / 20)),
//                             ),
//                           ),
//                           const SizedBox(height: 15),
//                         ],
//                       ),
//                     ),
//                     const SizedBox(height: 10),
//                     Text("Secure Payments by Razorpay", style: TextStyle(color: secondaryColor, fontStyle: FontStyle.italic, fontSize: Get.width / 30)),
//                     const SizedBox(height: 30),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       );
//     });
//   }
// }
