import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';


class force_update extends StatelessWidget {
  List data;
  force_update({required this.data});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: textwhiteColor,
      body: Container(
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppBar(toolbarHeight: 0,backgroundColor: Color(0xff00ffffff),),

            Image.asset('assets/logo/logo_text.png',width: Get.width/2,),
             SizedBox(height: Get.height/5,),
            Text("V"+data[0]['ios_force_update'].toString(),style: TextStyle(color: primaryColor,fontWeight: FontWeight.w800,fontFamily: 'poppins_regular'),),
            Text(appName,style: TextStyle(color: secondaryColor,fontWeight: FontWeight.w800,fontFamily: 'poppins_regular'),),
            const SizedBox(height: 30,),
            const Text("New Version available, Update now",style: TextStyle(fontFamily: 'poppins_regular'),),
            const SizedBox(height: 40,),
            common_button(
              title: 'Update Now',
              bg: primaryColor,
              title_clr: textwhiteColor,
              isLoading: false,
              width: Get.width/1.2,
              tap: (){
                launchURL(GetPlatform.isAndroid ? androidLink : iosLink);
              },
            ),

          ],
        ),
      ),
    );
  }
}
