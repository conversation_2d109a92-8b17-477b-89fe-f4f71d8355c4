
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../components/constants.dart';
import '../views/auth/splash_screen.dart';

class error_page extends StatefulWidget {
  String code,status;
  error_page({required this.code, required this.status});

  @override
  State<error_page> createState() => _error_pageState();
}

class _error_pageState extends State<error_page> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(),
      body: Container(

        width: Get.width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/icons/robot.png',width: Get.width/3,),
            SizedBox(height: 20,),
            Text(widget.code.toString(),style: TextStyle(color: Colors.black,fontSize: Get.width/13,fontWeight: FontWeight.w600),),
            SizedBox(height: 5,),
            Text(widget.status.toString(),style: TextStyle(color: Colors.red,fontSize: Get.width/19,fontWeight: FontWeight.w600),),
            SizedBox(height: 5,),
            Text("Contact your IT Team for a resolution",style: TextStyle(color: Colors.black54,fontSize: Get.width/27,fontWeight: FontWeight.w400),),
            SizedBox(height: 20,),
           Container(
             padding: EdgeInsets.symmetric(horizontal: 20),
             width: Get.width,
             child:  Row(
               children: [

                 Expanded(
                   child: GestureDetector(
                     onTap: (){
                       Get.offAll(SplashScreen());
                     },
                     child: Container(
                       decoration: BoxDecoration(
                         color: primaryColor,
                         borderRadius: BorderRadius.circular(5),
                       ),
                       alignment: Alignment.center,
                       padding: EdgeInsets.all(10),
                       child: Text("Retry",style: TextStyle(color: Colors.white,fontSize: Get.width/25,fontWeight: FontWeight.w600),),
                     ),
                   ),
                 ),
                 SizedBox(width: 10,),
                 Expanded(
                   child: GestureDetector(
                     onTap: (){
                       GetStorage().erase();
                       Get.offAll(SplashScreen());
                     },
                     child: Container(
                       decoration: BoxDecoration(
                         color: primaryColor,
                         borderRadius: BorderRadius.circular(5),
                       ),
                       alignment: Alignment.center,
                       padding: EdgeInsets.all(10),
                       child: Text("Reset",style: TextStyle(color: Colors.white,fontSize: Get.width/25,fontWeight: FontWeight.w600),),
                     ),
                   ),
                 ),

               ],
             ),
           ),
          ],
        )
      ),
    );
  }
}
