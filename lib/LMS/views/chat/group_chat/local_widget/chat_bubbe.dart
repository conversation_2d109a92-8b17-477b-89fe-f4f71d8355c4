import 'package:edutalim/components/constants.dart';
import 'package:edutalim/LMS/views/chat/group_chat/local_widget/image_preview.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatBubble extends StatelessWidget {
  ChatBubble(
      {super.key,
      required this.username,
      required this.isHighlight,
      required this.messageType,
      required this.message,
      required this.profiel_image_path,
      required this.messageDate,
      required this.messageFile,
      required this.commentOnTap,
      required this.onLongPress,
      required this.moreOnTap});
  String message, profiel_image_path;
  String username, messageDate;
  String isHighlight;
  String messageType;
  String messageFile;
  void Function()? commentOnTap;
  void Function()? onLongPress;
  void Function()? moreOnTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: onLongPress,
      child: Container(
        margin: const EdgeInsets.all(5),
        // padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: textwhiteColor,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 10),
                  child: Row(
                    children: [
                      Container(
                        width: 40, // Set the desired width
                        height: 40, // Set the desired height
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          border: Border.all(color: primaryColor, width: 2),
                          shape: BoxShape.circle,
                          image: DecorationImage(
                            image: NetworkImage(profiel_image_path),
                            fit: BoxFit
                                .cover, // Ensures the image covers the container fully
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                username,
                                style: TextStyle(
                                    // fontFamily: font_regular,
                                    fontSize: 12),
                              ),
                              if (isHighlight == "1")
                                Row(
                                  children: [
                                    const SizedBox(
                                      width: 3,
                                    ),
                                    Container(
                                        margin: const EdgeInsets.only(top: 4),
                                        child: Icon(
                                          Icons.verified,
                                          color: Colors.blue,
                                          size: Get.width / 28,
                                        )),
                                  ],
                                ),
                            ],
                          ),
                          Text(
                            messageDate,
                            style: TextStyle(fontSize: 10, color: Colors.grey),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                IconButton(onPressed: moreOnTap, icon: Icon(Icons.more_vert))
              ],
            ),
            // verticalSpace(15),
            if (messageType == "1")
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(message),
                  ),
                  messageFile != ""
                      ? Image.network(
                          // height: Get.width - 125,
                          // width: Get.width - 150,
                          messageFile,
                          fit: BoxFit.contain,
                        )
                      : SizedBox()
                ],
              ),

            if (messageType == "2")
              GestureDetector(
                onTap: () {
                  Get.to(ImagePreviewPage(imageUrl: messageFile));
                },
                child: Image.network(
                  // height: Get.width - 125,
                  // width: Get.width - 150,
                  messageFile,
                  fit: BoxFit.contain,
                ),
              ),
            // if (messageType == "4")
            //   SimpleAudioPlayer(senderName: username, audioUrl: messageFile),
            SizedBox(height: 15),
            const Divider(
              color: Color.fromARGB(255, 237, 234, 234),
            ),
            SizedBox(height: 15),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // customIcon(
                  //   "assets/icons/like.png",
                  //   "Like",
                  //   () {},
                  // ),
                  customIcon(
                      "assets/icons/comment.png", "Comment", commentOnTap),
                  // customIcon("assets/icons/whatsapp_chat.png", "Share", () {}),
                ],
              ),
            ),
            // const Divider(),
            SizedBox(height: 15),
          ],
        ),
      ),
    );
  }

  InkWell customIcon(image_path, title, void Function()? onTap) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              // Text(
              //   "10 ",
              //   style: TextStyle(color: Colors.grey),
              // ),
              Image.asset(
                image_path,
                height: 20,
              ),
              SizedBox(
                width: 5,
              ),
              Text(
                title,
                style: TextStyle(color: Colors.blueGrey, fontSize: 10),
              )
            ],
          ),
        ],
      ),
    );
  }
}
