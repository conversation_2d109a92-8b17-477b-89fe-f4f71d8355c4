// import 'package:call_log/call_log.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
//
//
//
//
// class call_log_overview extends StatefulWidget {
//   @override
//   _CallLogPageState createState() => _CallLogPageState();
// }
//
// class _CallLogPageState extends State<call_log_overview> {
//   List<CallLogEntry> callLogs = [];
//   int missed = 0,incoming = 0,outgoing = 0,other = 0,unknown = 0,contacts = 0;
//
//   counter(type,name)async{
//     setState(() {
//       if(type.toString() == "missed")
//       {
//         missed += 1;
//       }
//       else if(type.toString() == "incoming")
//       {
//         incoming += 1;
//       }
//       else if(type.toString() == "outgoing")
//       {
//         outgoing += 1;
//       }else{
//         other += 1;
//       }
//
//       if(name.toString() == "unknown")
//       {
//         unknown += 1;
//       }
//       else{
//         contacts += 1;
//       }
//
//     });
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     getCallLogs();
//   }
//
//   Future<void> getCallLogs() async {
//     Iterable<CallLogEntry> entries = await CallLog.get();
//     setState(() {
//       callLogs = entries.toList();
//     });
//
//     for(var i=0;i<=callLogs.length;i++){
//       CallLogEntry log = callLogs[i];
//       counter(log.callType.toString().split('.')[1].toString(),log.name ?? 'unknown');
//     }
//
//   }
//
//   String formatTimestamp(int timestamp) {
//     DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
//     String formattedDate = DateFormat('MMM dd, yyyy').format(dateTime);
//     String formattedTime = DateFormat('hh:mm a').format(dateTime);
//     return '$formattedDate - $formattedTime';
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//         padding: EdgeInsets.symmetric(vertical: 10),
//         child: Column(
//           children: [
//
//             Container(
//               // decoration: BoxDecoration(
//               // color: Colors.black.withOpacity(0.02),
//               //     border: Border.all(color: clr.withOpacity(1),
//               //         width: 0.5),
//               //     borderRadius: BorderRadius.circular(10)
//               // ),
//               margin: EdgeInsets.only(bottom: 10),
//               padding: EdgeInsets.all(3),
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//
//                   Row(
//                     children: [
//                       Icon(Icons.call_received,size: 20,color: Colors.blue,),
//                       SizedBox(width: 15,),
//                       Container(
//                         color: Colors.transparent,
//                         child: Text('Incoming',style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w500,fontSize: Get.width/25),textAlign: TextAlign.center,),
//                       ),
//                     ],
//                   ),
//
//                   Container(
//                     color: Colors.transparent,
//                     child: Text(incoming.toString(),style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w700,fontSize: Get.width/30),textAlign: TextAlign.center,),
//                   ),
//
//                 ],
//               ),
//             ),
//             Divider(),
//             Container(
//               margin: EdgeInsets.only(bottom: 10),
//               padding: EdgeInsets.all(3),
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//
//                   Row(
//                     children: [
//                       Icon(Icons.call_made,size: 20,color: Colors.orange,),
//                       SizedBox(width: 15,),
//                       Container(
//                         color: Colors.transparent,
//                         child: Text('Outgoing',style: TextStyle(color: Colors.orange, fontWeight: FontWeight.w500,fontSize: Get.width/25),textAlign: TextAlign.center,),
//                       ),
//                     ],
//                   ),
//
//                   Container(
//                     color: Colors.transparent,
//                     child: Text(outgoing.toString(),style: TextStyle(color: Colors.orange, fontWeight: FontWeight.w700,fontSize: Get.width/30),textAlign: TextAlign.center,),
//                   ),
//
//                 ],
//               ),
//             ),
//             Divider(),
//             Container(
//               margin: EdgeInsets.only(bottom: 10),
//               padding: EdgeInsets.all(3),
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//
//                   Row(
//                     children: [
//                       Icon(Icons.call_missed,size: 20,color: Colors.red,),
//                       SizedBox(width: 15,),
//                       Container(
//                         color: Colors.transparent,
//                         child: Text('Missed',style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500,fontSize: Get.width/25),textAlign: TextAlign.center,),
//                       ),
//                     ],
//                   ),
//
//                   Container(
//                     color: Colors.transparent,
//                     child: Text(missed.toString(),style: TextStyle(color: Colors.red, fontWeight: FontWeight.w700,fontSize: Get.width/30),textAlign: TextAlign.center,),
//                   ),
//
//                 ],
//               ),
//             ),
//             Divider(),
//             Container(
//               margin: EdgeInsets.only(bottom: 10),
//               padding: EdgeInsets.all(3),
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//
//                   Row(
//                     children: [
//                       Icon(Icons.contact_page_outlined,size: 20,color: Colors.green,),
//                       SizedBox(width: 15,),
//                       Container(
//                         color: Colors.transparent,
//                         child: Text('Contacts',style: TextStyle(color: Colors.green, fontWeight: FontWeight.w500,fontSize: Get.width/25),textAlign: TextAlign.center,),
//                       ),
//                     ],
//                   ),
//
//                   Container(
//                     color: Colors.transparent,
//                     child: Text(contacts.toString(),style: TextStyle(color: Colors.green, fontWeight: FontWeight.w700,fontSize: Get.width/30),textAlign: TextAlign.center,),
//                   ),
//
//                 ],
//               ),
//             ),
//             Divider(),
//             Container(
//               margin: EdgeInsets.only(bottom: 10),
//               padding: EdgeInsets.all(3),
//               child: Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//
//                   Row(
//                     children: [
//                       Icon(Icons.account_circle,size: 20,color: Colors.black54,),
//                       SizedBox(width: 15,),
//                       Container(
//                         color: Colors.transparent,
//                         child: Text('Unknown',style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500,fontSize: Get.width/25),textAlign: TextAlign.center,),
//                       ),
//                     ],
//                   ),
//
//                   Container(
//                     color: Colors.transparent,
//                     child: Text(unknown.toString(),style: TextStyle(color: Colors.black, fontWeight: FontWeight.w700,fontSize: Get.width/30),textAlign: TextAlign.center,),
//                   ),
//
//                 ],
//               ),
//             ),
//
//             // Container(
//             //   padding: EdgeInsets.symmetric(horizontal: 10),
//             //   child: Row(
//             //     children: [
//             //       // Expanded(
//             //       //   child: Container(
//             //       //       padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//             //       //       margin: EdgeInsets.all(5),
//             //       //       decoration: BoxDecoration(
//             //       //         color: Colors.black.withOpacity(0.03),
//             //       //         border: Border.all(color: Colors.black.withOpacity(0.4),width: 0.6),
//             //       //         borderRadius: BorderRadius.circular(10),
//             //       //       ),
//             //       //       child: Column(
//             //       //         children: [
//             //       //           Text("Incoming ",style: TextStyle(color: Colors.black),),
//             //       //           Text(incoming.toString(),style: TextStyle(color: Colors.black,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//             //       //         ],
//             //       //       )
//             //       //   ),
//             //       // ),
//             //       // Expanded(
//             //       //   child: Container(
//             //       //       padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//             //       //       margin: EdgeInsets.all(5),
//             //       //       decoration: BoxDecoration(
//             //       //         color: Colors.black.withOpacity(0.03),
//             //       //         border: Border.all(color: Colors.black.withOpacity(0.4),width: 0.6),
//             //       //         borderRadius: BorderRadius.circular(10),
//             //       //       ),
//             //       //       child: Column(
//             //       //         children: [
//             //       //           Text("Outgoing",style: TextStyle(color: Colors.black),),
//             //       //           Text(outgoing.toString(),style: TextStyle(color: Colors.black,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//             //       //         ],
//             //       //       )
//             //       //   ),
//             //       // ),
//             //       // Expanded(
//             //       //   child: Container(
//             //       //       padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//             //       //       margin: EdgeInsets.all(5),
//             //       //       decoration: BoxDecoration(
//             //       //         color: Colors.black.withOpacity(0.03),
//             //       //         border: Border.all(color: Colors.black.withOpacity(0.4),width: 0.6),
//             //       //         borderRadius: BorderRadius.circular(10),
//             //       //       ),
//             //       //       child: Column(
//             //       //         children: [
//             //       //           Text("Missed",style: TextStyle(color: Colors.red),),
//             //       //           Text(missed.toString(),style: TextStyle(color: Colors.red,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//             //       //         ],
//             //       //       )
//             //       //   ),
//             //       // ),
//             //     ],
//             //   ),
//             // ),
//             //
//             // Container(
//             //   padding: EdgeInsets.symmetric(horizontal: 10),
//             //   child: Row(
//             //     children: [
//             //       // Expanded(
//             //       //   child: Container(
//             //       //       padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//             //       //       margin: EdgeInsets.all(5),
//             //       //       decoration: BoxDecoration(
//             //       //         color: Colors.black.withOpacity(0.03),
//             //       //         border: Border.all(color: Colors.black.withOpacity(0.4),width: 0.6),
//             //       //         borderRadius: BorderRadius.circular(10),
//             //       //       ),
//             //       //       child: Column(
//             //       //         children: [
//             //       //           Text("Contacts",style: TextStyle(color: Colors.green),),
//             //       //           Text(contacts.toString(),style: TextStyle(color: Colors.green,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//             //       //         ],
//             //       //       )
//             //       //   ),
//             //       // ),
//             //       // Expanded(
//             //       //   child: Container(
//             //       //       padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//             //       //       margin: EdgeInsets.all(5),
//             //       //       decoration: BoxDecoration(
//             //       //         color: Colors.black.withOpacity(0.03),
//             //       //         border: Border.all(color: Colors.black.withOpacity(0.4),width: 0.6),
//             //       //         borderRadius: BorderRadius.circular(10),
//             //       //       ),
//             //       //       child: Column(
//             //       //         children: [
//             //       //           Text("Unknown",style: TextStyle(color: Colors.orange),),
//             //       //           Text(unknown.toString(),style: TextStyle(color: Colors.orange,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//             //       //         ],
//             //       //       )
//             //       //   ),
//             //       // ),
//             //     ],
//             //   ),
//             // ),
//
//           ],
//         )
//     );
//   }
// }