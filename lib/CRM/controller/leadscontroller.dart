import 'package:edutalim/CRM/api/apis.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../LMS/views/InteractivePlayer/utils/utils.dart';

class LeadsController extends GetxController {


  bool isdueleadloading = false;
  Map alldueleadsdata = {};
  List dueleadsdata = [],filt_dueleadsdata = [];
  call_dueleads(loadable) async {
    if(loadable)
    {
      isdueleadloading = true;
    }
    var data = await CRMApiBaseHandler.fet_duelaads_api();
    alldueleadsdata = data['data'];
    dueleadsdata = filt_dueleadsdata =  alldueleadsdata['due_leads'];
    isdueleadloading = false;
    update();
  }



  bool isleadloading = false;
  Map allleadsdata = {};
  List leadsdata = [],filt_leadsdata = [];
  call_leads(loadable,status_id) async {
    if(loadable)
      {
        isleadloading = true;
      }
    var data = await CRMApiBaseHandler.fet_laads_api(status_id);
    allleadsdata = data['data'];
    leadsdata = filt_leadsdata =  allleadsdata['leads'];
    isleadloading = false;
    update();
  }


  void filterleads(value) {
    print("----called----------------------");
    filt_leadsdata = leadsdata
        .where((members) =>
    // like operation and like fields....
    members['name'].toString().toLowerCase().contains(value.toLowerCase()) ||
        members['status_name'].toString().toLowerCase().contains(value.toLowerCase()) ||
        members['phone'].toString().toLowerCase().contains(value.toLowerCase()) ||
        members['remarks'].toString().toLowerCase().contains(value.toLowerCase()) ||
        members['source_name'].toString().toLowerCase().contains(value.toLowerCase()) ||
        members['course_name'].toString().toLowerCase().contains(value.toLowerCase()))
        .toList();
    update();
  }





  // ------------------------------------------------------------------------------------------


  late String member_id, from_id, sourceId, countryId, from;

  List leads = [], alldata = [];
  bool isLoading = true;
  String? sel_status, sel_country, sel_university, sel_source, sel_team, sel_course, sel_telecaller, sel_interest_status;
  final GlobalKey<PopupMenuButtonState<int>> _key = GlobalKey();
  TextEditingController searchfld = TextEditingController();

  get_leads(loadable) async {
    if (loadable) {
      isLoading = true;
      update();
    }

    filt_leadsdata = leads = [
      {
        "id": "3024",
        "title": "Enquiry ",
        "gender": "male",
        "age": "0",
        "code": "91",
        "phone": "919567770898",
        "whatsapp": "9567770898",
        "email": "",
        "qualification": "",
        "country_id": "0",
        "course_id": "",
        "country": "",
        "university_id": "6",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "2025-05-19",
        "lead_source_id": "0",
        "lead_source": "",
        "address": "",
        "telecaller_id": "56",
        "branch_id": "8",
        "telecaller": "",
        "place": "Kuttippala",
        "created_at": "19-05-2025 01:35 PM",
        "updated_at": "19-05-2025 01:35 PM"
      },
      {
        "id": "3022",
        "title": "Enqy",
        "gender": "male",
        "age": "0",
        "code": "91",
        "phone": "917510151527",
        "whatsapp": "7510151527",
        "email": "",
        "qualification": "",
        "country_id": "0",
        "course_id": "",
        "country": "",
        "university_id": "6",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "2025-05-19",
        "lead_source_id": "0",
        "lead_source": "",
        "address": "",
        "telecaller_id": "76",
        "branch_id": "6",
        "telecaller": "",
        "place": "Waynadu ",
        "created_at": "19-05-2025 01:02 PM",
        "updated_at": "19-05-2025 01:02 PM"
      },
      {
        "id": "3021",
        "title": "Enquiry ",
        "gender": "male",
        "age": "0",
        "code": "91",
        "phone": "911830402529",
        "whatsapp": "1830402529",
        "email": "",
        "qualification": "",
        "country_id": "0",
        "course_id": "",
        "country": "",
        "university_id": "6",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "2025-05-19",
        "lead_source_id": "0",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": "Thrissur ",
        "created_at": "19-05-2025 11:13 AM",
        "updated_at": "19-05-2025 11:13 AM"
      },
      {
        "id": "3020",
        "title": "Enqury",
        "gender": "male",
        "age": "0",
        "code": "91",
        "phone": "916235599580",
        "whatsapp": "916235599580",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "0",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "0",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "Pls follow up",
        "followup_date": "2025-05-19",
        "lead_source_id": "30",
        "lead_source": "",
        "address": "",
        "telecaller_id": "76",
        "branch_id": "6",
        "telecaller": "",
        "place": "Wayanadu",
        "created_at": "19-05-2025 10:18 AM",
        "updated_at": "19-05-2025 10:18 AM"
      },
      {
        "id": "3019",
        "title": "Enquiry Form Balusseri",
        "gender": "male",
        "age": "0",
        "code": "91",
        "phone": "918592801935",
        "whatsapp": "8592801935",
        "email": "",
        "qualification": "",
        "country_id": "0",
        "course_id": "",
        "country": "",
        "university_id": "6",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "2025-05-19",
        "lead_source_id": "0",
        "lead_source": "",
        "address": "",
        "telecaller_id": "76",
        "branch_id": "6",
        "telecaller": "",
        "place": "",
        "created_at": "19-05-2025 08:26 AM",
        "updated_at": "19-05-2025 08:26 AM"
      },
      {
        "id": "3004",
        "title": "Enqy ",
        "gender": "male",
        "age": "0",
        "code": "91",
        "phone": "917034539785",
        "whatsapp": "7034539785",
        "email": "",
        "qualification": "",
        "country_id": "0",
        "course_id": "",
        "country": "",
        "university_id": "6",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "2025-05-17",
        "lead_source_id": "30",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": "Chavakkad",
        "created_at": "16-05-2025 07:53 PM",
        "updated_at": "16-05-2025 07:53 PM"
      },
      {
        "id": "2929",
        "title": "DHANUSH CS",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "919846064917",
        "whatsapp": "919846064917",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
      {
        "id": "2928",
        "title": "ASWIN",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "919946840396",
        "whatsapp": "919946840396",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
      {
        "id": "2927",
        "title": "ANANDHU KS",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "918086047595",
        "whatsapp": "918086047595",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
      {
        "id": "2926",
        "title": "AMAL KRISHNA",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "919846392255",
        "whatsapp": "919846392255",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
      {
        "id": "2925",
        "title": "MD AFHAN",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "919745736536",
        "whatsapp": "919745736536",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
      {
        "id": "2924",
        "title": "KARTHIK PM",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "919605706418",
        "whatsapp": "919605706418",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
      {
        "id": "2923",
        "title": "HARISH",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "919961861470",
        "whatsapp": "919961861470",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
      {
        "id": "2922",
        "title": "GOKUL KRISHNA PS",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "918086331379",
        "whatsapp": "918086331379",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
      {
        "id": "2921",
        "title": "SVASANKAR",
        "gender": null,
        "age": null,
        "code": "91",
        "phone": "918086570652",
        "whatsapp": "918086570652",
        "email": "",
        "qualification": "",
        "country_id": "",
        "course_id": "2",
        "country": "",
        "university_id": "",
        "university": "",
        "interest_status": "",
        "lead_status_id": "1",
        "lead_status": "",
        "remarks": "",
        "followup_date": "",
        "lead_source_id": "7",
        "lead_source": "",
        "address": "",
        "telecaller_id": "57",
        "branch_id": "14",
        "telecaller": "",
        "place": null,
        "created_at": "12-05-2025 10:55 AM",
        "updated_at": "12-05-2025 10:55 AM"
      },
    ];
    alldata = [
      {
        "leads": [
          {
            "id": "3024",
            "title": "Enquiry ",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "919567770898",
            "whatsapp": "9567770898",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "course_id": "",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-19",
            "lead_source_id": "0",
            "lead_source": "",
            "address": "",
            "telecaller_id": "56",
            "branch_id": "8",
            "telecaller": "",
            "place": "Kuttippala",
            "created_at": "19-05-2025 01:35 PM",
            "updated_at": "19-05-2025 01:35 PM"
          },
          {
            "id": "3022",
            "title": "Enqy",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "917510151527",
            "whatsapp": "7510151527",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "course_id": "",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-19",
            "lead_source_id": "0",
            "lead_source": "",
            "address": "",
            "telecaller_id": "76",
            "branch_id": "6",
            "telecaller": "",
            "place": "Waynadu ",
            "created_at": "19-05-2025 01:02 PM",
            "updated_at": "19-05-2025 01:02 PM"
          },
          {
            "id": "3021",
            "title": "Enquiry ",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "911830402529",
            "whatsapp": "1830402529",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "course_id": "",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-19",
            "lead_source_id": "0",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": "Thrissur ",
            "created_at": "19-05-2025 11:13 AM",
            "updated_at": "19-05-2025 11:13 AM"
          },
          {
            "id": "3020",
            "title": "Enqury",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "916235599580",
            "whatsapp": "916235599580",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "0",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "0",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "Pls follow up",
            "followup_date": "2025-05-19",
            "lead_source_id": "30",
            "lead_source": "",
            "address": "",
            "telecaller_id": "76",
            "branch_id": "6",
            "telecaller": "",
            "place": "Wayanadu",
            "created_at": "19-05-2025 10:18 AM",
            "updated_at": "19-05-2025 10:18 AM"
          },
          {
            "id": "3019",
            "title": "Enquiry Form Balusseri",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "918592801935",
            "whatsapp": "8592801935",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "course_id": "",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-19",
            "lead_source_id": "0",
            "lead_source": "",
            "address": "",
            "telecaller_id": "76",
            "branch_id": "6",
            "telecaller": "",
            "place": "",
            "created_at": "19-05-2025 08:26 AM",
            "updated_at": "19-05-2025 08:26 AM"
          },
          {
            "id": "3004",
            "title": "Enqy ",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "917034539785",
            "whatsapp": "7034539785",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "course_id": "",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-17",
            "lead_source_id": "30",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": "Chavakkad",
            "created_at": "16-05-2025 07:53 PM",
            "updated_at": "16-05-2025 07:53 PM"
          },
          {
            "id": "2929",
            "title": "DHANUSH CS",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "919846064917",
            "whatsapp": "919846064917",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
          {
            "id": "2928",
            "title": "ASWIN",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "919946840396",
            "whatsapp": "919946840396",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
          {
            "id": "2927",
            "title": "ANANDHU KS",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "918086047595",
            "whatsapp": "918086047595",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
          {
            "id": "2926",
            "title": "AMAL KRISHNA",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "919846392255",
            "whatsapp": "919846392255",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
          {
            "id": "2925",
            "title": "MD AFHAN",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "919745736536",
            "whatsapp": "919745736536",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
          {
            "id": "2924",
            "title": "KARTHIK PM",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "919605706418",
            "whatsapp": "919605706418",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
          {
            "id": "2923",
            "title": "HARISH",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "919961861470",
            "whatsapp": "919961861470",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
          {
            "id": "2922",
            "title": "GOKUL KRISHNA PS",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "918086331379",
            "whatsapp": "918086331379",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
          {
            "id": "2921",
            "title": "SVASANKAR",
            "gender": null,
            "age": null,
            "code": "91",
            "phone": "918086570652",
            "whatsapp": "918086570652",
            "email": "",
            "qualification": "",
            "country_id": "",
            "course_id": "2",
            "country": "",
            "university_id": "",
            "university": "",
            "interest_status": "",
            "lead_status_id": "1",
            "lead_status": "",
            "remarks": "",
            "followup_date": "",
            "lead_source_id": "7",
            "lead_source": "",
            "address": "",
            "telecaller_id": "57",
            "branch_id": "14",
            "telecaller": "",
            "place": null,
            "created_at": "12-05-2025 10:55 AM",
            "updated_at": "12-05-2025 10:55 AM"
          },
        ],
        "country": [
          {"id": "6", "title": "Georgia"},
          {"id": "7", "title": "Uzbekistan"},
          {"id": "8", "title": "Philippines"},
          {"id": "9", "title": "Russia"},
          {"id": "10", "title": "Kazakhstan"},
          {"id": "11", "title": "Kyrgyzstan"},
          {"id": "12", "title": "Moldova"},
          {"id": "13", "title": "Others"}
        ],
        "university": [
          {"id": "6", "title": "Georgian American University GAU"},
          {"id": "7", "title": "Georgian National University SEU"},
          {"id": "8", "title": "University of Georgia UG"},
          {"id": "9", "title": "East European University EEU"},
          {"id": "10", "title": "Brokenshire School of Medicine"},
          {"id": "11", "title": "Others"},
          {"id": "12", "title": "Batumi State University"},
          {"id": "13", "title": "Alte University"},
          {"id": "14", "title": "Akaki State University"},
          {"id": "15", "title": "Caucasus University"},
          {"id": "16", "title": "Tbilisi Medical Academy TMA"},
          {"id": "17", "title": "Tbilisi State Medical University TSMU"},
          {"id": "18", "title": "Tbilisi State University TSU"},
          {"id": "19", "title": "Bukhara State Medical Institute"},
          {"id": "20", "title": "Andijan State Medical Institute"},
          {"id": "21", "title": "Samarkand State Medical Institute"},
          {"id": "22", "title": "Tashkent Medical Academy"},
          {"id": "23", "title": "Fargana State University"},
          {"id": "24", "title": "Kazakh National Medical University"},
          {"id": "25", "title": "Al-Farabi Kazakh National University"},
          {"id": "26", "title": "Caspian University"},
          {"id": "27", "title": "Nicolae Testemitanu State University"}
        ],
        "team": [
          {"id": "5", "title": "Guidance centre of smartphone technology TIR", "team_lead_id": "46"},
          {"id": "6", "title": "Unique institute smartphone technology KSD", "team_lead_id": "45"},
          {"id": "7", "title": "Zwixt institute of advanced smartphone technology KLM", "team_lead_id": "30"},
          {"id": "8", "title": "Empowering gen technologies KKL", "team_lead_id": "66"},
          {"id": "9", "title": "Ibeau technologies KDY", "team_lead_id": "32"},
          {"id": "10", "title": "Research institute of wireless technology MJI", "team_lead_id": "33"},
          {"id": "11", "title": "Frontier wireless care institute NBR", "team_lead_id": "81"},
          {"id": "12", "title": "Cell tell institute of mobile phone technology PMNA", "team_lead_id": "35"},
          {"id": "13", "title": "Britco LLP Deira DXB", "team_lead_id": "36"},
          {"id": "14", "title": "Hyderabad institute of smartphone technology HYD", "team_lead_id": "41"},
          {"id": "15", "title": "Circuit tree smartphone institute GWY", "team_lead_id": null},
          {"id": "16", "title": "IMPT DELHI", "team_lead_id": "53"},
          {"id": "17", "title": "UD-Technical Ho, Kinfra", "team_lead_id": "90"},
          {"id": "18", "title": "Right career institute of technology VTK", "team_lead_id": "48"},
          {"id": "19", "title": "Techfluent smartphone engineering insitute OTP", "team_lead_id": "37"},
          {"id": "20", "title": "Globtech institute of future technology KNR", "team_lead_id": "47"},
          {"id": "21", "title": "Techno savvy institute PYN", "team_lead_id": "39"},
          {"id": "22", "title": "RTC institute of entrepreneurship ALV", "team_lead_id": "82"},
          {"id": "23", "title": "Hotspot institute of wireless comminication CLT", "team_lead_id": "38"},
          {"id": "24", "title": "Intelligent career institute of technology TCR", "team_lead_id": "40"},
          {"id": "26", "title": "Circuit tree smartphone institute GWY", "team_lead_id": "42"}
        ],
        "lead_status": [
          {"id": "1", "title": "Pending"},
          {"id": "2", "title": "Enrolled"},
          {"id": "3", "title": "Follow-up"},
          {"id": "4", "title": "Interested"},
          {"id": "5", "title": "Not-interested"},
          {"id": "7", "title": "Financial problem"}
        ],
        "lead_source": [
          {"id": "1", "title": "Google Lead Ad"},
          {"id": "2", "title": "Facebook Instagram Inquiry"},
          {"id": "3", "title": "Instagram"},
          {"id": "4", "title": "LinkedIn"},
          {"id": "5", "title": "Seminar"},
          {"id": "6", "title": "Reference "},
          {"id": "7", "title": "Data Collection"},
          {"id": "8", "title": "Website inquiry"},
          {"id": "10", "title": "Scholl seminar"},
          {"id": "11", "title": "Public seminar"},
          {"id": "12", "title": "Webinar"},
          {"id": "13", "title": "Service centre"},
          {"id": "14", "title": "Notice "},
          {"id": "15", "title": "Home visiting"},
          {"id": "16", "title": "Kudumbasree"},
          {"id": "17", "title": "Local club"},
          {"id": "18", "title": "Organaisation"},
          {"id": "19", "title": "Exhibition"},
          {"id": "20", "title": "Alumni"},
          {"id": "21", "title": "Public advertisement"},
          {"id": "22", "title": "TV advertisement"},
          {"id": "23", "title": "Data collection"},
          {"id": "24", "title": "News paper ad"},
          {"id": "25", "title": "Hoardings/sun pack "},
          {"id": "26", "title": "Just dial"},
          {"id": "27", "title": "Free class"},
          {"id": "28", "title": "Vlog/reals"},
          {"id": "29", "title": "Industrial visit"},
          {"id": "30", "title": "WhatsAp"},
          {"id": "31", "title": "Tele caller"},
          {"id": "32", "title": "Phone calls"},
          {"id": "33", "title": "SMS"},
          {"id": "34", "title": "Key partner"},
          {"id": "35", "title": "Transfer by BAI"},
          {"id": "36", "title": "Current students"},
          {"id": "37", "title": "Parents"},
          {"id": "38", "title": "Assigned by Ho"},
          {"id": "39", "title": "C/o"},
          {"id": "40", "title": "Walk in"}
        ],
        "courses": [
          {"id": "0", "title": "all"},
          {"id": "1", "title": "Digital Marketing"},
          {"id": "2", "title": "CDT"},
          {"id": "3", "title": "Short term"},
          {"id": "4", "title": "BCST"},
          {"id": "5", "title": "BCHP"},
          {"id": "6", "title": "BSFP"},
          {"id": "7", "title": "BSSP"},
          {"id": "8", "title": "BMAC"},
          {"id": "9", "title": "BAHP"},
          {"id": "10", "title": "BSTC"}
        ],
        "academic_counselor": [
          {"id": "0", "name": "all"},
          {"id": "30", "name": "Rajaprasad"},
          {"id": "31", "name": "Shijas"},
          {"id": "32", "name": "Sudheer Cheruvady"},
          {"id": "33", "name": "Noufal"},
          {"id": "35", "name": "Yasir "},
          {"id": "36", "name": "Rilwan"},
          {"id": "37", "name": "Hamza"},
          {"id": "38", "name": "Sudheer "},
          {"id": "39", "name": "Md Saleem"},
          {"id": "40", "name": "Raju Seabastian"},
          {"id": "41", "name": "Sajid"},
          {"id": "42", "name": "Nasrudhin"},
          {"id": "43", "name": "Unnikrishnan Kinavoor"},
          {"id": "44", "name": "Harshana PK"},
          {"id": "45", "name": "Mishab"},
          {"id": "46", "name": "Abdul Azeez"},
          {"id": "47", "name": "Najeeb P"},
          {"id": "48", "name": "Jeeshma "},
          {"id": "49", "name": "Nimisha Raj"},
          {"id": "51", "name": "Bushra"},
          {"id": "52", "name": "Rishfan"},
          {"id": "53", "name": "Abdulla Kutty VP"},
          {"id": "54", "name": "Shafeera"},
          {"id": "55", "name": "Murali"},
          {"id": "56", "name": "Sanoop M"},
          {"id": "57", "name": "Lijo"},
          {"id": "58", "name": "Majid"},
          {"id": "59", "name": "Faisal"},
          {"id": "60", "name": "Jaseela KA"},
          {"id": "61", "name": "Abitha"},
          {"id": "62", "name": "Abdussamad"},
          {"id": "63", "name": "Ameer Trogon"},
          {"id": "64", "name": "Binzy "},
          {"id": "65", "name": "Sreeja AR"},
          {"id": "66", "name": "Velayudhan"},
          {"id": "67", "name": "Muthu V"},
          {"id": "68", "name": "Fayis"},
          {"id": "74", "name": "Ranjith AK"},
          {"id": "75", "name": "Ashir"},
          {"id": "76", "name": "Nisha"},
          {"id": "77", "name": "Ajumi"},
          {"id": "78", "name": "Neethu"},
          {"id": "79", "name": "Gajath JP"},
          {"id": "80", "name": "Ishan Amrutha Prasad"},
          {"id": "81", "name": "Nishad"},
          {"id": "82", "name": "Rajaprasad"},
          {"id": "87", "name": "trogon"},
          {"id": "90", "name": "Syam Prateesh"},
          {"id": "93", "name": "Ameen "},
          {"id": "101", "name": "Nimisha"},
          {"id": "104", "name": "Britco Dubai"},
          {"id": "116", "name": "Fathima Bushra NE"},
          {"id": "117", "name": "Deepika"},
          {"id": "118", "name": "Sebin Sebastian "},
          {"id": "119", "name": "Ranjitha"},
          {"id": "120", "name": "Shaheena"},
          {"id": "132", "name": "Irshad"},
          {"id": "133", "name": "Muhmmed Sinan"}
        ],
        "branches": [
          {
            "id": "1",
            "title": "HO-KINFRA",
            "is_main": "1",
            "course_id": null,
            "created_at": "2025-01-03 15:42:28",
            "updated_at": "2025-02-14 11:49:15",
            "deleted_at": null,
            "created_by": "1",
            "updated_by": "26",
            "deleted_by": null
          },
          {
            "id": "2",
            "title": "KASARGOD",
            "is_main": "0",
            "course_id": null,
            "created_at": "2025-01-03 15:49:18",
            "updated_at": "2025-02-06 14:56:50",
            "deleted_at": null,
            "created_by": "1",
            "updated_by": "26",
            "deleted_by": null
          },
          {
            "id": "3",
            "title": "PAYYANNUR",
            "is_main": "0",
            "course_id": null,
            "created_at": "2025-01-03 16:36:22",
            "updated_at": "2025-02-06 14:57:02",
            "deleted_at": null,
            "created_by": "1",
            "updated_by": "26",
            "deleted_by": null
          },
          {
            "id": "4",
            "title": "KANNUR",
            "is_main": "0",
            "course_id": null,
            "created_at": "2025-01-11 18:45:42",
            "updated_at": "2025-02-06 14:57:14",
            "deleted_at": null,
            "created_by": "1",
            "updated_by": "26",
            "deleted_by": null
          },
          {
            "id": "5",
            "title": "VATAKARA",
            "is_main": "0",
            "course_id": null,
            "created_at": "2025-01-31 09:48:06",
            "updated_at": "2025-02-06 14:57:26",
            "deleted_at": null,
            "created_by": "1",
            "updated_by": "26",
            "deleted_by": null
          },
          {"id": "6", "title": "CALICUT", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:57:35", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "7", "title": "TIRUR", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:58:16", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "8", "title": "KOTTAKKAL", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:58:25", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "9", "title": "KONDOTTY", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:58:45", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "10", "title": "MANJERI", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:58:54", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "11", "title": "NILAMBUR", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:59:04", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {
            "id": "12",
            "title": "PERINTHALMANNA",
            "is_main": "0",
            "course_id": null,
            "created_at": "2025-02-06 14:59:14",
            "updated_at": null,
            "deleted_at": null,
            "created_by": "26",
            "updated_by": null,
            "deleted_by": null
          },
          {"id": "13", "title": "OTTAPPALAM", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:59:25", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "14", "title": "THRISSUR", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:59:36", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "15", "title": "ALUVA", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:59:44", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "16", "title": "KOLLAM", "is_main": "0", "course_id": null, "created_at": "2025-02-06 14:59:51", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "17", "title": "HYDERABAD", "is_main": "0", "course_id": null, "created_at": "2025-02-06 15:00:02", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {"id": "18", "title": "GUWAHATTY", "is_main": "0", "course_id": null, "created_at": "2025-02-06 15:00:17", "updated_at": null, "deleted_at": null, "created_by": "26", "updated_by": null, "deleted_by": null},
          {
            "id": "19",
            "title": "Delhi",
            "is_main": "0",
            "course_id": null,
            "created_at": "2025-02-06 15:00:30",
            "updated_at": "2025-02-07 15:47:17",
            "deleted_at": null,
            "created_by": "26",
            "updated_by": "26",
            "deleted_by": null
          },
          {
            "id": "20",
            "title": "DUBAI",
            "is_main": "0",
            "course_id": null,
            "created_at": "2025-02-07 14:49:08",
            "updated_at": "2025-02-07 14:49:33",
            "deleted_at": null,
            "created_by": "26",
            "updated_by": "26",
            "deleted_by": null
          }
        ],
        "interest_status": [
          {"id": 1, "title": "Hot"},
          {"id": 2, "title": "Warm"},
          {"id": 3, "title": "Cold"}
        ]
      }
    ];
    isLoading = false;
    update();
  }



  List filtered_branches = [];
  bool isFiltBranchLoading = true;
  String sel_filtered_branches = '';

  get_filtered_branches(branch_id) async {
    print("called------");
    var data = await CRMApiBaseHandler.fet_filtered_branches(branch_id);
    filtered_branches = data['data']['branches'];
    print(filtered_branches);
    isFiltBranchLoading = false;
    update();
  }

  bool istransferLoading = true;

  get_transfer_lead(lead_id, branch_id, from_branch_id) async {
    istransferLoading = true;
    update();
    print("called------");
    var data = await CRMApiBaseHandler.fet_transfer_lead(lead_id, branch_id, from_branch_id);
    if (data['status'].toString() == 'true') {
      toast_success(data['message'].toString());
      get_leads(false);
      Get.back();
    } else {
      toast_error(data['message'].toString());
    }

    print('data-----------------$data');
    istransferLoading = false;
    update();
  }

  List academic_counselor = [];
  bool isAcademicLoading = true;
  String sel_academic_counselor = '', sel_branches = '';

  get_academic_counselor(branch_id) async {
    print("called------");
    var data = await CRMApiBaseHandler.fet_telecaller(branch_id);

    academic_counselor = data['data']['user'];
    print(academic_counselor);
    isAcademicLoading = false;
    update();
  }

  bool isdeleteLoading = true;

  get_delete_lead(lead_id) async {
    isdeleteLoading = true;
    update();

    var data = await CRMApiBaseHandler.fet_delete_lead(lead_id);

    if (data['status'].toString() == 'true') {
      toast_success(data['message'].toString());
      get_leads(false);
      Get.back();
    } else {
      toast_error(data['message'].toString());
    }

    isdeleteLoading = false;
    update();
  }

  bool isconvertLoading = true;

  get_convert_lead(lead_id, branch_id, course_id, batch_id) async {
    isconvertLoading = true;

    var data = await CRMApiBaseHandler.fet_convert_lead(lead_id, branch_id, course_id, batch_id);

    if (data['status'].toString() == 'true') {
      toast_success(data['message'].toString());
      get_leads(true);
      Get.back();
    } else {
      toast_error(data['message'].toString());
    }
    isconvertLoading = false;
    update();
  }

  String? sel_branch, sel_Course, sel_batch;

  List branches = [], courses = [], batches = [];

  bool isBranchLoading = true, isCourseLoading = true, isBatchLoading = true;

  get_courses() async {
    print("called------");
    // var data = await CRMApiBaseHandler.fet_courses();
    // courses = data['data'];
    courses = [
      {
        "id": "1",
        "title": "Digital Marketing",
        "branch_id": null,
        "amount": "1500",
        "created_by": "1",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-01-10 14:58:33",
        "deleted_at": null
      },
      {"id": "2", "title": "CDT", "branch_id": null, "amount": "20000", "created_by": "1", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-01-13 11:44:34", "deleted_at": null},
      {"id": "3", "title": "Short term", "branch_id": null, "amount": null, "created_by": "26", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-02-20 09:45:29", "deleted_at": null},
      {"id": "4", "title": "BCST", "branch_id": null, "amount": null, "created_by": "26", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-03-15 15:55:29", "deleted_at": null},
      {"id": "5", "title": "BCHP", "branch_id": null, "amount": null, "created_by": "26", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-03-15 15:55:38", "deleted_at": null},
      {"id": "6", "title": "BSFP", "branch_id": null, "amount": null, "created_by": "26", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-03-15 15:55:56", "deleted_at": null},
      {"id": "7", "title": "BSSP", "branch_id": null, "amount": null, "created_by": "26", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-03-15 15:56:06", "deleted_at": null},
      {"id": "8", "title": "BMAC", "branch_id": null, "amount": null, "created_by": "26", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-03-15 15:56:17", "deleted_at": null},
      {"id": "9", "title": "BAHP", "branch_id": null, "amount": null, "created_by": "26", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-03-15 15:56:30", "deleted_at": null},
      {"id": "10", "title": "BSTC", "branch_id": null, "amount": null, "created_by": "26", "updated_by": null, "deleted_by": null, "updated_at": null, "created_at": "2025-03-15 15:56:42", "deleted_at": null}
    ];
    isCourseLoading = false;
    update();
  }

  get_batches(branch_id, course_id) async {
    print("called------");
    var data = await CRMApiBaseHandler.fet_batches(branch_id, course_id);
    batches = data['data']['batches'];
    isCourseLoading = false;
    update();
  }

  TextEditingController follow_from_date = TextEditingController();
  TextEditingController follow_to_date = TextEditingController();

  String from_date = "All";

  String to_date = "All";

  DateTime? fromDate;
  DateTime? toDate;
}
