

import 'dart:developer';

import 'package:get/get.dart';

import '../api/main_apis.dart';

class LeaderboardController extends GetxController{

  String exam_id ="";
  bool isLoading = true;
  var leaderboard_data = [];
  void get_leaderboard_data()async{
    isLoading =true;
    // var data = await ApiBasehandler.fet_leaderboard_data_exam(exam_id);
    // if(data["status"].toString()=="true"||data["status"].toString()=="1"){
    //   leaderboard_data = data["data"];
    //   log(leaderboard_data.toString());
      isLoading = false;
      update();
    // }else{
    //   isLoading = false;
    //   update();
    // }
  }




  bool isAllLeaderboardLoading = true;
  var overallLeaderBoard = {
    "daily": [
      {
        "name": "Sreekanth",
        "score": "20",
        "profile_image": "",
        "rank": "1",
      },

      {
        "name": "Abid",
        "score": "19",
        "profile_image": "",
        "rank": "2",
      },
      {
        "name": "Rabil",
        "score": "18",
        "profile_image": "",
        "rank": "3",
      }
    ],
    "weekly": [
      {
        "name": "Sreekanth",
        "score": "20",
        "profile_image": "",
        "rank": "1",
      },

      {
        "name": "Abid",
        "score": "19",
        "profile_image": "",
        "rank": "2",
      },
      {
        "name": "Rabil",
        "score": "18",
        "profile_image": "",
        "rank": "3",
      }
    ],
    "monthly": [
      {
        "name": "Lijosh",
        "score": "20",
        "profile_image": "",
        "rank": "1",
      },

      {
        "name": "Rabil",
        "score": "19",
        "profile_image": "",
        "rank": "2",
      },
      {
        "name": "Abid",
        "score": "18",
        "profile_image": "",
        "rank": "3",
      }
    ],
  };

  void get_all_leaderboard_data()async{

    isAllLeaderboardLoading = true;

    // var data = await ApiBasehandler.fet_leaderboard_data();
    // overallLeaderBoard = data["data"];

    isAllLeaderboardLoading = false;
    update();


  }


}