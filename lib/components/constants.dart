import 'package:flutter/material.dart';

// var baseurl = 'https://edutalim.trogon.info/';
var baseurl = 'https://project.trogon.info/edutalim_beta/';
var api = '${baseurl}api/v1/';
var api2 = '${baseurl}api/';


const appName = 'EDUTALIM';
const appfullName = 'EDUTALIM';
const share_description = 'Let me recommend you $appName Application. \n\nDownload Now 👇\n\n📱 iOS : $iosLink \n\n📱 Android : $androidLink \n\n';

const ios_version = '3.0.7';
const android_version = '3.1.9';  // Commented The Call Log & Lead Calll history Pages
const razorpay_color = '1D373A';
const defaultPadding = 16.0;
const commontextColor = Color(0xE4000000);

Color primaryColor = Color(0xff1A1F1C);
Color primarylightColor = Color(0xff1D373A);
Color secondaryColor = Color(0xffBC8017);
Color secondarylightColor = Color(0xffEDCA3E);
Color textwhiteColor = Color(0xffffffff);
Color textblackColor = Color(0xff000000);
Color appbarblackColor = Color(0xff00000000);
Color appbarwhiteColor = Color(0xff00ffffff);
Color chatbgColor = Color(0xffc4bdfc);
Color bgColor = Color(0xffF9F4E9);
Color onboardBgColor = Color(0xffBED8DA);

const privacy_policy_url = 'https://www.edutalim.com/privacy/';
const iosLink = 'https://apps.apple.com/us/app/edutalim/id6474233548';
const androidLink = 'https://play.google.com/store/apps/details?id=org.edutalim.mobile';
const logoUrl = '';

var font_medium = 'plusjakartasans_medium';
var font_semibold = 'plusjakartasans_semibold';
var font_regular = 'plusjakartasans_regular';
var font_bold = 'plusjakartasans_bold';


