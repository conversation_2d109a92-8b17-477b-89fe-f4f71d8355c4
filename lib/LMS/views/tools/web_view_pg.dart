import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import 'package:webview_flutter/webview_flutter.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';

class webview_page extends StatefulWidget {
  final String url;

  webview_page({required this.url});

  @override
  webview_pageState createState() => webview_pageState();
}


// Using WidgetsBindingObserver to if the user detached  from the screen or not to Use Exam

class webview_pageState extends State<webview_page> // with WidgetsBindingObserver
{
  bool isEliminated = false;

  // void _eliminateUser() {
  //   if (!isEliminated) {
  //     setState(() {
  //       isEliminated = true;
  //     });
  //
  //     Future.delayed(Duration(milliseconds: 500), () {
  //       showDialog(
  //         context: context,
  //         barrierDismissible: false,
  //         builder: (context) {
  //           return AlertDialog(
  //             title: Text("Eliminated"),
  //             content: Text("You are eliminated from the exam due to inappropriate actions."),
  //             actions: [
  //               ElevatedButton(
  //                 onPressed: () {
  //                   Navigator.of(context).pop();
  //                   Navigator.of(context).maybePop(); // Exit WebView or Exam Page
  //                 },
  //                 child: Text("OK"),
  //               ),
  //             ],
  //           );
  //         },
  //       );
  //     });
  //   }
  // }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   if (state == AppLifecycleState.paused ||
  //       state == AppLifecycleState.inactive ||
  //       state == AppLifecycleState.detached) {
  //     _eliminateUser();
  //   }
  // }

  late WebViewController webViewController;

  bool isLoading = true;
  String? user_id, progress = "0";

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addObserver(this);
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0xffffffff))
      ..addJavaScriptChannel('Flutter', onMessageReceived: (aa) {
        show_confirm(context);
      })
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progresss) {
            setState(() {
              progress = progresss.toString();
            });
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {
            setState(() {
              isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith('tested')) {
              toast_info('Web Page blocked');
              Get.back();
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url.toString()), method: LoadRequestMethod.get);
  }

  @override
  void dispose() async {
    // WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        return show_confirm(context);
      },
      child: Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            iconTheme: IconThemeData(color: Colors.black),
            leading: IconButton(
              onPressed: () {
                show_confirm(context);
              },
              icon: Icon(Icons.arrow_back_ios, color: Colors.black54),
            ),
          ),
          body: Stack(
            children: [
              Container(
                color: Colors.white,
                child: Stack(
                  children: [
                    WebViewWidget(controller: webViewController),
                    progress != "100" ? loader() : Stack(),
                  ],
                ),
              ),
            ],
          )),
    );
  }
}

show_confirm(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(30))),
        content: Wrap(
          children: [
            SizedBox(
              width: Get.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("Head's Up!", style: TextStyle(fontFamily: 'poppins_bold', fontSize: 23)),
                  Text("Are you sure to exit?", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black54, fontSize: 13)),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            child: Text("NO", style: TextStyle(color: Colors.black)),
            onPressed: () {
              Get.back();
            },
          ),
          TextButton(
            child: Text("YES", style: TextStyle(color: Colors.black)),
            onPressed: () {
              Get.back();
              Get.back();
            },
          ),
        ],
      );
    },
  );
}
