name: edutalim
description: "A new Flutter project."

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  get_storage: ^2.1.1
  get: ^4.7.2
  device_info_plus: ^9.1.0
  url_launcher: ^6.3.1
  fluttertoast: ^8.2.12
  lottie: ^3.3.1
  intl: ^0.19.0
  story:
  flutter_svg: ^2.0.17
  otp_autofill: ^4.1.0
  intl_phone_field: ^3.2.0
  carousel_slider: ^5.0.0
  video_player: ^2.9.3
  card_swiper: ^3.0.1
  dio: ^5.8.0+1
  share_plus: ^10.1.1
  dotted_line: ^3.2.3
  flutter_html:
  step_progress_indicator: ^1.0.2
  cached_network_image: ^3.3.1
  percent_indicator: ^4.2.5
  speech_to_text: ^7.0.0
  flutter_tts: ^4.2.2
  webview_flutter: ^4.11.0
  flutter_cached_pdfview: ^0.4.1
#  razorpay_flutter: ^1.4.0
  permission_handler: ^11.0.1
  dotted_border: ^2.1.0
  file_picker: ^8.1.2
  flutter_rating_bar: ^4.0.1
  country_list_pick: ^1.0.0+3
#  call_log: ^5.1.0
  better_player_plus: ^1.0.8
  wakelock_plus: ^1.2.10
  zoom_allinonesdk: ^0.0.5
#  flutter_zoom_meeting: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.1


flutter_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path_ios: "assets/logo/logo_ios.png"
  image_path: "assets/logo/logo_.png"

flutter:
  uses-material-design: true
  assets:
    - assets/logo/
    - assets/onboard/
    - assets/bg/
    - assets/icons/
    - assets/images/
    - assets/temp_images/
    - assets/lotties/
    - lib/LMS/views/InteractivePlayer/assets/
    - assets/gif/
#    - assets/chaticons/

  fonts:
    - family: plusjakartasans_bold
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Bold.ttf
    - family: plusjakartasans_medium
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Medium.ttf
    - family: plusjakartasans_mediumitalic
      fonts:
        - asset: assets/fonts/PlusJakartaSans-MediumItalic.ttf
    - family: plusjakartasans_regular
      fonts:
        - asset: assets/fonts/PlusJakartaSans-Regular.ttf
    - family: plusjakartasans_semibold
      fonts:
        - asset: assets/fonts/PlusJakartaSans-SemiBold.ttf
