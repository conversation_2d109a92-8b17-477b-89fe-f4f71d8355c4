import 'dart:math';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../components/constants.dart';

class StorageInfoCard extends StatelessWidget {
  const StorageInfoCard({
    Key? key,
    required this.title,
    required this.svgSrc,
    required this.amountOfFiles,
    required this.numOfFiles,
  }) : super(key: key);

  final String title, svgSrc, amountOfFiles;
  final int numOfFiles;

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(top: defaultPadding),
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(width: 2, color: primaryColor.withValues(alpha: 0.15)),
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
      ),
      child: Row(
        children: [


          Expanded(
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

                  Row(
                    children: [

                      SizedBox(height: 20, width: 20, child: SvgPicture.asset(svgSrc),),

                      SizedBox(width: 10,),

                      Text("$numOfFiles",
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall!
                            .copyWith(color: commontextColor),
                      ),

                    ],
                  ),

                  SizedBox(height: 5,),

                  Text(
                    title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: commontextColor,fontSize: title.toString().length > 22 ? 11 : title.toString().length > 18 ? 13 : 14),
                  ),

                ],
              ),
            ),
          ),

          // Text(amountOfFiles,style: TextStyle(color: commontextColor),)

        ],
      ),
    );
  }
}


class home_card1 extends StatelessWidget {
  const home_card1({
    Key? key,
    required this.title,
    required this.amountOfFiles,
    required this.numOfFiles,
  }) : super(key: key);

  final String title, amountOfFiles,numOfFiles;

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(top: defaultPadding),
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(width: 1, color: primaryColor.withValues(alpha: 0.1)),
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
      ),
      child: Row(
        children: [


          Expanded(
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [

                 Container(
                   child: Text(
                     numOfFiles.toString(),
                     maxLines: 2,
                     overflow: TextOverflow.ellipsis,
                     style: TextStyle(color: Colors.black,fontSize: 23),
                   ),
                 ),

                  SizedBox(height: 5,),

                  Text(
                    title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: commontextColor,fontSize: title.toString().length > 22 ? 11 : title.toString().length > 18 ? 13 : 15),
                  ),

                ],
              ),
            ),
          ),

          // Text(amountOfFiles,style: TextStyle(color: commontextColor),)

        ],
      ),
    );
  }
}


class home_card2 extends StatelessWidget {
  const home_card2({
    Key? key,
    required this.title,
    required this.amountOfFiles,
    required this.numOfFiles,
  }) : super(key: key);

  final String title, amountOfFiles,numOfFiles;

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(top: defaultPadding),
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: primaryColor,
        border: Border.all(width: 3, color: Colors.white,),
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
      ),
      child: Row(
        children: [


          Expanded(
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [

                  Container(
                    child: Text(
                      numOfFiles.toString(),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(color: Colors.white,fontSize: 23),
                    ),
                  ),

                  SizedBox(height: 5,),

                  Text(
                    title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: Colors.white,fontSize: title.toString().length > 22 ? 11 : title.toString().length > 18 ? 12 : 13.5),
                  ),

                ],
              ),
            ),
          ),

          // Text(amountOfFiles,style: TextStyle(color: commontextColor),)

        ],
      ),
    );
  }
}


class home_card3 extends StatelessWidget {
  const home_card3({
    Key? key,
    required this.title,
    required this.amountOfFiles,
    required this.numOfFiles,
  }) : super(key: key);

  final String title, amountOfFiles,numOfFiles;

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(top: defaultPadding),
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: primaryColor,
        border: Border.all(width: 3, color: Colors.white,),
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
      ),
      child: Row(
        children: [


          Expanded(
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [

                 Row(
                   children: [
                     Container(
                       child: Text(
                         numOfFiles.toString(),
                         maxLines: 2,
                         overflow: TextOverflow.ellipsis,
                         style: TextStyle(color: Colors.white,fontSize: 20),
                       ),
                     ),
                     SizedBox(width: 20,),
                     Text(
                       title,
                       maxLines: 2,
                       overflow: TextOverflow.ellipsis,
                       style: TextStyle(color: Colors.white,fontSize: title.toString().length > 22 ? 11 : title.toString().length > 18 ? 12 : 13.5),
                     ),
                   ],
                 ),
                 Icon(Icons.keyboard_arrow_right_outlined,color: Colors.white,),

                ],
              ),
            ),
          ),

          // Text(amountOfFiles,style: TextStyle(color: commontextColor),)

        ],
      ),
    );
  }
}


class home_card_image extends StatelessWidget {
  const home_card_image({
    Key? key,
    required this.title,
    required this.image,
  }) : super(key: key);

  final String title, image;

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(top: defaultPadding),
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(width: 1, color: primaryColor.withValues(alpha: 0.1)),
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
      ),
      child: Row(
        children: [


          Expanded(
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [

                  Container(
                    width: Get.width/6,
                    child: Image.asset(image.toString()),
                  ),

                  SizedBox(height: 20,),

                  Text(
                    title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: commontextColor,fontSize: title.toString().length > 22 ? 11 : title.toString().length > 18 ? 13 : 15),
                  ),

                ],
              ),
            ),
          ),

          // Text(amountOfFiles,style: TextStyle(color: commontextColor),)

        ],
      ),
    );
  }
}
