import 'package:edutalim/LMS/views/ai_mentor/ai_chat_screen.dart';
import 'package:edutalim/LMS/views/dashboard/home/<USER>';
import 'package:edutalim/LMS/views/dashboard/my_course/assignmetnts/assignments_list.dart';
import 'package:edutalim/LMS/views/dashboard/my_course/change_course.dart';
import 'package:edutalim/LMS/views/dashboard/my_course/lessons.dart';
import 'package:edutalim/LMS/views/dashboard/my_course/materials.dart';
import 'package:edutalim/LMS/views/dashboard/my_course/practice.dart';
import 'package:edutalim/LMS/views/dashboard/my_course/practices.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/widgets.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';
import 'package:story/story.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/mycoursecontroller.dart';
import '../dashboard.dart';
import 'exams.dart';
import 'liveclasses.dart';

class my_course extends StatefulWidget {
  const my_course({super.key});

  @override
  State<my_course> createState() => _my_courseState();
}

class _my_courseState extends State<my_course> {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());

  // Initial position of the floating widget
  double posX = 20;
  double posY = 10;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    controller.get_mycourse();
  }

  @override
  void dispose() {
    // controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Mycoursecontroller>(builder: (controller) {
      return Scaffold(
        backgroundColor: bgColor,
        appBar: AppBar(
          backgroundColor: appbarwhiteColor,
          surfaceTintColor: appbarwhiteColor,
          title: Container(
            padding: EdgeInsets.symmetric(horizontal: 15),
            child: Text(
              "My Course",
              style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 22, color: textblackColor),
            ),
          ),
          centerTitle: true,
          automaticallyImplyLeading: false,
        ),
        body: Container(
            height: Get.height,
            width: Get.width,
            color: bgColor,
            child: controller.isMyCourseLoading
                ? loader()
                : controller.myCourseData[0]['course_details'].isEmpty
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "Good ${greeting()} ${controller.myCourseData[0]['user']['name'] ?? '--'} !",
                            style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 18),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Image.asset('assets/icons/not_enrolled.png', width: Get.width / 4),
                          SizedBox(height: 10),
                          Container(child: Text("You have not enrolled in any course yet!")),
                          SizedBox(height: 40),
                          SizedBox(
                            width: Get.width / 2.5,
                            child: common_button_gradient_primary(
                                onPressed: () {
                                  Get.offAll(dashboard());
                                  GetStorage().write('home_index', '0');
                                },
                                borderRadius: BorderRadius.circular(12),
                                child: Text("Enroll Now", style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontSize: Get.width / 25))),
                          ),
                        ],
                      )
                    : Stack(
                        children: [
                          SingleChildScrollView(
                            physics: ScrollPhysics(),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 30,
                                ),
                                Row(
                                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                                          child: Text(
                                            "Good ${greeting()} ",
                                            style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 24),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 5,
                                        ),
                                        Container(
                                          width: Get.width * .8,
                                          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                                          child: Text(
                                            "${controller.myCourseData[0]['user']['name'] ?? '--'} ",
                                            style: TextStyle(color: textblackColor.withOpacity(0.5), fontWeight: FontWeight.w600, fontSize: Get.width / 24),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      width: 10,
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        // Get.to(AccountPage());
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(1000),
                                        child: Container(
                                          width: Get.width / 9,
                                          height: Get.width / 9,
                                          child: Image.network(
                                            controller.myCourseData[0]['user']['profile_image'].toString(),
                                            fit: BoxFit.cover,
                                            errorBuilder: (a, b, c) {
                                              return Image.asset(
                                                'assets/images/avatar_placeholder.png',
                                                fit: BoxFit.cover,
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 20,
                                ),
                                Container(
                                  decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(32)), color: textwhiteColor),
                                  child: Column(
                                    children: [
                                      SizedBox(
                                        height: 30,
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 13),
                                        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                                        decoration: BoxDecoration(image: DecorationImage(image: AssetImage('assets/bg/mycourse.png'), fit: BoxFit.fill)),
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                        width: Get.width * .6,
                                                        // padding: EdgeInsets.symmetric(horizontal: 15,vertical: 10),
                                                        // color: Colors.yellow,
                                                        child: Text(
                                                          'Enrolled Course',
                                                          style: TextStyle(fontWeight: FontWeight.w400, color: textwhiteColor, fontSize: Get.width / 30),
                                                        )),
                                                    SizedBox(
                                                      height: 2,
                                                    ),
                                                    controller.myCourseData[0]['course_details']['course_title'].toString() == ''
                                                        ? Container(
                                                            width: Get.width * .6,
                                                            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                                            // color: Colors.yellow,
                                                            child: Text(
                                                              'No Course Enrolled',
                                                              style: TextStyle(fontWeight: FontWeight.w700, color: textwhiteColor, fontSize: Get.width / 25),
                                                            ))
                                                        : Container(
                                                            width: Get.width * .55,
                                                            // color: Colors.blue,
                                                            child: Text(
                                                              controller.myCourseData[0]['course_details']['course_title'].toString(),
                                                              textAlign: TextAlign.start,
                                                              style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 26, color: textwhiteColor),
                                                            ),
                                                          ),
                                                  ],
                                                ),
                                                Column(
                                                  mainAxisAlignment: MainAxisAlignment.end,
                                                  children: [
                                                    controller.myCourseData[0]['course_details']['course_title'].toString() == ''
                                                        ? GestureDetector(
                                                            onTap: () {
                                                              // Get.to(()=> switch_course());
                                                              Get.offAll(dashboard());
                                                              GetStorage().write('home_index', '0');
                                                              print('data');
                                                              print('${GetStorage().read('home_index')}');
                                                            },
                                                            child: Container(
                                                              decoration: BoxDecoration(
                                                                // borderRadius: BorderRadius.circular(10),
                                                                borderRadius: BorderRadius.only(topRight: Radius.circular(5), bottomRight: Radius.circular(5)),
                                                                color: Color(0xffF3CA40),
                                                              ),
                                                              width: Get.width / 3.5,
                                                              padding: EdgeInsets.symmetric(vertical: 12),
                                                              child: Row(
                                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                children: [
                                                                  Text(
                                                                    "Enroll Now",
                                                                    style: TextStyle(color: textblackColor, fontSize: Get.width / 28, fontWeight: FontWeight.w600),
                                                                  ),
                                                                  // SizedBox(width: 7,),
                                                                  // Icon(Icons.compare_arrows,color: textwhiteColor,size: 20,),
                                                                ],
                                                              ),
                                                            ),
                                                          )
                                                        : GestureDetector(
                                                            onTap: () {
                                                              Get.to(() => change_course());
                                                            },
                                                            child: Container(
                                                              height: 60,
                                                              width: 100,
                                                              decoration: BoxDecoration(image: DecorationImage(image: AssetImage('assets/images/change.png'))),
                                                            ),
                                                          ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                SizedBox(
                                                  width: MediaQuery.of(context).size.width * 0.7, // Constrain width
                                                  child: StepProgressIndicator(
                                                    totalSteps: 100,
                                                    currentStep: int.parse(controller.myCourseData[0]['course_details']['course_progress'].toString()),
                                                    size: 8,
                                                    padding: 0,
                                                    selectedColor: Color(0xffF1C27D),
                                                    unselectedColor: Color(0xffeeeeee),
                                                    roundedEdges: Radius.circular(10),
                                                  ),
                                                ),
                                                Expanded(
                                                  // width: Get.width*.55,
                                                  child: Text(
                                                    '${controller.myCourseData[0]['course_details']['course_progress'].toString()}%',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 26, color: textwhiteColor),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 30,
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                          children: [
                                            GestureDetector(
                                              onTap: () {
                                                Get.to(() => live_class(course_id: controller.myCourseData[0]['course_details']['course_id'].toString(),));
                                              },
                                              child: Column(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius: BorderRadius.all(Radius.circular(5)),
                                                    child: Image.asset('assets/icons/live_class.png', width: Get.width / 7.0),
                                                  ),
                                                  SizedBox(height: 10),
                                                  Text('Live Class', style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 35), maxLines: 1),
                                                ],
                                              ),
                                            ),
                                            // GestureDetector(
                                            //   onTap: () {
                                            //     Get.to(() => materials(course_id: controller.myCourseData[0]['course_details']['course_id'].toString(),section_id: '',lesson_id: '',));
                                            //   },
                                            //   child: Column(
                                            //     children: [
                                            //       ClipRRect(
                                            //         borderRadius: BorderRadius.all(Radius.circular(5)),
                                            //         child: Image.asset('assets/icons/materials.png', width: Get.width / 7.0),
                                            //       ),
                                            //       SizedBox(height: 10),
                                            //       Text('Materials', style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 35), maxLines: 1),
                                            //     ],
                                            //   ),
                                            // ),
                                            GestureDetector(
                                              onTap: () {
                                                Get.to(() => ExamScreen(course_id: controller.myCourseData[0]['course_details']['course_id'].toString()));
                                              },
                                              child: Column(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius: BorderRadius.all(Radius.circular(5)),
                                                    child: Image.asset('assets/icons/practice.png', width: Get.width / 7.0),
                                                  ),
                                                  SizedBox(height: 10),
                                                  Text('Exam', style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 35), maxLines: 1),
                                                ],
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                Get.to(() => PracticeScreen(course_id: controller.myCourseData[0]['course_details']['course_id'].toString()));
                                              },
                                              child: Column(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius: BorderRadius.all(Radius.circular(5)),
                                                    child: Image.asset('assets/icons/exam.png', width: Get.width / 7.0),
                                                  ),
                                                  SizedBox(height: 10),
                                                  Text('Practice', style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 35), maxLines: 1),
                                                ],
                                              ),
                                            ),

                                            GestureDetector(
                                              onTap: () {
                                                Get.to(() =>  assignment_list(
                                                    course_id: controller.myCourseData[0]['course_details']['course_id'].toString()
                                                ));
                                              },
                                              child: Column(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius: BorderRadius.all(Radius.circular(5)),
                                                    child: Image.asset('assets/images/assignment.png', width: Get.width / 7),
                                                  ),
                                                  SizedBox(height: 10),
                                                  Text('Assignments', style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 35), maxLines: 1),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 30,
                                      ),
                                      if (controller.myCourseData[0]['course_details']['sections'].isNotEmpty)
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              padding: EdgeInsets.symmetric(horizontal: 15),
                                              child: Text(
                                                "Subjects",
                                                style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 22, color: textblackColor),
                                              ),
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            SizedBox(
                                              width: Get.width,
                                              child: ListView.builder(
                                                physics: ScrollPhysics(),
                                                padding: EdgeInsets.all(0),
                                                shrinkWrap: true,
                                                itemCount: controller.myCourseData[0]['course_details']['sections'].length,
                                                itemBuilder: (BuildContext context, int index) {
                                                  Map data = controller.myCourseData[0]['course_details']['sections'][index];
                                                  return GestureDetector(
                                                    onTap: () async {
                                                      if (data['premium_lock'].toString() == "1") {
                                                        Get.to(() => course_details(course_id: controller.myCourseData[0]['course_details']['course_id'].toString()));
                                                      } else if (data['sequence_lock'].toString() == "1") {
                                                        toast_info(data['sequence_lock_message'].toString());
                                                      } else {
                                                        Get.to(lessons(subject_id: data['id'].toString(), sub_title: data['title'].toString()));
                                                      }
                                                    },
                                                    child: Container(
                                                      margin: EdgeInsets.only(left: index == 0 ? 5 : 5, right: index == 2 ? 15 : 5, bottom: 10),
                                                      child: Column(
                                                        children: [
                                                          Stack(
                                                            children: [
                                                              Container(
                                                                decoration: BoxDecoration(
                                                                  color: textwhiteColor,
                                                                  border: Border.all(color: textblackColor.withOpacity(0.1), width: 1),
                                                                  borderRadius: BorderRadius.all(Radius.circular(12)),
                                                                ),
                                                                padding: EdgeInsets.all(10),
                                                                width: Get.width / 1.1,
                                                                child: Container(
                                                                  padding: EdgeInsets.only(left: 0),
                                                                  child: Column(
                                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                    children: [
                                                                      Row(
                                                                        children: [
                                                                          Container(
                                                                            color: textwhiteColor,
                                                                            child: ClipRRect(
                                                                              borderRadius: BorderRadius.all(Radius.circular(5)),
                                                                              child: Image.network(
                                                                                data['thumbnail'].toString(),
                                                                                width: Get.width / 6,
                                                                                errorBuilder: (a, b, c) {
                                                                                  return Image.asset(
                                                                                    'assets/temp_images/subject1.png',
                                                                                    width: Get.width / 6,
                                                                                  );
                                                                                },
                                                                              ),
                                                                            ),
                                                                          ),
                                                                          SizedBox(
                                                                            width: 12,
                                                                          ),
                                                                          Column(
                                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                                            children: [
                                                                              SizedBox(
                                                                                width: Get.width * .6,
                                                                                child: Text(
                                                                                  data['title'].toString(),
                                                                                  style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 27),
                                                                                  maxLines: 1,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                height: 3,
                                                                              ),
                                                                              SizedBox(
                                                                                width: Get.width * .6,
                                                                                child: Text(
                                                                                  '${data['lessons_count'].toString()} Lessons',
                                                                                  style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w400, fontSize: Get.width / 27),
                                                                                  maxLines: 2,
                                                                                ),
                                                                              ),
                                                                              SizedBox(
                                                                                height: 10,
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        ],
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                              Positioned(
                                                                  right: 15,
                                                                  bottom: 15,
                                                                  child: Icon(
                                                                    data['premium_lock'].toString() == "1" || data['sequence_lock'].toString() == "1" ? Icons.lock : CupertinoIcons.right_chevron,
                                                                    color: textblackColor.withValues(alpha: .4),
                                                                    size: 20,
                                                                  )
                                                                  // child: Image.asset('assets/images/ar_right.png',width: Get.width*.06,)
                                                                  )
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          AnimatedPositioned(
                              duration: Duration(milliseconds: 200),
                              curve: Curves.easeOut,
                              right: posX,
                              bottom: posY,
                              child: GestureDetector(
                                onPanUpdate: (details) {
                                  setState(() {
                                    posX -= details.delta.dx;
                                    posY -= details.delta.dy;
                                  });
                                },
                                onTap: () {
                                  Get.to(() => ai_chat_screen(), transition: Transition.downToUp);
                                },
                                child: Container(
                                    decoration: BoxDecoration(
                                        color: Colors.transparent, borderRadius: BorderRadius.circular(1000), boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.2), blurRadius: 5, offset: Offset(0, 0))]),
                                    child: Image.asset(
                                      "assets/images/ai_bot.png",
                                      width: Get.width / 6,
                                    )),
                              ))
                        ],
                      )),
      );
    });
  }
}
