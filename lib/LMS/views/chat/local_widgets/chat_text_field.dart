import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatTextField extends StatelessWidget {
  ChatTextField(
      {super.key,
      required this.controller,
      required this.onTap,
      required this.onChanged});
  void Function(String)? onChanged;
  void Function()? onTap;
  TextEditingController? controller;
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: TextField(
        onTap: onTap,
        onChanged: onChanged,
        maxLines: null,
        keyboardType: TextInputType.text,
        controller: controller,
        style: TextStyle(fontSize: Get.width / 28, color: Colors.black),
        decoration: InputDecoration(
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent, width: 1),
            borderRadius: BorderRadius.circular(10.0),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent, width: 1),
            borderRadius: BorderRadius.circular(10.0),
          ),
          filled: true,
          hintStyle: TextStyle(
              // fontFamily: font_regular,
              color: Colors.black38,
              fontSize: Get.width / 28),
          hintText: "Type your message here",
          fillColor: Colors.transparent,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20),
        ),
      ),
    );
  }
}
