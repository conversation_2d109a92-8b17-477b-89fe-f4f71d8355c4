import 'package:edutalim/CRM/api/apis.dart';
import 'package:edutalim/CRM/controller/leadscontroller.dart';
import 'package:edutalim/CRM/views/crm_dashboard/leads/lead_add.dart';
import 'package:edutalim/CRM/views/crm_dashboard/leads/lead_history.dart';
import 'package:edutalim/CRM/views/crm_dashboard/leads/leads_details.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../../../../components/constants.dart';
import '../../../../components/utils.dart';

class due_leads_list extends StatefulWidget {

  @override
  State<due_leads_list> createState() => _leads_listState();
}

class _leads_listState extends State<due_leads_list> {
  LeadsController controller = Get.put(LeadsController());

  @override
  void initState() {
    controller.call_dueleads(true);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LeadsController>(builder: (controller) {
      return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: true,
          iconTheme: IconThemeData(color: Colors.black),
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          title: Text(
            "Due Leads (${controller.dueleadsdata.length.toString()})",
            style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16),
          ),
          actions: [
            IconButton(
                onPressed: () {
                  Get.to(lead_add(action: 'add', data: [], member_id: GetStorage().read('user_id').toString()))!.then((value) {
                    controller.get_leads(false);
                  });
                },
                icon: Icon(Icons.add)),
            SizedBox(width: 10),
          ],
        ),
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Container(
            child: Column(
              children: [
                Container(
                  color: Colors.white,
                  padding: EdgeInsets.only(bottom: 15, left: 10, right: 10, top: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width / 1.5,
                        child: TextField(
                          onTap: () {},
                          onChanged: (value) {
                            if (value == "") {
                              setState(() {
                                controller.filt_dueleadsdata = controller.dueleadsdata;
                                controller.update();
                              });
                            } else {
                              controller.filterleads(value);
                            }
                          },
                          readOnly: false,
                          style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                          controller: controller.searchfld,
                          decoration: InputDecoration(
                            focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.black26, width: 0.5), borderRadius: BorderRadius.circular(10.0)),
                            enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.black26, width: 0.5), borderRadius: BorderRadius.circular(10.0)),
                            filled: true,
                            hintStyle: TextStyle(color: const Color(0xFFA1A3AB), fontFamily: "poppins_regular"),
                            hintText: "Search",
                            fillColor: Colors.white70,
                            contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                            suffixIcon: GestureDetector(
                              onTap: () {
                                if (controller.searchfld.text.isNotEmpty) {
                                  setState(() {
                                    controller.searchfld.text = '';
                                    controller.filt_dueleadsdata = controller.dueleadsdata;
                                    controller.update();
                                  });
                                }
                              },
                              child: Container(
                                color: Colors.transparent,
                                padding: EdgeInsets.all(13),
                                child: controller.searchfld.text.isEmpty ? Image.asset('assets/icons/search2.png', width: 2, color: Colors.black38) : Icon(Icons.close),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (controller.isdueleadloading != true) {
                              filter_bottom_sheet();
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(color: primaryColor, borderRadius: BorderRadius.circular(10)),
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.filter_list_rounded, size: Get.width / 15, color: Colors.white),
                                SizedBox(width: 6),
                                Text("Filter", style: TextStyle(color: Colors.white)),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(
                  height: 5,
                ),
                controller.searchfld.text.isEmpty || controller.from_date.toString() != 'All' || controller.to_date.toString() != 'All'
                    ? Row(
                        children: [
                          controller.from_date.toString() == 'All'
                              ? Container()
                              : Expanded(
                                  child: Container(
                                    color: Colors.transparent,
                                    child: Container(
                                        padding: EdgeInsets.only(top: 10, bottom: 10, left: 10),
                                        margin: EdgeInsets.all(5),
                                        color: Colors.white,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              children: [
                                                Text("From Date", style: TextStyle(color: Colors.black)),
                                                Text("${controller.from_date} ", style: TextStyle(color: Colors.black)),
                                              ],
                                            ),
                                            IconButton(
                                              padding: EdgeInsets.all(0),
                                              alignment: Alignment.center,
                                              onPressed: () {
                                                controller.from_date = 'All';
                                                controller.get_leads(false);
                                                controller.update();
                                              },
                                              icon: Icon(Icons.close),
                                            )
                                          ],
                                        )),
                                  ),
                                ),
                          controller.to_date.toString() == 'All'
                              ? Container()
                              : Expanded(
                                  child: Container(
                                    color: Colors.transparent,
                                    child: Container(
                                        padding: EdgeInsets.only(top: 10, bottom: 10, left: 10),
                                        margin: EdgeInsets.all(5),
                                        color: Colors.white,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text("To Date", style: TextStyle(color: Colors.black54)),
                                                SizedBox(height: 5),
                                                Text("${controller.to_date} ", style: TextStyle(color: Colors.black)),
                                              ],
                                            ),
                                            IconButton(
                                              padding: EdgeInsets.all(0),
                                              alignment: Alignment.center,
                                              onPressed: () {
                                                controller.to_date = 'All';
                                                controller.get_leads(false);
                                                controller.update();
                                              },
                                              icon: Icon(Icons.close),
                                            )
                                          ],
                                        )),
                                  ),
                                ),
                        ],
                      )
                    : Container(),

                //

                Expanded(
                  child: controller.isdueleadloading
                      ? loader()
                      : controller.dueleadsdata.isEmpty
                          ? Container(
                              width: Get.width,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Image.asset('assets/icons/no_data.png', width: Get.width / 4),
                                  SizedBox(height: 20),
                                  Text("No Data Found", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16)),
                                ],
                              ),
                            )
                          : ListView.builder(
                              physics: ScrollPhysics(),
                              itemCount: controller.filt_dueleadsdata.length,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return GestureDetector(
                                  onTap: () {
                                    Get.to(LeadsDetails());
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10)), color: Colors.white),
                                    margin: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                                    child: Column(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 13),
                                          child: Row(
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            children: [
                                              Container(
                                                width: Get.width / 8,
                                                height: Get.width / 8,
                                                decoration: BoxDecoration(
                                                  border: Border.all(color: primaryColor, width: 2),
                                                  borderRadius: BorderRadius.circular(50),
                                                ),
                                                child: ClipRRect(
                                                  borderRadius: BorderRadius.circular(50),
                                                  child: Image.network(
                                                    controller.filt_dueleadsdata[index]['profile_picture'].toString(),
                                                    fit: BoxFit.cover,
                                                    errorBuilder: (a, b, c) {
                                                      return Image.asset('assets/images/avater_placeholder.jpg');
                                                    },
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 10),
                                              Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Row(
                                                    children: [
                                                      SizedBox(
                                                        width: Get.width / 1.65,
                                                        child: Text(
                                                          controller.filt_dueleadsdata[index]['name'] ?? '--',
                                                          style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: commontextColor, overflow: TextOverflow.ellipsis),
                                                        ),
                                                      ),
                                                      Icon(Icons.keyboard_arrow_right,)
                                                    ],
                                                  ),
                                                  Container(
                                                    child: Row(
                                                      children: [
                                                        SizedBox(
                                                          width: Get.width / 5,
                                                          child: Text("Phone", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                        ),
                                                        SizedBox(
                                                          width: Get.width / 2.5,
                                                          child: Text(
                                                            controller.filt_dueleadsdata[index]['phone'].toString() == "null" ? ":  --" : ":  ${controller.filt_dueleadsdata[index]['phone']}",
                                                            style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Row(
                                                    children: [
                                                      SizedBox(
                                                        width: Get.width / 5,
                                                        child: Text("Alternate number", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                      ),
                                                      SizedBox(
                                                        width: Get.width / 2.5,
                                                        child: Text(
                                                          controller.filt_dueleadsdata[index]['secondary_phone'].toString() == "" ? ":  --" : ":  ${controller.filt_dueleadsdata[index]['whatsapp']}",
                                                          style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  Row(
                                                    children: [
                                                      SizedBox(
                                                        width: Get.width / 5,
                                                        child: Text(
                                                          "Email",
                                                          style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: Get.width / 2.5,
                                                        child: Text(
                                                          controller.filt_dueleadsdata[index]['email'].toString() == "null" ? ":  --" : ":  ${controller.filt_dueleadsdata[index]['email']}",
                                                          style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(height: 3),
                                                  Row(
                                                    children: [
                                                      SizedBox(
                                                        width: Get.width / 5,
                                                        child: Text("Lead Status", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w600, color: commontextColor)),
                                                      ),
                                                      SizedBox(
                                                        width: Get.width / 2.5,
                                                        child: Text(
                                                          controller.filt_dueleadsdata[index]['status_name'].toString() == "" ? ":  --" : ":  ${controller.filt_dueleadsdata[index]['status_name'].toString()}",
                                                          style: TextStyle(fontSize: 11, fontWeight: FontWeight.w600, color: commontextColor),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(
                                            margin: EdgeInsets.symmetric(horizontal: 20),
                                            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                            width: Get.width,
                                            decoration: BoxDecoration(color: Colors.grey.withValues(alpha: 0.15), borderRadius: BorderRadius.circular(8)),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                if (controller.filt_dueleadsdata[index]['course_name'].toString() != "")
                                                  Row(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text("Course  : ".trim(), style: TextStyle(color: Colors.black54, fontSize: Get.width / 30)),
                                                      SizedBox(width: 5),
                                                      Expanded(
                                                        child: Text(
                                                          controller.filt_dueleadsdata[index]['course_name'].toString().trim(),
                                                          style: TextStyle(color: Colors.black, fontSize: 14, fontFamily: 'poppins_regular'),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                if (controller.filt_dueleadsdata[index]['category_name'].toString() != "")
                                                  Row(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text("Category  : ".trim(), style: TextStyle(color: Colors.black54, fontSize: Get.width / 30)),
                                                      SizedBox(width: 5),
                                                      Expanded(
                                                        child: Text(
                                                          controller.filt_dueleadsdata[index]['category_name'].toString().trim(),
                                                          style: TextStyle(color: Colors.black, fontSize: 14, fontFamily: 'poppins_regular'),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                if (controller.filt_dueleadsdata[index]['source_name'].toString() != "")
                                                  Row(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text("Lead Source  : ".trim(), style: TextStyle(color: Colors.black54, fontSize: Get.width / 30)),
                                                      SizedBox(width: 5),
                                                      Expanded(
                                                        child: Text(
                                                          controller.filt_dueleadsdata[index]['source_name'].toString().trim(),
                                                          style: TextStyle(color: Colors.black, fontSize: 14, fontFamily: 'poppins_regular'),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                if (controller.filt_dueleadsdata[index]['remarks'].toString() != "")
                                                  Row(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text("Remark  : ".trim(), style: TextStyle(color: Colors.black54, fontStyle: FontStyle.italic, fontSize: Get.width / 30)),
                                                      SizedBox(width: 5),
                                                      Expanded(
                                                        child: Text(
                                                          controller.filt_dueleadsdata[index]['remarks'].toString().trim(),
                                                          style: TextStyle(color: Colors.black, fontSize: 14, fontFamily: 'poppins_regular'),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                              ],
                                            )),
                                        Divider(color: Colors.black.withValues(alpha: 0.1)),
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 15),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text('created on', style: TextStyle(color: Colors.black54, fontSize: Get.width / 38)),
                                                  Text(controller.filt_dueleadsdata[index]['created_at'].toString(), style: TextStyle(color: Colors.black54, fontSize: Get.width / 33)),
                                                ],
                                              ),
                                              Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text('updated on', style: TextStyle(color: Colors.green, fontSize: Get.width / 38)),
                                                  Text(controller.filt_dueleadsdata[index]['updated_at'].toString(), style: TextStyle(color: Colors.green, fontSize: Get.width / 33)),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        Divider(color: Colors.black.withValues(alpha: 0.1)),
                                        Container(
                                            margin: EdgeInsets.symmetric(vertical: 12),
                                            child: IntrinsicHeight(
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    child: GestureDetector(
                                                      onTap: () {
                                                        var content = Uri.parse("Hi ${controller.filt_dueleadsdata[index]['name'].toString().trim()}");
                                                        launchURL("https://wa.me/${controller.filt_dueleadsdata[index]['country_code'].toString() + controller.filt_leadsdata[index]['phone'].toString()}?text=${content.toString().trim()}");
                                                      },
                                                      child: Container(
                                                        color: Colors.transparent,
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.center,
                                                          children: [
                                                            Image.asset('assets/icons/whatsapp.png', width: Get.width / 17),
                                                            SizedBox(width: 6),
                                                            Text("Whatsapp", textAlign: TextAlign.center, style: TextStyle(color: Colors.green, fontFamily: "poppins_regular")),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  VerticalDivider(color: Colors.black12),
                                                  Expanded(
                                                    child: GestureDetector(
                                                      onTap: () {
                                                        launchURL('tel:${controller.filt_dueleadsdata[index]['phone'].toString()}');
                                                      },
                                                      child: Container(
                                                        color: Colors.transparent,
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.center,
                                                          children: [
                                                            Icon(Icons.call, color: Colors.green, size: 20),
                                                            SizedBox(width: 6),
                                                            Text("Call", textAlign: TextAlign.center, style: TextStyle(color: Colors.green, fontFamily: "poppins_regular")),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 5,),
                                                  PopupMenuButton<int>(
                                                        itemBuilder: (context) => [
                                                          // if(GetStorage().read('current_role').toString() != "academic_counsellor")
                                                          PopupMenuItem(
                                                            value: 1,
                                                            child: Container(
                                                              child: Row(
                                                                children: [Icon(Icons.edit), SizedBox(width: 10), Text("Edit", style: TextStyle(color: Colors.black))],
                                                              ),
                                                            ),
                                                          ),
                                  
                                                          PopupMenuItem(
                                                            value: 2,
                                                            child: Row(
                                                              children: [
                                                                Icon(Icons.sync),
                                                                SizedBox(width: 10),
                                                                Text("Update", style: TextStyle(color: Colors.black)),
                                                              ],
                                                            ),
                                                          ),
                                  
                                                          PopupMenuItem(
                                                            value: 3,
                                                            child: Row(
                                                              children: [
                                                                Icon(Icons.history),
                                                                SizedBox(width: 10),
                                                                Text("History", style: TextStyle(color: Colors.black)),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                        color: Colors.white,
                                                        icon: Container(
                                                          height: 25,
                                                          width: 22,
                                                          
                                                          alignment: Alignment.center,
                                                          decoration: BoxDecoration(border: Border.all(color: Colors.grey),borderRadius: BorderRadius.circular(5)),
                                                          child: Center(child: Icon(Icons.more_vert, color: Colors.black,size: 20,)),
                                                        ),
                                                        elevation: 5,
                                                        padding: EdgeInsets.all(0),
                                                        onSelected: (value) async {
                                                          if (value == 1) {
                                                            Get.to(lead_add(action: 'edit', data: [controller.filt_dueleadsdata[index]], member_id: GetStorage().read('user_id').toString()))!.then((value) {
                                                              controller.call_dueleads(false);
                                                            });
                                                          } else if (value == 2) {
                                                            Get.to(lead_add(action: 'update', data: [controller.filt_dueleadsdata[index]], member_id: GetStorage().read('user_id').toString()))!.then((value) {
                                                              controller.call_dueleads(false);
                                                            });
                                                          } else if (value == 3) {
                                                            Get.to(() => lead_history(
                                                                  data: [controller.filt_dueleadsdata[index]],
                                                                ));
                                                          }
                                                        },
                                                      ),
                                                ],
                                              ),
                                            )),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  transfer_student_dialog(BuildContext context, Map data) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return GetBuilder<LeadsController>(
          builder: (controller) {
            return AlertDialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: EdgeInsets.all(0),
              content: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10.0)), color: Colors.white),
                width: MediaQuery.of(context).size.width,
                child: SingleChildScrollView(
                  physics: ScrollPhysics(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Section
                      Container(
                        padding: EdgeInsets.only(left: 15, right: 5, top: 10, bottom: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Transfer Lead",
                              style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600, fontSize: MediaQuery.of(context).size.width / 20),
                            ),
                            IconButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              icon: Icon(Icons.close, color: Colors.black),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                        child: Row(
                          children: [
                            SizedBox(
                              width: Get.width / 4,
                              child: Text("Name", style: TextStyle(fontSize: Get.width * .03, fontWeight: FontWeight.w600, color: Colors.black)),
                            ),
                            Text(':  ', style: TextStyle(color: Colors.black)),
                            SizedBox(
                              width: Get.width / 2.5,
                              child: Text(data['title'].toString(), style: TextStyle(fontSize: Get.width * .035, fontWeight: FontWeight.w500, color: Colors.black)),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                        child: Row(
                          children: [
                            SizedBox(
                              width: Get.width / 4,
                              child: Text("Place", style: TextStyle(fontSize: Get.width * .03, fontWeight: FontWeight.w600, color: Colors.black)),
                            ),
                            Text(':  ', style: TextStyle(color: Colors.black)),
                            SizedBox(
                              width: Get.width / 2.5,
                              child: Text(data['place'].toString(), style: TextStyle(fontSize: Get.width * .035, fontWeight: FontWeight.w500, color: Colors.black)),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                        child: Row(
                          children: [
                            SizedBox(
                              width: Get.width / 4,
                              child: Text("Phone", style: TextStyle(fontSize: Get.width * .03, fontWeight: FontWeight.w600, color: Colors.black)),
                            ),
                            Text(':  ', style: TextStyle(color: Colors.black)),
                            SizedBox(
                              width: Get.width / 2.5,
                              child: Text(data['phone'].toString(), style: TextStyle(fontSize: Get.width * .035, fontWeight: FontWeight.w500, color: Colors.black)),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 30),

                      // Confirm Button
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {

                                  controller.get_transfer_lead(data['id'].toString(), controller.sel_filtered_branches.toString(), data['branch_id'].toString());
                                controller.update();
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(horizontal: 15),
                                decoration: BoxDecoration(color: primaryColor, border: Border.all(color: primaryColor, width: 1), borderRadius: BorderRadius.circular(10)),
                                padding: EdgeInsets.symmetric(vertical: 15),
                                child: Text(
                                  "Confirm",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: Colors.white, fontFamily: "poppins_regular", fontWeight: FontWeight.w600),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  delete_student_dialog(BuildContext context, Map data) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return GetBuilder<LeadsController>(
          builder: (controller) {
            return AlertDialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: EdgeInsets.all(0),
              content: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10.0)), color: Colors.white),
                width: MediaQuery.of(context).size.width,
                child: SingleChildScrollView(
                  physics: ScrollPhysics(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Section
                      Container(
                        padding: EdgeInsets.only(left: 15, right: 5, top: 10, bottom: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Delete", style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600, fontSize: MediaQuery.of(context).size.width / 20)),
                            IconButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              icon: Icon(Icons.close, color: Colors.black),
                            ),
                          ],
                        ),
                      ),

                      // Confirmation Text
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                        child: Text("Are you sure to delete ${data['title']}?", style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w500, fontSize: MediaQuery.of(context).size.width / 28)),
                      ),
                      SizedBox(height: 30),
                      // Confirm Button
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                controller.get_delete_lead(data['id'].toString());
                                controller.update();
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(horizontal: 15),
                                decoration: BoxDecoration(color: primaryColor, border: Border.all(color: primaryColor, width: 1), borderRadius: BorderRadius.circular(10)),
                                padding: EdgeInsets.symmetric(vertical: 15),
                                child: Text(
                                  "Confirm",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: Colors.white, fontFamily: "poppins_regular", fontWeight: FontWeight.w600),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  convert_student_dialog(BuildContext context, Map data) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return GetBuilder<LeadsController>(
          builder: (controller) {
            return AlertDialog(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: EdgeInsets.all(0),
              content: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10.0)), color: Colors.white),
                width: MediaQuery.of(context).size.width,
                child: SingleChildScrollView(
                  physics: ScrollPhysics(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Section
                      Container(
                        padding: EdgeInsets.only(left: 15, right: 5, top: 10, bottom: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Convert", style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600, fontSize: MediaQuery.of(context).size.width / 20)),
                            IconButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              icon: Icon(Icons.close, color: Colors.black),
                            ),
                          ],
                        ),
                      ),

                      // Confirmation Text
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                        child: Text("Are you sure to convert ${data['title']} to Student?", style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w500, fontSize: MediaQuery.of(context).size.width / 28)),
                      ),

                      SizedBox(height: 30),

                      // Course Dropdown
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Course", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black)),
                            SizedBox(height: 10),
                            Container(
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                              child: DropdownButtonHideUnderline(
                                child: ButtonTheme(
                                  alignedDropdown: true,
                                  child: DropdownButton<String>(
                                    value: controller.sel_Course,
                                    iconSize: 30,
                                    dropdownColor: Colors.white,
                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                    hint: Text('- select a course -'),
                                    onChanged: (newValue) async {
                                      controller.sel_Course = newValue;
                                      controller.sel_batch = null;
                                      controller.update();
                                      await controller.get_batches(controller.sel_branch, controller.sel_Course);
                                      controller.update();
                                    },
                                    items: controller.courses.map<DropdownMenuItem<String>>((item) {
                                      return DropdownMenuItem<String>(
                                        value: item['id'].toString(),
                                        child: Container(
                                          width: MediaQuery.of(context).size.width / 1.7,
                                          child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 20),
                          ],
                        ),
                      ),

                      // Batch Dropdown
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Batch", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black)),
                            SizedBox(height: 10),
                            Container(
                              width: MediaQuery.of(context).size.width,
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                              child: DropdownButtonHideUnderline(
                                child: ButtonTheme(
                                  alignedDropdown: true,
                                  child: DropdownButton<String>(
                                    value: controller.sel_batch,
                                    iconSize: 30,
                                    dropdownColor: Colors.white,
                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                    hint: Text('- select a batch -'),
                                    onChanged: (newValue) {
                                      controller.sel_batch = newValue;
                                      controller.update();
                                    },
                                    items: controller.batches.map<DropdownMenuItem<String>>((item) {
                                      return DropdownMenuItem<String>(
                                        value: item['id'].toString(),
                                        child: SizedBox(
                                          width: MediaQuery.of(context).size.width / 1.7,
                                          child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 20),
                          ],
                        ),
                      ),

                      // Confirm Button
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                if (controller.sel_Course.toString() == '' || controller.sel_Course.toString() == 'null') {
                                  toast_info('Select a course');
                                } else if (controller.sel_batch.toString() == '' || controller.sel_batch.toString() == 'null') {
                                  toast_info('Select a batch');
                                } else {
                                  controller.get_convert_lead(data['id'].toString(), controller.sel_branch.toString(), controller.sel_Course.toString(), controller.sel_batch.toString());
                                }
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(horizontal: 15),
                                decoration: BoxDecoration(
                                  color: primaryColor,
                                  border: Border.all(color: primaryColor, width: 1),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                padding: EdgeInsets.symmetric(vertical: 15),
                                child: Text("Confirm", textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontFamily: "poppins_regular", fontWeight: FontWeight.w600)),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _fromDatePicker(ctx) {
    controller.follow_from_date.text = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.from_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.update();
    // showCupertinoModalPopup is a built-in function of the cupertino library
    showCupertinoModalPopup(
        context: ctx,
        builder: (_) => GetBuilder<LeadsController>(builder: (controller) {
              return Container(
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                height: 410,
                child: Column(
                  children: [
                    Container(
                      width: Get.width,
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(color: Colors.lightBlueAccent, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                      child: Text('Scrool to select', style: TextStyle(fontSize: 15, color: Colors.white, decoration: TextDecoration.none, fontFamily: '')),
                    ),

                    SizedBox(
                      height: 290,
                      child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: DateTime.now(),
                          onDateTimeChanged: (val) {
                            controller.fromDate = val;
                            controller.follow_from_date.text = controller.fromDate!.day.toString() + "-" + controller.fromDate!.month.toString() + "-" + controller.fromDate!.year.toString();
                            controller.from_date = "${controller.fromDate!.day}-${controller.fromDate!.month}-${controller.fromDate!.year}";
                            controller.update();
                          }),
                    ),

                    // Close the modal
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CupertinoButton(
                          child: const Text('Cancel'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                        CupertinoButton(
                          child: const Text('OK'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }));
  }

  void _toDatePicker(ctx) {
    controller.follow_to_date.text = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.to_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.update();
    // showCupertinoModalPopup is a built-in function of the cupertino library
    showCupertinoModalPopup(
        context: ctx,
        builder: (_) => GetBuilder<LeadsController>(builder: (controller) {
              return Container(
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                height: 410,
                child: Column(
                  children: [
                    Container(
                      width: Get.width,
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(color: Colors.lightBlueAccent, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                      child: Text('Scrool to select', style: TextStyle(fontSize: 15, color: Colors.white, decoration: TextDecoration.none, fontFamily: '')),
                    ),
                    SizedBox(
                      height: 290,
                      child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: DateTime.now(),
                          onDateTimeChanged: (val) {
                            setState(() {
                              controller.toDate = val;
                              controller.follow_to_date.text = "${controller.toDate!.day}-${controller.toDate!.month}-${controller.toDate!.year}";
                              controller.to_date = "${controller.toDate!.day}-${controller.toDate!.month}-${controller.toDate!.year}";
                            });
                          }),
                    ),

                    // Close the modal
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CupertinoButton(
                          child: const Text('Cancel'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                        CupertinoButton(
                          child: const Text('OK'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }));
  }

  // filter bottom sheet sec
  void filter_bottom_sheet() {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      isScrollControlled: true,
      context: context,
      useSafeArea: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      builder: (builder) {
        return GetBuilder<LeadsController>(
          builder: (controller) {
            return Padding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Stack(
                children: [
                  Container(
                    constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height / 1.2),
                    child: Column(
                      children: [
                        SizedBox(height: 30),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Image.asset('assets/icons/filter.png', width: Get.width / 18, color: Colors.black),
                              SizedBox(width: 15),
                              Text("Filter", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 23, fontWeight: FontWeight.w600, color: Colors.black)),
                            ],
                          ),
                        ),
                        Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            physics: ScrollPhysics(),
                            child: Wrap(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text("Date", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black), textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            Container(
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    child: Text("From date :", style: TextStyle(color: Colors.black54, fontSize: Get.width / 34)),
                                                  ),
                                                  SizedBox(width: 12),
                                                  Expanded(
                                                    child: Text("To date :", style: TextStyle(color: Colors.black54, fontSize: Get.width / 34)),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            SizedBox(height: 8),
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: TextField(
                                                    onTap: () {
                                                      _fromDatePicker(context);
                                                    },
                                                    readOnly: true,
                                                    style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                                                    controller: controller.follow_from_date,
                                                    decoration: InputDecoration(
                                                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                      filled: true,
                                                      hintStyle: TextStyle(color: Colors.black, fontFamily: "poppins_regular"),
                                                      hintText: controller.from_date.toString(),
                                                      fillColor: Colors.blueAccent.withValues(alpha: 0.2),
                                                      contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                                                      suffixIcon: controller.from_date.toString() == "All"
                                                          ? Container(width: 0, height: 0)
                                                          : IconButton(
                                                              onPressed: () {
                                                                controller.follow_from_date.text = "All";
                                                                controller.from_date = "All";
                                                                controller.update();
                                                                // fetleadsby_type();
                                                              },
                                                              icon: Icon(Icons.close, color: Colors.black),
                                                            ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(width: 12),
                                                Expanded(
                                                  child: TextField(
                                                    onTap: () {
                                                      _toDatePicker(context);
                                                    },
                                                    readOnly: true,
                                                    style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                                                    controller: controller.follow_to_date,
                                                    decoration: InputDecoration(
                                                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                      filled: true,
                                                      hintStyle: TextStyle(color: Colors.black, fontFamily: "poppins_regular"),
                                                      hintText: controller.to_date.toString(),
                                                      fillColor: Colors.blueAccent.withValues(alpha: 0.2),
                                                      contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                                                      suffixIcon: controller.to_date.toString() == "All"
                                                          ? SizedBox(width: 0, height: 0)
                                                          : IconButton(
                                                              onPressed: () {
                                                                controller.follow_to_date.text = "All";
                                                                controller.to_date = "All";
                                                                controller.update();
                                                              },
                                                              icon: Icon(Icons.close, color: Colors.black),
                                                            ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text("Interest Status", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black), textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            Container(
                                              width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_interest_status,
                                                    iconSize: 30,
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('- select a interest status -'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_interest_status = newValue.toString();
                                                      controller.update();
                                                    },
                                                    items: controller.alldata[0]['interest_status'].map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['id'].toString(),
                                                        child: SizedBox(
                                                          width: MediaQuery.of(context).size.width / 1.6,
                                                          child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text("Lead Status", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black), textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            Container(
                                              width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_status,
                                                    iconSize: 30,
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('- select a status -'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_status = newValue.toString();
                                                      controller.update();
                                                    },
                                                    items: controller.alldata[0]['lead_status'].map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['id'].toString(),
                                                        child: SizedBox(
                                                          width: MediaQuery.of(context).size.width / 1.6,
                                                          child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text("Lead Source", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black), textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            Container(
                                              width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_source,
                                                    iconSize: 30,
                                                    // icon: (null),
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('- select a source -'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_source = newValue.toString();
                                                      controller.update();
                                                    },
                                                    items: controller.alldata[0]['lead_source'].map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['id'].toString(),
                                                        child: SizedBox(
                                                          width: MediaQuery.of(context).size.width / 1.6,
                                                          child: Text(
                                                            item['title'].toString(),
                                                            style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27),
                                                            maxLines: 2,
                                                          ),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              height: 20,
                                            ),
                                          ],
                                        ),
                                      ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      // Container(
                                      //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      //   child: Column(
                                      //     crossAxisAlignment: CrossAxisAlignment.start,
                                      //     children: [
                                      //       SizedBox(height: 10),
                                      //       Text("Country",
                                      //           style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                      //           textAlign: TextAlign.left),
                                      //       SizedBox(height: 10),
                                      //       Container(
                                      //         width: MediaQuery.of(context).size.width,
                                      //         decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                      //         child: DropdownButtonHideUnderline(
                                      //           child: ButtonTheme(
                                      //             alignedDropdown: true,
                                      //             child: DropdownButton<String>(
                                      //               value: controller.sel_country,
                                      //               iconSize: 30,
                                      //               style: TextStyle(color: Colors.black54, fontSize: 14),
                                      //               hint: Text('- select a country -'),
                                      //               onChanged: (newValue) async {
                                      //                 controller.sel_country = newValue.toString();
                                      //                 controller.update();
                                      //               },
                                      //               items: controller.alldata[0]['country'].map<DropdownMenuItem<String>>((item) {
                                      //                 return DropdownMenuItem<String>(
                                      //                   value: item['id'].toString(),
                                      //                   child: SizedBox(
                                      //                     width: MediaQuery.of(context).size.width / 1.6,
                                      //                     child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                      //                   ),
                                      //                 );
                                      //               }).toList(),
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //       SizedBox(
                                      //         height: 20,
                                      //       ),
                                      //     ],
                                      //   ),
                                      // ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      // Container(
                                      //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      //   child: Column(
                                      //     crossAxisAlignment: CrossAxisAlignment.start,
                                      //     children: [
                                      //       SizedBox(height: 10),
                                      //       Text("University",
                                      //           style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                      //           textAlign: TextAlign.left),
                                      //       SizedBox(height: 10),
                                      //       Container(
                                      //         width: MediaQuery.of(context).size.width,
                                      //         decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                      //         child: DropdownButtonHideUnderline(
                                      //           child: ButtonTheme(
                                      //             alignedDropdown: true,
                                      //             child: DropdownButton<String>(
                                      //               value: controller.sel_university,
                                      //               iconSize: 30,
                                      //               style: TextStyle(color: Colors.black54, fontSize: 14),
                                      //               hint: Text('- select a university -'),
                                      //               onChanged: (newValue) async {
                                      //                 controller.sel_university = newValue.toString();
                                      //                 controller.update();
                                      //               },
                                      //               items: controller.alldata[0]['university'].map<DropdownMenuItem<String>>((item) {
                                      //                 return DropdownMenuItem<String>(
                                      //                   value: item['id'].toString(),
                                      //                   child: SizedBox(
                                      //                     width: MediaQuery.of(context).size.width / 1.6,
                                      //                     child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                      //                   ),
                                      //                 );
                                      //               }).toList(),
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //       SizedBox(height: 20),
                                      //     ],
                                      //   ),
                                      // ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text("Course", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black), textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            Container(
                                              width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_course,
                                                    iconSize: 30,
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('- select a course -'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_course = newValue.toString();
                                                      controller.update();
                                                    },
                                                    items: controller.alldata[0]['courses'].map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['id'].toString(),
                                                        child: SizedBox(
                                                          width: MediaQuery.of(context).size.width / 1.6,
                                                          child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              height: 20,
                                            ),
                                          ],
                                        ),
                                      ),

                                      // branches
                                      Padding(
                                        padding: EdgeInsets.only(left: 15),
                                        child: Container(
                                          alignment: Alignment.centerLeft,
                                          width: MediaQuery.of(context).size.width / 1.2,
                                          padding: EdgeInsets.only(bottom: 10),
                                          child: Row(
                                            children: [Text("Branches", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black), textAlign: TextAlign.left)],
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: MediaQuery.of(context).size.width / 1.14,
                                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black12, width: 1)),
                                        margin: EdgeInsets.only(left: 15),
                                        child: DropdownButtonHideUnderline(
                                          child: ButtonTheme(
                                            alignedDropdown: true,
                                            child: DropdownButton<String>(
                                              value: controller.sel_branches.toString() == 'null' || controller.sel_branches.toString() == '' ? null : controller.sel_branches.toString(),
                                              iconSize: 30,
                                              style: TextStyle(color: Colors.black54, fontSize: 14),
                                              hint: Text('--select branch--'),
                                              onChanged: (newValue) async {
                                                controller.sel_branches = newValue!;
                                                controller.update();
                                                await controller.get_academic_counselor(newValue);
                                                controller.update();
                                              },
                                              items: controller.alldata[0]['branches'].map<DropdownMenuItem<String>>((item) {
                                                return DropdownMenuItem(
                                                  value: item['id'].toString(),
                                                  child: SizedBox(
                                                    width: MediaQuery.of(context).size.width / 1.6,
                                                    child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                  ),
                                                );
                                              }).toList(),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: MediaQuery.of(context).size.height / 30),

                                      // Telecaller
                                      if (GetStorage().read('current_role').toString() != "academic_counsellor")
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Padding(
                                              padding: EdgeInsets.only(left: 15),
                                              child: Container(
                                                alignment: Alignment.centerLeft,
                                                width: MediaQuery.of(context).size.width / 1.2,
                                                padding: EdgeInsets.only(bottom: 10),
                                                child: Row(
                                                  children: [
                                                    Text("Telecaller", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black), textAlign: TextAlign.left)
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Container(
                                              width: MediaQuery.of(context).size.width / 1.14,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black12, width: 1)),
                                              margin: EdgeInsets.only(left: 15),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_academic_counselor.toString() == 'null' || controller.sel_academic_counselor.toString() == '' ? null : controller.sel_academic_counselor.toString(),
                                                    iconSize: 30,
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('--select telecaller --'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_academic_counselor = newValue!;
                                                      controller.update();
                                                    },
                                                    items: controller.academic_counselor.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem(
                                                        value: item['team_id'].toString(),
                                                        child: SizedBox(
                                                          width: MediaQuery.of(context).size.width / 1.6,
                                                          child: Text(item['name'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: MediaQuery.of(context).size.height / 30),
                                          ],
                                        ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      // if(GetStorage().read('current_role').toString() != "academic_counsellor" || GetStorage().read('current_role').toString() == "team_lead")
                                      //   Container(
                                      //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      //   child: Column(
                                      //     crossAxisAlignment: CrossAxisAlignment.start,
                                      //     children: [
                                      //       SizedBox(height: 10,),
                                      //       Container(
                                      //         // padding: EdgeInsets.symmetric(horizontal: 15),
                                      //         // color: Colors.yellow,
                                      //           child: Text("Team",style: TextStyle(fontFamily: "poppins_regular",fontSize: MediaQuery.of(context).size.width/25,fontWeight: FontWeight.w500,color: Colors.black),textAlign: TextAlign.left,)
                                      //       ),
                                      //       SizedBox(height: 10,),
                                      //
                                      //       Container(
                                      //         width: MediaQuery.of(context).size.width,
                                      //         decoration: BoxDecoration(
                                      //             borderRadius: BorderRadius.circular(10.0),
                                      //             border: Border.all(color: Colors.black26,width: 0.5),
                                      //             color: Colors.white
                                      //         ),
                                      //         child: DropdownButtonHideUnderline(
                                      //           child: ButtonTheme(
                                      //             alignedDropdown: true,
                                      //             child: DropdownButton<String>(
                                      //               value: sel_team,
                                      //               iconSize: 30,
                                      //               // icon: (null),
                                      //               style: TextStyle(
                                      //                 color: Colors.black54,
                                      //                 fontSize: 14,
                                      //               ),
                                      //               hint: Text('- select a team -'),
                                      //               onChanged: (newValue) async {
                                      //                 setState(() {
                                      //                   sel_team = newValue.toString();
                                      //                 });
                                      //               },
                                      //               items: alldata[0]['team'].map<DropdownMenuItem<String>>((item) {
                                      //                 return  DropdownMenuItem<String>(
                                      //                   child:  Container(
                                      //                     width: MediaQuery.of(context).size.width / 1.6,
                                      //                     child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                      //                   ),
                                      //                   value: item['id'].toString(),
                                      //                 );
                                      //               }).toList(),
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //
                                      //       SizedBox(height: 20,),
                                      //
                                      //
                                      //     ],
                                      //   ),
                                      // ),

                                      SizedBox(height: 30),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    Get.back();
                                    controller.get_leads(true);
                                  },
                                  child: Container(
                                    width: Get.width / 4,
                                    decoration: BoxDecoration(color: primaryColor, border: Border.all(color: primaryColor, width: 1), borderRadius: BorderRadius.circular(1000)),
                                    padding: EdgeInsets.symmetric(vertical: 15),
                                    child: Text("Apply", textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontFamily: "poppins_regular", fontWeight: FontWeight.w600)),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 15),
                      ],
                    ),
                  ),
                  Positioned(
                      right: 0,
                      left: 0,
                      top: 0,
                      child: GestureDetector(
                        onTap: () {},
                        child: Container(color: Colors.transparent, child: Image.asset('assets/icons/drag_handle.png')),
                      )),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
