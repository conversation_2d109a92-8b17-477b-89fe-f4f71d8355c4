import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lottie/lottie.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../views/auth/login.dart';
import '../dashboard/dashboard.dart';


class error_500 extends StatelessWidget {
  // List data;data
  // String type;
  // error_page({required this.type});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: textwhiteColor,
      body: Container(
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppBar(toolbarHeight: 0,backgroundColor: Color(0xff00ffffff),),

            // Image.asset('assets/logo/logo.png',width: Get.width/2.5,),
            // SizedBox(height: 10,),
            // type.toString() == '0'?
            Column(
              children: [
                Container(
                  // color: Colors.yellow,
                 width: Get.width,
                  // color: Colors.yellow,
                 child: Image.asset('assets/icons/error_500.png',height: Get.height/6, width: Get.width,)
                ),
                SizedBox(height: 20,),
                Text("Oops",style: TextStyle(color: textblackColor,fontFamily: font_bold,fontSize: Get.width*.04),),
                SizedBox(height: 10,),
                Text("Server Under Maintenance",style: TextStyle(color: textblackColor,fontFamily: font_medium),),
                SizedBox(height: 40,),
                common_button_gradient_secondary(
                  onPressed: () {
                    GetStorage().read('login_status').toString() == "true" ? Get.offAll(()=> dashboard()) : Get.offAll(()=> Login());
                  },
                  borderRadius: BorderRadius.circular(8),
                  width: Get.width*.9,
                  child: Text("Back to ${GetStorage().read('login_status').toString() == "true" ?'Dashboard' : 'Login'}",style: TextStyle(color: textwhiteColor,fontWeight: FontWeight.w600,fontFamily: font_semibold,fontSize: Get.width/25),),
                ),
              ],
            ),
                // :

          ],
        ),
      ),
    );
  }
}
