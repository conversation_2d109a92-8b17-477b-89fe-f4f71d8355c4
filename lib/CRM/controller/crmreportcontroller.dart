import 'package:edutalim/CRM/api/apis.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class CRMReportController extends GetxController{



  String? sel_section = "all",sel_status,sel_team,sel_sourse;
  List reportdata = [

       {
        "teams": [
          {
            "id": "5",
            "title": "Guidance centre of smartphone technology TIR",
            "team_lead_id": "46",
            "status": "1"
          },
          {
            "id": "6",
            "title": "Unique institute smartphone technology KSD",
            "team_lead_id": "45",
            "status": "1"
          },
          {
            "id": "7",
            "title": "Zwixt institute of advanced smartphone technology KLM",
            "team_lead_id": "30",
            "status": "1"
          },
          {
            "id": "8",
            "title": "Empowering gen technologies KKL",
            "team_lead_id": "66",
            "status": "1"
          },
          {
            "id": "9",
            "title": "Ibeau technologies KDY",
            "team_lead_id": "32",
            "status": "1"
          },
          {
            "id": "10",
            "title": "Research institute of wireless technology MJI",
            "team_lead_id": "33",
            "status": "1"
          },
          {
            "id": "11",
            "title": "Frontier wireless care institute NBR",
            "team_lead_id": "81",
            "status": "1"
          },
          {
            "id": "12",
            "title": "Cell tell institute of mobile phone technology PMNA",
            "team_lead_id": "35",
            "status": "1"
          },
          {
            "id": "13",
            "title": "Britco LLP Deira DXB",
            "team_lead_id": "36",
            "status": "1"
          },
          {
            "id": "14",
            "title": "Hyderabad institute of smartphone technology HYD",
            "team_lead_id": "41",
            "status": "1"
          },
          {
            "id": "15",
            "title": "Circuit tree smartphone institute GWY",
            "team_lead_id": null,
            "status": "1"
          },
          {
            "id": "16",
            "title": "IMPT DELHI",
            "team_lead_id": "53",
            "status": "1"
          },
          {
            "id": "17",
            "title": "UD-Technical Ho, Kinfra",
            "team_lead_id": "90",
            "status": "1"
          },
          {
            "id": "18",
            "title": "Right career institute of technology VTK",
            "team_lead_id": "48",
            "status": "1"
          },
          {
            "id": "19",
            "title": "Techfluent smartphone engineering insitute OTP",
            "team_lead_id": "37",
            "status": "1"
          },
          {
            "id": "20",
            "title": "Globtech institute of future technology KNR",
            "team_lead_id": "47",
            "status": "1"
          },
          {
            "id": "21",
            "title": "Techno savvy institute PYN",
            "team_lead_id": "39",
            "status": "1"
          },
          {
            "id": "22",
            "title": "RTC institute of entrepreneurship ALV",
            "team_lead_id": "82",
            "status": "1"
          },
          {
            "id": "23",
            "title": "Hotspot institute of wireless comminication CLT",
            "team_lead_id": "38",
            "status": "1"
          },
          {
            "id": "24",
            "title": "Intelligent career institute of technology TCR",
            "team_lead_id": "40",
            "status": "1"
          },
          {
            "id": "26",
            "title": "Circuit tree smartphone institute GWY",
            "team_lead_id": "42",
            "status": "1"
          }
        ],
        "sources": [
          {
            "id": "1",
            "title": "Google Lead Ad"
          },
          {
            "id": "2",
            "title": "Facebook Instagram Inquiry"
          },
          {
            "id": "3",
            "title": "Instagram"
          },
          {
            "id": "4",
            "title": "LinkedIn"
          },
          {
            "id": "5",
            "title": "Seminar"
          },
          {
            "id": "6",
            "title": "Reference "
          },
          {
            "id": "7",
            "title": "Data Collection"
          },
          {
            "id": "8",
            "title": "Website inquiry"
          },
          {
            "id": "10",
            "title": "Scholl seminar"
          },
          {
            "id": "11",
            "title": "Public seminar"
          },
          {
            "id": "12",
            "title": "Webinar"
          },
          {
            "id": "13",
            "title": "Service centre"
          },
          {
            "id": "14",
            "title": "Notice "
          },
          {
            "id": "15",
            "title": "Home visiting"
          },
          {
            "id": "16",
            "title": "Kudumbasree"
          },
          {
            "id": "17",
            "title": "Local club"
          },
          {
            "id": "18",
            "title": "Organaisation"
          },
          {
            "id": "19",
            "title": "Exhibition"
          },
          {
            "id": "20",
            "title": "Alumni"
          },
          {
            "id": "21",
            "title": "Public advertisement"
          },
          {
            "id": "22",
            "title": "TV advertisement"
          },
          {
            "id": "23",
            "title": "Data collection"
          },
          {
            "id": "24",
            "title": "News paper ad"
          },
          {
            "id": "25",
            "title": "Hoardings/sun pack "
          },
          {
            "id": "26",
            "title": "Just dial"
          },
          {
            "id": "27",
            "title": "Free class"
          },
          {
            "id": "28",
            "title": "Vlog/reals"
          },
          {
            "id": "29",
            "title": "Industrial visit"
          },
          {
            "id": "30",
            "title": "WhatsAp"
          },
          {
            "id": "31",
            "title": "Tele caller"
          },
          {
            "id": "32",
            "title": "Phone calls"
          },
          {
            "id": "33",
            "title": "SMS"
          },
          {
            "id": "34",
            "title": "Key partner"
          },
          {
            "id": "35",
            "title": "Transfer by BAI"
          },
          {
            "id": "36",
            "title": "Current students"
          },
          {
            "id": "37",
            "title": "Parents"
          },
          {
            "id": "38",
            "title": "Assigned by Ho"
          },
          {
            "id": "39",
            "title": "C/o"
          },
          {
            "id": "40",
            "title": "Walk in"
          }
        ],
        "statuses": [
          {
            "id": "1",
            "title": "Pending"
          },
          {
            "id": "2",
            "title": "Enrolled"
          },
          {
            "id": "3",
            "title": "Follow-up"
          },
          {
            "id": "4",
            "title": "Interested"
          },
          {
            "id": "5",
            "title": "Not-interested"
          },
          {
            "id": "7",
            "title": "Financial problem"
          }
        ],
        "lead_overview": [
          {
            "id": "1",
            "title": "Pending",
            "count": 591
          },
          {
            "id": "2",
            "title": "Enrolled",
            "count": 47
          },
          {
            "id": "3",
            "title": "Follow-up",
            "count": 538
          },
          {
            "id": "4",
            "title": "Interested",
            "count": 193
          },
          {
            "id": "5",
            "title": "Not-interested",
            "count": 351
          },
          {
            "id": "7",
            "title": "Financial problem",
            "count": 14
          }
        ],
        "call_overview": {
          "Incoming": 0,
          "Outgoing": 0,
          "Missed": 0,
          "contacts": 0,
          "Declined": 0
        },
        "accounts": {
          "total_revenue": 1500,
          "pending_amount": 1200
        },
        "lead_sources": [
          {
            "id": "7",
            "title": "Data Collection",
            "count": 1579
          },
          {
            "id": "30",
            "title": "WhatsAp",
            "count": 81
          },
          {
            "id": "3",
            "title": "Instagram",
            "count": 68
          },
          {
            "id": "32",
            "title": "Phone calls",
            "count": 55
          },
          {
            "id": "2",
            "title": "Facebook Instagram Inquiry",
            "count": 45
          }
        ],
        "lead_makers": [
          {
            "id": "64",
            "profile_picture": "",
            "name": "Binzy ",
            "phone": "**********",
            "count": 464
          },
          {
            "id": "56",
            "profile_picture": "",
            "name": "Sanoop M",
            "phone": "9847025925",
            "count": 407
          },
          {
            "id": "51",
            "profile_picture": "",
            "name": "Bushra",
            "phone": "8606398098",
            "count": 400
          },
          {
            "id": "55",
            "profile_picture": "",
            "name": "Murali",
            "phone": "9495646047",
            "count": 198
          },
          {
            "id": "75",
            "profile_picture": "",
            "name": "Ashir",
            "phone": "8086499599",
            "count": 190
          }
        ],
        "emigration_countries": []
      }

  ];
  bool isLoading  = false;

  TextEditingController follow_from_date = TextEditingController();
  TextEditingController follow_to_date = TextEditingController();

  // String from_date = DateTime.now().day.toString()+"-"+DateTime.now().month.toString()+"-"+DateTime.now().year.toString();
  // String to_date = DateTime.now().day.toString()+"-"+DateTime.now().month.toString()+"-"+DateTime.now().year.toString();
  String from_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
  String to_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";

  DateTime? fromDate;
  DateTime? toDate;


  get_report()async{

      isLoading = false;

      var data = await CRMApiBaseHandler.fet_report(from_date == "All" ? '' :from_date.toString(),to_date == "All" ? '' :to_date.toString(),sel_status?? '',sel_team?? '',sel_sourse?? '');

      // reportdata = [data[0]['data']];
      isLoading = false;
      update();


  }


}