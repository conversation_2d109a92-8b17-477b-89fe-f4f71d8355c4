import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/leaderboard_controller.dart';

class LeaderBoardExamwise extends StatefulWidget {
  final String title;

  LeaderBoardExamwise({Key? key, required this.title}) : super(key: key);

  @override
  State<LeaderBoardExamwise> createState() => _LeaderBoardExamwiseState();
}

class _LeaderBoardExamwiseState extends State<LeaderBoardExamwise> {

  LeaderboardController controller = Get.put(LeaderboardController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    controller.get_leaderboard_data();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LeaderboardController>(
        builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: controller.isLoading ? loader() : SingleChildScrollView(
            child: Column(
              children: [
                buildTopBanner(),
                <PERSON><PERSON><PERSON><PERSON>(height: 14),
                <PERSON>umn(
                  children: [
                    // Table Header
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                      child: Row(
                        children: [
                          Expanded(flex: 1, child: Text("Rank", style: TextStyle(fontWeight: FontWeight.bold))),
                          Expanded(flex: 3, child: Text("Name", style: TextStyle(fontWeight: FontWeight.bold))),
                          Expanded(flex: 1, child: Text("Top Mark", style: TextStyle(fontWeight: FontWeight.bold))),
                        ],
                      ),
                    ),

                    // const Divider(),
                    SizedBox(height: 10),

                    // Leaderboard List
                    // if (controller.leaderboard_data.length != 0 || controller.leaderboard_data.length < 4)
                      ListView.builder(
                        shrinkWrap: true,
                        // important!
                        physics: NeverScrollableScrollPhysics(),
                        // disables nested scrolling
                        padding: EdgeInsets.all(0),
                        itemCount: controller.leaderboard_data.length,
                        itemBuilder: (context, index) {
                          final item = controller.leaderboard_data[index];
                          return Container(
                            margin: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(color: Colors.transparent),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6, offset: Offset(0, 2))],
                            ),
                            child: Row(
                              children: [
                                SizedBox(width: 5),
                                Expanded(flex: 0, child: Text("${item['rank']}")),
                                SizedBox(width: 10),
                                SizedBox(height: 50, child: VerticalDivider(color: textblackColor.withValues(alpha: 0.1), width: 2, endIndent: 1, indent: 2, thickness: 1)),
                                SizedBox(width: 10),
                                Expanded(
                                  flex: 3,
                                  child: Row(
                                    children: [
                                      CircleAvatar(radius: 12, child: Image.network(item["profile_image"], width: Get.width / 4.5, height: Get.width / 4.5, fit: BoxFit.cover)),
                                      SizedBox(width: 8),
                                      Text(item["name"]),
                                    ],
                                  ),
                                ),

                                Expanded(flex: 2, child: Text("${item['score']}", textAlign: TextAlign.right, style: TextStyle(fontWeight: FontWeight.bold))),
                                SizedBox(width: 5),
                              ],
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ],
            ),
          ),
        );
      }
    );
  }

  Widget buildTopBanner() {
    return Container(
      padding: EdgeInsets.only(top: 0, left: 0, right: 0, bottom: 24),
      decoration: BoxDecoration(
        image: DecorationImage(image: AssetImage('assets/bg/leader_bg.png'), fit: BoxFit.cover),
        gradient: LinearGradient(colors: [Color(0xFF997DF9), Color(0xFF916AFF)]),
        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(40), bottomRight: Radius.circular(40)),
      ),
      child: Column(
        children: [
          AppBar(
            backgroundColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
            elevation: 5,
            centerTitle: true,
            leading: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Container(margin: EdgeInsets.all(10), child: Icon(Icons.arrow_back, color: textwhiteColor)),
            ),
            title: Text(widget.title, style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w700, fontFamily: font_regular, fontSize: Get.width / 24)),
          ),
          SizedBox(height: 10),
          Leaderboard(data: controller.leaderboard_data.take(3).toList()),
        ],
      ),
    );
  }

  Widget buildPodiumUser(String name, String score, String label, double size, {bool highlight = false, String image = "👤"}) {
    return Column(
      children: [
        if (!highlight)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle),
            child: Text(label, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12, color: Colors.black)),
          ),
        Stack(
          alignment: Alignment.center,
          children: [
            CircleAvatar(radius: size / 2, backgroundColor: Colors.white, child: Text(image, style: TextStyle(fontSize: size / 2))),
            if (highlight)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(color: Color(0xFFFFC107), borderRadius: BorderRadius.circular(12)),
                  child: Text(label, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 10)),
                ),
              ),
          ],
        ),
        SizedBox(height: 8),
        Text(name, style: TextStyle(color: Colors.white)),
        Text(score, style: TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }
}

class Leaderboard extends StatelessWidget {
  Leaderboard({super.key, required this.data});

  List data;

  @override
  Widget build(BuildContext context) {

    if(data.isEmpty)
    {
      return Container(
        padding: EdgeInsets.symmetric(vertical: 50),
        child: Text("No Profile's found",style: TextStyle(fontFamily: font_medium,color: Colors.white),),
      );
    }

    return Container(
      height: Get.height * .25,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Center (1st Place) - on top
          if(data.isNotEmpty)
          Positioned(
            top: 0,
            child: LeaderboardItem(
              alignment: Alignment.topRight,
              rank: '1st',
              name: data[0]["name"].toString(),
              score: data[0]["score"].toString(),
              imagePath: data[0]["profile_image"].toString(),
              color: Colors.orange,
              textColor: Colors.white,
            ),
          ),

          // Row for 2nd and 3rd
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Spacer(),
                // 3rd Place - Left
                data.length >= 3 ?
                Padding(
                  padding: const EdgeInsets.only(bottom: 0), // adjust to lower it
                  child: LeaderboardItem(
                    alignment: Alignment.topLeft,
                    rank: '3rd',
                    name: data[2]["name"].toString(),
                    score: data[2]["score"].toString(),
                    imagePath: data[2]["profile_image"].toString(),
                    color: Colors.white,
                    textColor: Colors.black,
                  ),
                ): Container(),
                Spacer(),
                Spacer(),
                Spacer(),
                // 2nd Place - Right
                data.length >= 2 ?
                Padding(
                  padding: const EdgeInsets.only(bottom: 0), // slightly higher than 3rd
                  child: LeaderboardItem(
                    alignment: Alignment.topRight,
                    rank: '2nd',
                    // Fix typo from '2st' to '2nd'
                    name: data[1]["name"].toString(),
                    score: data[1]["score"].toString(),
                    imagePath: data[1]["profile_image"].toString(),
                    color: Colors.white,
                    textColor: Colors.black,
                  ),
                ): Container(),
                Spacer(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LeaderboardItem extends StatelessWidget {
  final String rank;
  final String name;
  final String score;
  final String imagePath;
  final Color color;
  final Color textColor;
  AlignmentGeometry alignment;

  LeaderboardItem({required this.rank, required this.name, required this.score, required this.imagePath, required this.color, required this.textColor, required this.alignment});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          alignment: alignment,
          children: [
            
            Container(padding: EdgeInsets.all(15),child: CircleAvatar(radius: 40, backgroundImage: NetworkImage(imagePath))),

            Positioned(
              top: 0,
              right: rank.toString()  == "3rd" ? 70 : 0,
              child: Container(
                height: 50,
                width: Get.width * .09,
                decoration: BoxDecoration(image: DecorationImage(colorFilter: ColorFilter.mode(color, BlendMode.srcIn), image: AssetImage('assets/img/hexagon.png'))),
                child: Center(child: Text(rank, style: TextStyle(color: textColor, fontWeight: FontWeight.bold, fontSize: Get.width * .03))),
                // height: 50,
              ),
            ),

            // Transform.rotate(
            //   angle: pi *.0,
            //   child: CustomPaint(
            //     size: Size(80, 80),
            //     painter: HexagonPainter(
            //         color: color,
            //       // cornerRadius: 2
            //     ),
            //     child: Padding(
            //       padding: const EdgeInsets.all(8.0),
            //       child: Text(
            //         rank,
            //         style: TextStyle(
            //           color: textColor,
            //           fontWeight: FontWeight.bold,
            //           fontSize: Get.width*.03,
            //         ),
            //       ),
            //     ),
            //   ),
            // ),
          ],

        ),
        SizedBox(height: 10),
        Text(name, style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: textwhiteColor)),
        Text(score, style: TextStyle(fontSize: 16, color: textwhiteColor,fontFamily: font_bold,fontWeight: FontWeight.w600)),
      ],
    );
  }
}

// class HexagonPainter extends CustomPainter {
//   final Color color;
//   final double curvature; // Controls how much curve to apply
//
//   HexagonPainter({
//     required this.color,
//     this.curvature = 0, // Default subtle curve
//   });
//
//   @override
//   void paint(Canvas canvas, Size size) {
//     Paint paint = Paint()..color = color;
//     Path path = Path();
//
//     double w = size.width;
//     double h = size.height;
//
//     // Define the six corners of the hexagon
//     final points = [
//       Offset(w * 0.5, 0),       // Top
//       Offset(w, h * 0.25),      // Top-right
//       Offset(w, h * 0.75),      // Bottom-right
//       Offset(w * 0.5, h),       // Bottom
//       Offset(0, h * 0.75),      // Bottom-left
//       Offset(0, h * 0.25),      // Top-left
//     ];
//
//     // Start at the top point
//     path.moveTo(points[0].dx, points[0].dy);
//
//     // Draw curved lines between each point
//     for (int i = 0; i < points.length; i++) {
//       final current = points[i];
//       final next = points[(i + 1) % points.length];
//
//       // Calculate control point for the curve
//       final controlX = current.dx + (next.dx - current.dx) * (0.5 - curvature);
//       final controlY = current.dy + (next.dy - current.dy) * (0.5 + curvature);
//
//       // Create a quadratic bezier curve
//       path.quadraticBezierTo(
//           controlX,
//           controlY,
//           next.dx,
//           next.dy
//       );
//     }
//
//     path.close();
//     canvas.drawPath(path, paint);
//   }
//
//   @override
//   bool shouldRepaint(CustomPainter oldDelegate) => false;
// }
//
