import 'package:edutalim/CRM/api/apis.dart';
import 'package:get/get.dart';

import '../../LMS/views/InteractivePlayer/utils/utils.dart';

class CandidateEnrolledCoures extends GetxController {
  late Map selectedCandidate;

  List branches = [], courses = [], batches = [], enrolled_courses = [];
  bool isBranchLoading = true, isCourseLoading = true, isBatchLoading = true, isLoading = false;

  get_enrolled_courses() async {

    print("called------");
    // var data = await CRMApiBaseHandler.fet_enrolled_courses(selectedCandidate['id'].toString());
    // enrolled_courses = data['data'];
    enrolled_courses = [
      {
        "id": "12",
        "course_id": "2",
        "branch_id": "13",
        "batch_id": "4",
        "user_id": "85",
        "created_by": "83",
        "created_at": "2025-02-24 14:44:32",
        "updated_by": null,
        "updated_at": null,
        "deleted_by": null,
        "deleted_at": null,
        "course_name": "CDT",
        "users_name": "TEST LEAD ",
        "batch_name": "January 1",
        "branch_name": "OTTAPPALAM",
        "enrolled_date": "24-02-2025"
      }
    ];
    isLoading = false;
    update();
  }

  get_branches() async {
    print("called------");
    // var data = await CRMApiBaseHandler.fet_branches();
    // branches = data['data']['branches'];
    branches = [
      {
        "id": "1",
        "title": "HO-KINFRA",
        "is_main": "1",
        "course_id": null,
        "created_at": "2025-01-03 15:42:28",
        "updated_at": "2025-02-14 11:49:15",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "2",
        "title": "KASARGOD",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-01-03 15:49:18",
        "updated_at": "2025-02-06 14:56:50",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "3",
        "title": "PAYYANNUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-01-03 16:36:22",
        "updated_at": "2025-02-06 14:57:02",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "4",
        "title": "KANNUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-01-11 18:45:42",
        "updated_at": "2025-02-06 14:57:14",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "5",
        "title": "VATAKARA",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-01-31 09:48:06",
        "updated_at": "2025-02-06 14:57:26",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "6",
        "title": "CALICUT",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:57:35",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "7",
        "title": "TIRUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:58:16",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "8",
        "title": "KOTTAKKAL",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:58:25",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "9",
        "title": "KONDOTTY",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:58:45",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "10",
        "title": "MANJERI",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:58:54",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "11",
        "title": "NILAMBUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:04",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "12",
        "title": "PERINTHALMANNA",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:14",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "13",
        "title": "OTTAPPALAM",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:25",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "14",
        "title": "THRISSUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:36",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "15",
        "title": "ALUVA",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:44",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "16",
        "title": "KOLLAM",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:51",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "17",
        "title": "HYDERABAD",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 15:00:02",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "18",
        "title": "GUWAHATTY",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 15:00:17",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "19",
        "title": "Delhi",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 15:00:30",
        "updated_at": "2025-02-07 15:47:17",
        "deleted_at": null,
        "created_by": "26",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "20",
        "title": "DUBAI",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-07 14:49:08",
        "updated_at": "2025-02-07 14:49:33",
        "deleted_at": null,
        "created_by": "26",
        "updated_by": "26",
        "deleted_by": null
      }
    ];
    isBranchLoading = false;
    update();
  }

  get_courses() async {
    print("called------");
    // var data = await CRMApiBaseHandler.fet_courses();
    // courses = data['data'];
    courses = [
      {
        "id": "1",
        "title": "Digital Marketing",
        "branch_id": null,
        "amount": "1500",
        "created_by": "1",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-01-10 14:58:33",
        "deleted_at": null
      },
      {
        "id": "2",
        "title": "CDT",
        "branch_id": null,
        "amount": "20000",
        "created_by": "1",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-01-13 11:44:34",
        "deleted_at": null
      },
      {
        "id": "3",
        "title": "Short term",
        "branch_id": null,
        "amount": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-02-20 09:45:29",
        "deleted_at": null
      },
      {
        "id": "4",
        "title": "BCST",
        "branch_id": null,
        "amount": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-03-15 15:55:29",
        "deleted_at": null
      },
      {
        "id": "5",
        "title": "BCHP",
        "branch_id": null,
        "amount": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-03-15 15:55:38",
        "deleted_at": null
      },
      {
        "id": "6",
        "title": "BSFP",
        "branch_id": null,
        "amount": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-03-15 15:55:56",
        "deleted_at": null
      },
      {
        "id": "7",
        "title": "BSSP",
        "branch_id": null,
        "amount": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-03-15 15:56:06",
        "deleted_at": null
      },
      {
        "id": "8",
        "title": "BMAC",
        "branch_id": null,
        "amount": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-03-15 15:56:17",
        "deleted_at": null
      },
      {
        "id": "9",
        "title": "BAHP",
        "branch_id": null,
        "amount": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-03-15 15:56:30",
        "deleted_at": null
      },
      {
        "id": "10",
        "title": "BSTC",
        "branch_id": null,
        "amount": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null,
        "updated_at": null,
        "created_at": "2025-03-15 15:56:42",
        "deleted_at": null
      }
    ];
    isCourseLoading = false;
    update();
  }

  get_batches(branch_id, course_id) async {
    print("called------");
    // var data = await CRMApiBaseHandler.fet_batches(branch_id, course_id);
    // batches = data['data']['batches'];
    batches = [];
    isCourseLoading = false;
    update();
  }

  bool addbtn_press = false;

  get_enroll_a_course() async {
    addbtn_press = true;
    update();

    var data = await CRMApiBaseHandler.fet_enroll_course(
      sel_branch.toString(),
      sel_course.toString(),
      selectedCandidate['id'].toString(),
      sel_batch.toString(),
    );

    addbtn_press = false;
    update();

    if (data['status'].toString() == "true") {
      toast_success(data['message'].toString());
      Get.back();
    } else {
      toast_error(data['message'].toString());
    }
  }

  String? sel_branch, sel_course, sel_batch;
}
