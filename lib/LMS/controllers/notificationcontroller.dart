import 'package:carousel_slider/carousel_slider.dart';
import 'package:edutalim/LMS/api/main_apis.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../views/dashboard/common/notifications.dart';

class Notificationcontroller extends GetxController {
  List notifications = [
    {
      "id": "1",
      "title": "New Notification",
      "description": "Time to Complete Your Classes.",
      "date": "01 April 2025 09:30 AM",
    },
    {
      "id": "2",
      "title": "New Notification",
      "description": "Time to Complete Your Classes.",
      "date": "01 April 2025 09:30 AM",
    },
    {
      "id": "3",
      // "title": "Time to Complete Your Classes",
      "title": "New Notification",
      "description": "Time to Complete Your Classes.",
      "date": "01 April 2025 09:30 AM",
    },
  ];

  // List notifications = [];
  bool isloading = true;

  get_notification() async {
    isloading = true;
    var data = await ApiBaseHandler.notificationsList();
    notifications = data['data'];
    isloading = false;
    update();
  }

  @override
  void onInit() {
    super.onInit();
    get_notification();
  }
}
