import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:edutalim/LMS/views/dashboard/feed/reel_page.dart';
import 'package:edutalim/LMS/views/dashboard/home/<USER>';
import 'package:edutalim/LMS/views/dashboard/leaderboard/leaderboard.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:get_storage/get_storage.dart';
import '../../../controllers/feedcontroller.dart';
import '../../../controllers/homecontroller.dart';
import '../common/main_drawer.dart';
import '../common/notifications.dart';
import 'course_details.dart';
import 'local_widget/course_card.dart';

class home extends StatefulWidget {
  const home({super.key});

  @override
  State<home> createState() => _homeState();
}

class _homeState extends State<home> {
  Homecontroller controller = Get.put(Homecontroller());
  Feedcontroller feed_controller = Get.put(Feedcontroller());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    controller.get_homedata();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Homecontroller>(builder: (controller) {
      return controller.isHomeLoading
          ? loader()
          : Scaffold(
              key: controller.scaffoldkey,
              backgroundColor: bgColor,
              body: Container(
                color: bgColor,
                child: controller.isHomeLoading
                    ? loader()
                    : SingleChildScrollView(
                        physics: ScrollPhysics(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AppBar(
                              backgroundColor: bgColor,
                              surfaceTintColor: bgColor,
                              leading: GestureDetector(
                                onTap: () {
                                  controller.scaffoldkey.currentState!.openDrawer();
                                },
                                child: Container(
                                  color: Colors.transparent,
                                  padding: EdgeInsets.all(15),
                                  child: SvgPicture.asset('assets/icons/hamburger.svg', colorFilter: ColorFilter.mode(textblackColor, BlendMode.srcIn)),
                                ),
                              ),
                              title: Image.asset('assets/logo/logo_horizontal.png', width: Get.width / 4),
                              // centerTitle: true,
                              actions: [
                                GestureDetector(
                                  onTap: () {
                                    Get.to(() => LeaderBoardScreen(title: "Leaderboard"));
                                  },
                                  child: Container(color: Colors.transparent, padding: EdgeInsets.all(15), child: Image.asset('assets/icons/analysis.png')),
                                ),
                                // SizedBox(width: 10,),
                                GestureDetector(
                                  onTap: () {
                                    Get.to(() => notifications());
                                  },
                                  child: Container(
                                    color: Colors.transparent,
                                    padding: EdgeInsets.all(15),
                                    child: SvgPicture.asset('assets/icons/notification.svg', colorFilter: ColorFilter.mode(textblackColor, BlendMode.srcIn)),
                                  ),
                                ),
                                SizedBox(width: 7),
                              ],
                            ),
                            SizedBox(height: Get.height * .015),
                            Column(
                              children: [
                                SizedBox(height: Get.height * .001),
                                Container(
                                  color: Colors.transparent,
                                  child: CarouselSlider.builder(
                                    carouselController: controller.carouselcontroller,
                                    itemCount: controller.banners.length,
                                    itemBuilder: (context, index, ind) {
                                      Map data = controller.banners[index];
                                      return GestureDetector(
                                        onTap: () {},
                                        child: SizedBox(
                                          width: Get.width * .9,
                                          height: Get.height * 0.18,
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(10),
                                            child: CachedNetworkImage(
                                              imageUrl: data['file_url'].toString(),
                                              fit: BoxFit.cover,
                                              errorWidget: (a, b, c) => Image.asset('assets/images/placeholder_banner.webp', fit: BoxFit.cover),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                    options: CarouselOptions(
                                      height: 140,
                                      viewportFraction: 0.9,
                                      autoPlay: true,
                                      autoPlayInterval: Duration(seconds: 3),
                                      autoPlayAnimationDuration: Duration(milliseconds: 800),
                                      autoPlayCurve: Curves.fastOutSlowIn,
                                      enlargeCenterPage: true,
                                      scrollDirection: Axis.horizontal,
                                      onPageChanged: (indexx, reason) {
                                        setState(() {
                                          controller.carouselindex = indexx;
                                        });
                                      },
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: controller.banners.asMap().entries.map<Widget>((entry) {
                                    return AnimatedOpacity(
                                      opacity: 1,
                                      duration: Duration(seconds: 1),
                                      child: AnimatedContainer(
                                        duration: Duration(seconds: 1),
                                        width: controller.carouselindex == entry.key ? 30 : 30.0,
                                        height: 5.0,
                                        margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                                        decoration: BoxDecoration(
                                          color: controller.carouselindex == entry.key ? secondaryColor : Color(0xFFD8D8D8),
                                          borderRadius: BorderRadius.circular(100),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ],
                            ),
                            SizedBox(height: 30),
                            Container(
                              decoration: BoxDecoration(borderRadius: BorderRadius.vertical(top: Radius.circular(32)), color: textwhiteColor),
                              child: Column(
                                children: [
                                  SizedBox(height: 20),

                                  if (controller.reels.isNotEmpty)
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                children: [
                                                  Image.asset("assets/icons/video.png", height: Get.width / 20),
                                                  SizedBox(width: 5),
                                                  Text("SHORT VIDEO", style: TextStyle(fontWeight: FontWeight.w700, fontFamily: font_semibold, fontSize: Get.width / 25)),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        Column(
                                          children: [
                                            SizedBox(height: 10),
                                            Container(
                                              color: Colors.transparent,
                                              child: SizedBox(
                                                height: 200,
                                                child: ListView.builder(
                                                  scrollDirection: Axis.horizontal,
                                                  shrinkWrap: true,
                                                  itemCount: controller.reels.length,
                                                  padding: EdgeInsets.symmetric(horizontal: 10),
                                                  itemBuilder: (context, index) {
                                                    var data = controller.reels[index];
                                                    return GestureDetector(
                                                      onTap: () {
                                                        Get.to(() => reel_page(data: controller.reels, index: index));
                                                      },
                                                      child: Container(
                                                        margin: EdgeInsets.only(left: index == 0 ? 5 : 5, right: index == controller.reels.length - 1 ? 5 : 5),
                                                        child: Stack(
                                                          children: [
                                                            ClipRRect(
                                                                borderRadius: BorderRadius.all(Radius.circular(10)),
                                                                child: CachedNetworkImage(
                                                                    imageUrl: data['thumbnail_url'].toString(),
                                                                    errorWidget: (_, __, ____) {
                                                                      return Image.asset("assets/logo/logo.png");
                                                                    },
                                                                    width: Get.width / 3.1,
                                                                    height: 240,
                                                                    fit: BoxFit.cover)),
                                                            Positioned(
                                                              left: 0,
                                                              right: 0,
                                                              top: 0,
                                                              bottom: 0,
                                                              child: Container(
                                                                decoration: BoxDecoration(
                                                                  borderRadius: BorderRadius.all(Radius.circular(10)),
                                                                  gradient: LinearGradient(
                                                                      colors: [textblackColor, textblackColor.withValues(alpha: 0.02), Colors.transparent],
                                                                      begin: Alignment.bottomCenter,
                                                                      end: Alignment.topCenter,
                                                                      stops: [0, 0.6, 1]),
                                                                ),
                                                                alignment: Alignment.bottomLeft,
                                                                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                                                child: Text(
                                                                  data['title'].toString(),
                                                                  style: TextStyle(color: textwhiteColor, fontSize: Get.width / 36, overflow: TextOverflow.ellipsis),
                                                                  maxLines: 2,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 10)
                                      ],
                                    ),

                                  SizedBox(height: 20),
                                  if (controller.secondary_banners.isNotEmpty)
                                    Column(
                                      children: [
                                        SizedBox(height: Get.height * .01),
                                        Container(
                                          color: Colors.transparent,
                                          child: CarouselSlider.builder(
                                              carouselController: controller.carouselcontroller2,
                                              itemCount: controller.secondary_banners.length,
                                              itemBuilder: (context, index, ind) {
                                                Map data = controller.secondary_banners[index];
                                                return GestureDetector(
                                                  onTap: () {},
                                                  child: SizedBox(
                                                    width: Get.width * .9,
                                                    child: ClipRRect(
                                                      borderRadius: BorderRadius.circular(10),
                                                      child: CachedNetworkImage(
                                                        imageUrl: data['file_url'].toString(),
                                                        fit: BoxFit.cover,
                                                        errorWidget: (a, b, c) {
                                                          return Image.asset('assets/images/placeholder_banner.webp', fit: BoxFit.cover);
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              },
                                              options: CarouselOptions(
                                                  height: 170,
                                                  viewportFraction: 0.8,
                                                  initialPage: 0,
                                                  enableInfiniteScroll: true,
                                                  reverse: false,
                                                  autoPlay: true,
                                                  autoPlayInterval: Duration(seconds: 5),
                                                  autoPlayAnimationDuration: Duration(milliseconds: 1200),
                                                  autoPlayCurve: Curves.fastOutSlowIn,
                                                  enlargeCenterPage: true,
                                                  scrollDirection: Axis.horizontal,
                                                  onPageChanged: (indexx, reason) {
                                                    setState(() {
                                                      controller.carouselindex = indexx;
                                                    });
                                                  })),
                                        ),
                                      ],
                                    ),

                                  SizedBox(height: 20),
                                  //eduthalim

                                  if (controller.categories['edutalim']['universities'].isNotEmpty)
                                    Column(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  ClipRRect(
                                                    borderRadius: BorderRadius.circular(1000),
                                                    child: CachedNetworkImage(
                                                      height: Get.width / 10,
                                                      width: Get.width / 10,
                                                      imageUrl: controller.categories['edutalim']['thumbnail'].toString(),
                                                      fit: BoxFit.cover,
                                                      errorWidget: (_, __, ___) {
                                                        return Image.asset("assets/logo/logo_icon.png", height: Get.width / 15);
                                                      },
                                                    ),
                                                  ),
                                                  SizedBox(width: 5),
                                                  Text(controller.categories['edutalim']['title'].toString(), style: TextStyle(fontWeight: FontWeight.w700, fontFamily: font_semibold, fontSize: Get.width / 25)),
                                                ],
                                              ),
                                              // GestureDetector(
                                              //   onTap: (){
                                              //     // Get.to(all_recommended_courses());
                                              //   },
                                              //   child: Container(
                                              //     color: Colors.transparent,
                                              //     child: Text(
                                              //       "View all >",
                                              //       style: TextStyle(fontWeight: FontWeight.w500, fontSize: Get.width / 28, color: secondaryColor),
                                              //     ),
                                              //   ),
                                              // ),
                                            ],
                                          ),
                                        ),
                                        Divider(color: textblackColor.withOpacity(0.08)),
                                        // SizedBox(height: 8,),
                                        SizedBox(
                                          height: Get.height * .26,
                                          child: ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            shrinkWrap: true,
                                            itemCount: controller.categories['edutalim']['universities'].length,
                                            itemBuilder: (context, index) {
                                              var data = controller.categories['edutalim']['universities'][index];

                                              // log(data.toString());

                                              return GestureDetector(
                                                onTap: () {
                                                  Get.to(() => university_details(university_id: data['id'].toString()));
                                                },
                                                child: Container(
                                                  width: Get.width / 2.1,
                                                  margin: EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                                                  decoration: BoxDecoration(color: textwhiteColor, borderRadius: BorderRadius.circular(10), boxShadow: [
                                                    BoxShadow(color: textblackColor.withOpacity(0.1), blurRadius: 8.0, spreadRadius: 2),
                                                  ]),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      Stack(
                                                        children: [
                                                          Container(
                                                            margin: EdgeInsets.only(bottom: 20),
                                                            child: ClipRRect(
                                                              borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
                                                              // child: Image.network(
                                                              child: Image.network(
                                                                // courses[index]['img'].toString(),
                                                                data['thumbnail'].toString(),
                                                                fit: BoxFit.cover,
                                                                errorBuilder: (a, b, c) {
                                                                  return Image.asset(
                                                                    index % 3 == 0
                                                                        ? 'assets/temp_images/edutalim1.png'
                                                                        : index % 3 == 1
                                                                            ? "assets/temp_images/edutalim2.png"
                                                                            : 'assets/temp_images/edutalim3.png',
                                                                    fit: BoxFit.cover,
                                                                  );
                                                                },
                                                              ),
                                                            ),
                                                          ),
                                                          Positioned(
                                                            left: 10,
                                                            bottom: 0,
                                                            child: ClipRRect(
                                                              borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
                                                              // child: Image.network(
                                                              child: Image.network(
                                                                // courses[index]['img'].toString(),
                                                                data['image'].toString(),
                                                                height: Get.height * .06,
                                                                fit: BoxFit.cover,
                                                                errorBuilder: (a, b, c) {
                                                                  return Image.asset(height: Get.height * .06, 'assets/images/placeholder_square.png', fit: BoxFit.cover);
                                                                },
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Container(
                                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                                        child: Column(
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                          children: [
                                                            const SizedBox(height: 5),
                                                            Container(
                                                              padding: EdgeInsets.symmetric(vertical: 3),
                                                              child: Text(data['title'].toString(), style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 29), maxLines: 1),
                                                            ),
                                                            Container(
                                                              padding: EdgeInsets.symmetric(vertical: 1),
                                                              child:
                                                                  Text(data['short_description'].toString(), style: TextStyle(color: textblackColor, fontWeight: FontWeight.w500, fontSize: Get.width / 32), maxLines: 1),
                                                            ),
                                                            Container(
                                                              padding: EdgeInsets.symmetric(vertical: 4),
                                                              child: Text(
                                                                data['courses_offered'] ?? '',
                                                                style: TextStyle(color: Color(0xFF3B7377), fontWeight: FontWeight.w600, fontSize: Get.width / 35),
                                                                maxLines: 1,
                                                                overflow: TextOverflow.ellipsis,
                                                              ),
                                                            ),
                                                            SizedBox(height: 10),
                                                            Container(
                                                              decoration:
                                                                  BoxDecoration(color: secondarylightColor.withValues(alpha: .2), borderRadius: BorderRadius.circular(5), border: Border.all(color: secondarylightColor)),
                                                              width: Get.width,
                                                              alignment: Alignment.center,
                                                              padding: EdgeInsets.all(8),
                                                              //Explore
                                                              child: Text('View Course', style: TextStyle(color: secondaryColor, fontWeight: FontWeight.w600)),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),

                                        SizedBox(height: 20),
                                      ],
                                    ),

                                  //
                                  //fintalim
                                  if (controller.categories['fintalim']['courses'].isNotEmpty)
                                    Column(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 10),
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    height: Get.width / 10,
                                                    width: Get.width / 10,
                                                    decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(1000),
                                                      // border: Border.all(color: Colors.grey),
                                                      boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.1),blurRadius: 5)]
                                                    ),
                                                    child: ClipRRect(
                                                      borderRadius: BorderRadius.circular(1000),
                                                      child: CachedNetworkImage(
                                                        height: Get.width / 10,
                                                        width: Get.width / 10,
                                                        imageUrl: controller.categories['fintalim']['thumbnail'].toString(),
                                                        fit: BoxFit.cover,
                                                        errorWidget: (_, __, ___) {
                                                          return Image.asset("assets/logo/fintalim_logo.png", height: Get.width / 15);
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 5),
                                                  Text(
                                                    controller.categories['fintalim']['title'].toString(),
                                                    style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 22),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(padding: EdgeInsets.symmetric(vertical: 12), child: Divider(color: textblackColor.withOpacity(0.08))),
                                        SizedBox(
                                          // color: Colors.yellow,
                                          height: GetStorage().read('pay_version').toString() == "false" ? 225 : 190,
                                          child: ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            // padding: EdgeInsets.all(0),
                                            physics: ScrollPhysics(),
                                            shrinkWrap: true,
                                            // itemCount: courses.length,
                                            itemCount: controller.categories['fintalim']['courses'].length,
                                            padding: EdgeInsets.symmetric(horizontal: 10),
                                            itemBuilder: (BuildContext context, int index) {
                                              var data = controller.categories['fintalim']['courses'][index];
                                              return GestureDetector(
                                                  onTap: () {
                                                    Get.to(() => course_details(course_id: data['id'].toString()));
                                                  },
                                                  child: CourseCard(
                                                    data: data,
                                                    textwhiteColor: textwhiteColor,
                                                    textblackColor: textblackColor,
                                                    secondarylightColor: secondarylightColor,
                                                  ));
                                            },
                                          ),
                                        ),
                                        SizedBox(
                                          height: 20,
                                        ),
                                      ],
                                    ),

                                  //skilshare
                                  if (controller.categories['skilshore']['courses'].isNotEmpty)
                                    Column(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 10),
                                        Container(
                                          width: Get.width,
                                          alignment: Alignment.centerLeft,
                                          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Row(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius: BorderRadius.circular(1000),
                                                    child: CachedNetworkImage(
                                                      height: Get.width / 10,
                                                      width: Get.width / 10,
                                                      imageUrl: controller.categories['skilshore']['thumbnail'].toString(),
                                                      fit: BoxFit.cover,
                                                      errorWidget: (_, __, ___) {
                                                        return Image.asset("assets/logo/fintalim_logo.png", height: Get.width / 15);
                                                      },
                                                    ),
                                                  ),
                                                  SizedBox(width: 5),
                                                  Text(controller.categories['skilshore']['title'].toString(), style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 22)),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(padding: EdgeInsets.symmetric(vertical: 12), child: Divider(color: textblackColor.withOpacity(0.08))),
                                        SizedBox(
                                          width: Get.width,
                                          // color: Colors.yellow,
                                          height: GetStorage().read('pay_version').toString() == "false" ? 220 : 190,
                                          child: ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            // padding: EdgeInsets.all(0),
                                            physics: ScrollPhysics(),
                                            shrinkWrap: true,
                                            // itemCount: courses.length,
                                            itemCount: controller.categories['skilshore']['courses'].length,
                                            padding: EdgeInsets.symmetric(horizontal: 10),
                                            itemBuilder: (BuildContext context, int index) {
                                              var data = controller.categories['skilshore']['courses'][index];
                                              return GestureDetector(
                                                  onTap: () {
                                                    Get.to(() => course_details(course_id: data['id'].toString()));
                                                  },
                                                  child: CourseCard(
                                                    data: data,
                                                    textwhiteColor: textwhiteColor,
                                                    textblackColor: textblackColor,
                                                    secondarylightColor: secondarylightColor,
                                                  ));
                                            },
                                          ),
                                        ),
                                        SizedBox(height: 20),
                                      ],
                                    ),
                                  if (controller.testimonials.isNotEmpty)
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(height: 20),
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 15),
                                          child: Text("Testimonials", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 22)),
                                        ),
                                        SizedBox(
                                          height: 205,
                                          child: ListView.builder(
                                            physics: ScrollPhysics(),
                                            scrollDirection: Axis.horizontal,
                                            padding: EdgeInsets.all(0),
                                            shrinkWrap: true,
                                            itemCount: controller.testimonials.length,
                                            itemBuilder: (BuildContext context, int index) {
                                              Map data = controller.testimonials[index];
                                              bool isLongReview = data['content'].length > 50;
                                              return GestureDetector(
                                                onTap: () {
                                                  if (isLongReview) {
                                                    Get.dialog(
                                                      AlertDialog(
                                                        backgroundColor: Colors.white,
                                                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
                                                        content: Column(
                                                          mainAxisSize: MainAxisSize.min,
                                                          children: [
                                                            ClipRRect(
                                                              borderRadius: BorderRadius.circular(30),
                                                              child: Image.network(
                                                                data['profile_image'].toString(),
                                                                width: Get.width / 3,
                                                                height: Get.width / 3,
                                                                fit: BoxFit.cover,
                                                                errorBuilder: (a, b, c) {
                                                                  return Image.asset('assets/images/avatar_placeholder.png', width: Get.width / 3, height: Get.width / 3);
                                                                },
                                                              ),
                                                            ),
                                                            SizedBox(height: 10),
                                                            Text(data['student_name'].toString(), style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 25)),
                                                            SizedBox(height: 5),
                                                            Text(data['course'].toString() == 'null' ? '' : data['course'].toString(),
                                                                style: TextStyle(fontWeight: FontWeight.w400, fontSize: Get.width / 30, color: textblackColor.withOpacity(0.5))),
                                                            SizedBox(height: 10),
                                                            Text(data['content'].toString(), style: TextStyle(fontWeight: FontWeight.w400, fontSize: Get.width / 31, color: textblackColor.withOpacity(0.5))),
                                                          ],
                                                        ),
                                                      ),
                                                    );
                                                  }
                                                },
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(vertical: 12, horizontal: 5),
                                                  child: Container(
                                                    margin: EdgeInsets.only(left: index == 0 ? 10 : 2.5, right: index == 2 ? 10 : 2.5),
                                                    decoration: BoxDecoration(
                                                      color: textwhiteColor,
                                                      borderRadius: BorderRadius.circular(15),
                                                      boxShadow: [BoxShadow(color: Color(0xff715D9D).withOpacity(0.15), blurRadius: 10, spreadRadius: 1)],
                                                    ),
                                                    // padding: EdgeInsets.all(10),
                                                    width: Get.width / 1.5,
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      mainAxisAlignment: MainAxisAlignment.start,
                                                      children: [
                                                        Stack(
                                                          children: [
                                                            Container(
                                                              height: 55,
                                                              width: Get.width,
                                                              margin: EdgeInsets.only(bottom: 20),
                                                              decoration: BoxDecoration(
                                                                color: index % 2 == 0 ? Color(0xFFEFDFE5) : Color(0xFFD8EDF2),
                                                                borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
                                                              ),
                                                            ),
                                                            Positioned(
                                                              left: 10,
                                                              bottom: 0,
                                                              child: Container(
                                                                decoration: BoxDecoration(
                                                                  borderRadius: BorderRadius.circular(30),
                                                                  border: Border.all(color: textblackColor.withOpacity(0.15)),
                                                                ),
                                                                child: ClipRRect(
                                                                  borderRadius: BorderRadius.circular(30),
                                                                  child: Image.network(
                                                                    data['photo'].toString(),
                                                                    width: Get.width / 9,
                                                                    height: Get.width / 9,
                                                                    fit: BoxFit.cover,
                                                                    errorBuilder: (a, b, c) {
                                                                      return Image.asset('assets/images/avatar_placeholder.png', width: Get.width / 9, height: Get.width / 9);
                                                                    },
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        Row(
                                                          children: [
                                                            SizedBox(width: 10),
                                                            Column(
                                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              children: [
                                                                const SizedBox(height: 5),
                                                                SizedBox(
                                                                  width: Get.width / 2.4,
                                                                  child: Text(data['student_name'].toString(), style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 25), maxLines: 1),
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                        SizedBox(height: 10),
                                                        SingleChildScrollView(
                                                          child: Container(
                                                              padding: EdgeInsets.symmetric(horizontal: 10),
                                                              child: Text(isLongReview ? '${data['content'].toString().substring(0, 50)}...' : data['content'].toString(),
                                                                  style: TextStyle(fontWeight: FontWeight.w400, fontSize: Get.width / 31, color: textblackColor.withOpacity(0.5)),
                                                                  maxLines: 2,
                                                                  overflow: TextOverflow.ellipsis)),
                                                        ),
                                                        if (isLongReview)
                                                          Padding(
                                                            padding: const EdgeInsets.only(top: 3.0, bottom: 3, left: 2),
                                                            child: Container(padding: EdgeInsets.symmetric(horizontal: 10), child: Text('Read More', style: TextStyle(fontSize: Get.width / 32))),
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  SizedBox(height: 40),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
              drawer: main_drawer(),
            );
    });
  }
}
