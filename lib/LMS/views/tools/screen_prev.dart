// import 'dart:async';
// import 'package:dotted_border/dotted_border.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:lottie/lottie.dart';
// import 'package:screen_protector/screen_protector.dart';
//
// import '../../../components/constants.dart';
// import 'package:edutalim/components/utils.dart';
//
// class screen_rec_prev extends StatefulWidget {
//   @override
//   State<StatefulWidget> createState() => StartState();
// }
//
// class StartState extends State<screen_rec_prev> {
//
//
//   void _checkScreenRecording() async {
//     final isRecording = await ScreenProtector.isRecording();
//
//     if (isRecording) {
//       print("yes----recording-----");
//       // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
//       //   content: Text('Screen Recording...'),
//       // ));
//       // Get.to(()=>()=>screen_rec_prev());
//       toast_warning( 'Please stop the Screen recording, then click "Okay"');
//     }else{
//       print("not----recording-----");
//       Get.back();
//     }
//   }
//
//   empty(){}
//
//   @override
//   Widget build(BuildContext context) {
//     var size = MediaQuery.of(context).size;
//     return WillPopScope(
//         onWillPop: (){
//           return empty();
//         },
//         child: Scaffold(
//             backgroundColor: Color(0xfffbfbfb),
//             body: SingleChildScrollView(
//               physics: ScrollPhysics(),
//               child: Container(
//                   height: MediaQuery.of(context).size.height,
//                   width: size.width,
//                   decoration: BoxDecoration(
//                     color: primaryColor,
//                     //   image: DecorationImage(
//                     //       fit: BoxFit.fill,
//                     //       image: AssetImage('assets/bg/splash_bg_blue_nologo.png')
//                     //   )
//                   ),
//                   child: Container(
//                     padding: EdgeInsets.only(bottom: 30,top: 50,left: 20,right: 20),
//                     child: DottedBorder(
//                       borderType: BorderType.RRect,
//                       radius: Radius.circular(12),
//                       padding: EdgeInsets.all(6),
//                       color: Colors.white,
//                       strokeWidth: 5,
//                       child: Center(
//                           child: Stack(
//                             children: [
//
//                               Positioned(
//                                   left: 0,
//                                   right: 0,
//                                   child: Column(
//                                     children: [
//
//                                       SizedBox(height: Get.height/20,),
//
//                                       Container(
//                                         height: MediaQuery.of(context).size.height/18,
//                                         child: Image.asset("assets/logo/logo_android.png",),
//                                       ),
//
//                                       SizedBox(height: Get.height/7,),
//
//                                       Container(
//                                         width: Get.width/3,
//                                         child: Image.asset('assets/icons/cam_blocked.png',color: Colors.white,),
//                                       ),
//                                       SizedBox(height: 20,),
//                                       Container(
//                                         padding: EdgeInsets.symmetric(horizontal: 10),
//                                         child: Text("Screenshot Blocked",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.white,fontSize: Get.width/17),textAlign: TextAlign.center,),
//                                       ),
//
//                                       SizedBox(height: 20,),
//
//                                       Container(
//                                         padding: EdgeInsets.symmetric(horizontal: 10),
//                                         child: Text("Its look like you tried to take Screenshot or recording. $appName Prevented Both Screenshot & Screen recording.",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.white,fontSize: Get.width/28),textAlign: TextAlign.center,),
//                                       ),
//
//                                     ],
//                                   )
//                               ),
//
//                               Positioned(
//                                   bottom: 40,
//                                   // left: MediaQuery.of(context).size.width/2.5,
//                                   left: 0,
//                                   right: 0,
//                                   child: Container(
//                                     child: Column(
//                                       children: [
//
//                                         Container(
//                                           height: MediaQuery.of(context).size.height/26,
//                                           child: Lottie.asset("assets/lotties/arrow_down.json",),
//                                         ),
//
//                                         SizedBox(height: 30,),
//
//                                         Container(
//                                           child: cust_elevatedbutton(
//                                             width: size.width/1.24,
//                                             onPressed: () {
//                                               // login();
//                                               _checkScreenRecording();
//                                             },
//                                             borderRadius: BorderRadius.circular(10),
//                                             height: 50,
//                                             gradient: LinearGradient(
//                                               colors: [Colors.white,Colors.white,],
//                                               begin: Alignment.centerLeft,
//                                               end: Alignment.centerRight,
//                                             ),
//                                             child:
//                                             Text('Okay',style: TextStyle(fontFamily: 'poppins_bold',color: Colors.black),),
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   )
//                               ),
//
//                             ],
//                           )
//                       ),
//                     ),
//                   )
//               ),
//             )
//         )
//     );
//   }
//
//
// }