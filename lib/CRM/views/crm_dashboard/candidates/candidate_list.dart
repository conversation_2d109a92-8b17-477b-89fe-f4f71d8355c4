import 'package:edutalim/CRM/api/apis.dart';
import 'package:edutalim/CRM/controller/candidatecontroller.dart';
import 'package:edutalim/CRM/controller/followupscontroller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../../../../components/constants.dart';
import '../../../../components/utils.dart';
import 'enrolled_courses.dart';

class candidate_list extends StatefulWidget {
  String member_id;

  candidate_list({required this.member_id});

  @override
  State<candidate_list> createState() => _leads_listState();
}

class _leads_listState extends State<candidate_list> {
  CandidateController controller = Get.put(CandidateController());

  @override
  void initState() {
    controller.member_id = widget.member_id;
    controller.get_leads();
    controller.get_branches();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CandidateController>(builder: (controller) {
      return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: true,
          iconTheme: IconThemeData(color: Colors.black),
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          title: Text("Candidates", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16)),
        ),
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Column(
            children: [
              Container(
                color: Colors.white,
                padding: EdgeInsets.only(bottom: 15, left: 10, right: 10, top: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width / 1.22,
                      child: TextField(
                        onTap: () {},
                        onChanged: (value) {
                          if (value == "") {
                            setState(() {
                              controller.filt_leadsdata = controller.leads;
                            });
                          } else {
                            controller.filterleads(value);
                          }
                        },
                        readOnly: false,
                        textInputAction: TextInputAction.search,
                        style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                        controller: controller.searchfld,
                        decoration: InputDecoration(
                          focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.black26, width: 0.5), borderRadius: BorderRadius.circular(10.0)),
                          enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.black26, width: 0.5), borderRadius: BorderRadius.circular(10.0)),
                          filled: true,
                          hintStyle: TextStyle(color: const Color(0xFFA1A3AB), fontFamily: "poppins_regular"),
                          hintText: "Search",
                          fillColor: Colors.white70,
                          contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                          suffixIcon: GestureDetector(
                            child: Container(color: Colors.transparent, padding: EdgeInsets.all(13), child: Image.asset('assets/icons/search2.png', width: 2, color: Colors.black38)),
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        if (controller.isLoading != true) {
                          filter_bottom_sheet();
                        }
                      },
                      icon: Icon(Icons.filter_alt, size: Get.width / 12, color: Colors.black54),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 5,
              ),
              controller.searchfld.text.isEmpty || controller.from_date.toString() != 'All' || controller.to_date.toString() != 'All'
                  ? Column(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          height: 60,
                          child: ListView(
                            scrollDirection: Axis.horizontal,
                            children: [
                              Container(
                                color: Colors.transparent,
                                child: Row(
                                  children: [
                                    Container(
                                        padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                                        margin: EdgeInsets.all(5),
                                        alignment: Alignment.center,
                                        color: Colors.white,
                                        child: Text("Total Students : ${controller.leads.length} ", style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600))),
                                  ],
                                ),
                              ),
                              controller.from_date.toString() == 'All'
                                  ? Container()
                                  : Container(
                                      color: Colors.transparent,
                                      child: Row(
                                        children: [
                                          Container(
                                              padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                                              margin: EdgeInsets.all(5),
                                              alignment: Alignment.center,
                                              color: Colors.white,
                                              child: Row(
                                                children: [
                                                  Text("From Date : ${controller.from_date} ", style: TextStyle(color: Colors.black)),
                                                  IconButton(
                                                      padding: EdgeInsets.all(0),
                                                      alignment: Alignment.center,
                                                      onPressed: () {
                                                        controller.from_date = 'All';
                                                        controller.get_leads();
                                                        controller.update();
                                                      },
                                                      icon: Icon(Icons.close))
                                                ],
                                              )),
                                        ],
                                      ),
                                    ),
                              controller.to_date.toString() == 'All'
                                  ? Container()
                                  : Container(
                                      color: Colors.transparent,
                                      child: Row(
                                        children: [
                                          Container(
                                              padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                                              margin: EdgeInsets.all(5),
                                              alignment: Alignment.center,
                                              color: Colors.white,
                                              child: Row(
                                                children: [
                                                  Text("To Date : ${controller.to_date} ", style: TextStyle(color: Colors.black)),
                                                  IconButton(
                                                      padding: EdgeInsets.all(0),
                                                      alignment: Alignment.center,
                                                      onPressed: () {
                                                        controller.to_date = 'All';
                                                        controller.get_leads();
                                                        controller.update();
                                                      },
                                                      icon: Icon(Icons.close))
                                                ],
                                              )),
                                        ],
                                      ),
                                    ),
                            ],
                          ),
                        ),
                        SizedBox(height: 5),
                      ],
                    )
                  : Container(),
              Expanded(
                child: controller.isLoading
                    ? loader()
                    : controller.leads.isEmpty
                        ? Container(
                            width: Get.width,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Image.asset('assets/icons/no_data.png', width: Get.width / 4),
                                SizedBox(height: 20),
                                Text("No Data Found", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16)),
                              ],
                            ),
                          )
                        : ListView.builder(
                            physics: ScrollPhysics(),
                            itemCount: controller.filt_leadsdata.length,
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return Container(
                                decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10)), color: Colors.white),
                                margin: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 13),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          Container(
                                            width: Get.width / 8,
                                            height: Get.width / 8,
                                            decoration: BoxDecoration(
                                              border: Border.all(color: primaryColor, width: 2),
                                              borderRadius: BorderRadius.circular(50),
                                            ),
                                            child: ClipRRect(
                                              borderRadius: BorderRadius.circular(50),
                                              // child: Image.network(leads[index]['profile_picture'].toString(),fit: BoxFit.cover,
                                              child: Image.network(
                                                controller.filt_leadsdata[index]['profile_picture'].toString(),
                                                fit: BoxFit.cover,
                                                errorBuilder: (a, b, c) {
                                                  return Image.asset('assets/images/avater_placeholder.jpg');
                                                },
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          Container(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                Row(
                                                  children: [
                                                    SizedBox(
                                                      width: Get.width / 1.8,
                                                      child: Text(
                                                        controller.filt_leadsdata[index]['name'].toString(),
                                                        style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: commontextColor, overflow: TextOverflow.ellipsis),
                                                      ),
                                                    ),
                                                    Container(
                                                      child: PopupMenuButton<int>(
                                                        itemBuilder: (context) => [
                                                          // PopupMenuItem(
                                                          //   value: 1,
                                                          //   child: Container(
                                                          //     child: Row(
                                                          //       children: [
                                                          //         Icon(Icons.edit),
                                                          //         SizedBox(width: 10),
                                                          //         Text("Edit", style: TextStyle(color: Colors.black)),
                                                          //       ],
                                                          //     ),
                                                          //   ),
                                                          // ),
                                                          // PopupMenuItem(
                                                          //   value: 2,
                                                          //   child: Container(
                                                          //     child: Row(
                                                          //       children: [
                                                          //         Icon(Icons.sync),
                                                          //         SizedBox(width: 10),
                                                          //         Text("Update", style: TextStyle(color: Colors.black)),
                                                          //       ],
                                                          //     ),
                                                          //   ),
                                                          // ),
                                                          // PopupMenuItem(
                                                          //   value: 3,
                                                          //   child: Container(
                                                          //     child: Row(
                                                          //       children: [
                                                          //         Icon(Icons.history),
                                                          //         SizedBox(width: 10),
                                                          //         Text("History", style: TextStyle(color: Colors.black)),
                                                          //       ],
                                                          //     ),
                                                          //   ),
                                                          // ),
                                                          PopupMenuItem(
                                                            value: 4,
                                                            child: Row(
                                                              children: [
                                                                Icon(Icons.subject),
                                                                SizedBox(width: 10),
                                                                Text("Enrolled Course", style: TextStyle(color: Colors.black)),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                        // offset: Offset(0, 100),
                                                        color: Colors.white,
                                                        icon: Container(
                                                          height: 36,
                                                          width: 48,
                                                          alignment: Alignment.centerRight,
                                                          child: Icon(Icons.more_vert, color: Colors.black),
                                                        ),
                                                        elevation: 5,
                                                        padding: EdgeInsets.all(0),
                                                        onSelected: (value) {
                                                          // if(value == 1)
                                                          // {
                                                          //
                                                          //   // Get.to(candidate_add(action: 'edit', data: [leads[index]],member_id: widget.member_id.toString()))!.then((value){
                                                          //   Get.to(candidate_add(action: 'edit', data: [filt_leadsdata[index]],member_id: widget.member_id.toString()))!.then((value){
                                                          //     get_leads();
                                                          //   });
                                                          //
                                                          // }
                                                          // else if(value == 2)
                                                          // {
                                                          //
                                                          //   // Get.to(candidate_add(action: 'update', data: [leads[index]],member_id: widget.member_id.toString()))!.then((value){
                                                          //   Get.to(candidate_add(action: 'update', data: [filt_leadsdata[index]],member_id: widget.member_id.toString()))!.then((value){
                                                          //     get_leads();
                                                          //   });
                                                          //
                                                          // }
                                                          // else if(value == 3)
                                                          // {
                                                          //
                                                          //   // 7994742450
                                                          //
                                                          //   // Get.to(()=>candidate_history(data: [leads[index]],));
                                                          //   Get.to(()=>candidate_history(data: [filt_leadsdata[index]],));
                                                          //
                                                          // }

                                                          // else
                                                          if (value == 4) {
                                                            Get.to(() => enrolled_courses(
                                                                  data: controller.filt_leadsdata[index],
                                                                ));
                                                          }
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                Row(
                                                  children: [
                                                    SizedBox(
                                                      width: Get.width / 5,
                                                      child: Text("Phone", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                    ),
                                                    SizedBox(
                                                      width: Get.width / 2.5,
                                                      child: Text(
                                                        controller.filt_leadsdata[index]['phone'].toString() == "null" ? ":  --" : ":  ${controller.leads[index]['phone']}",
                                                        style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                Row(
                                                  children: [
                                                    SizedBox(
                                                      width: Get.width / 5,
                                                      child: Text("Whatsapp", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                    ),
                                                    SizedBox(
                                                      width: Get.width / 2.5,
                                                      child: Text(
                                                        controller.filt_leadsdata[index]['whatsapp'].toString() == "null" ? ":  --" : ":  ${controller.leads[index]['whatsapp']}",
                                                        style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                Row(
                                                  children: [
                                                    SizedBox(
                                                      width: Get.width / 5,
                                                      child: Text("Email", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                    ),
                                                    SizedBox(
                                                      width: Get.width / 2.5,
                                                      child: Text(
                                                        controller.filt_leadsdata[index]['email'].toString() == "null" ? ":  --" : ":  ${controller.leads[index]['email']}",
                                                        style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    // Divider(),

                                    // Container(
                                    //   // color: Colors.yellowAccent,
                                    //   // width: Get.width,
                                    //   padding: EdgeInsets.symmetric(horizontal: 20),
                                    //   child: Table(
                                    //       children: [
                                    //         TableRow(
                                    //             children: [
                                    //               Container(
                                    //                 padding: EdgeInsets.symmetric(vertical: 5),
                                    //                 child: Text("Qualification",style: TextStyle(color: Colors.black54),),
                                    //               ),
                                    //               Container(
                                    //                 padding: EdgeInsets.symmetric(vertical: 5),
                                    //                 child: Text(leads[index]['qualification'].toString(),style: TextStyle(color: Colors.black),),
                                    //               ),
                                    //             ]),
                                    //
                                    //         TableRow(
                                    //             children: [
                                    //               Container(
                                    //                 padding: EdgeInsets.symmetric(vertical: 5),
                                    //                 child: Text("Age",style: TextStyle(color: Colors.black54),),
                                    //               ),
                                    //               Container(
                                    //                 padding: EdgeInsets.symmetric(vertical: 5),
                                    //                 child:  Text(leads[index]['age'].toString(),style: TextStyle(color: Colors.black),),
                                    //               ),
                                    //             ]
                                    //         ),
                                    //
                                    //         TableRow(
                                    //             children: [
                                    //               Container(
                                    //                 padding: EdgeInsets.symmetric(vertical: 5),
                                    //                 child: Text("Place",style: TextStyle(color: Colors.black54),),
                                    //               ),
                                    //               Container(
                                    //                 padding: EdgeInsets.symmetric(vertical: 5),
                                    //                 child:  Text(leads[index]['place'].toString(),style: TextStyle(color: Colors.black),),
                                    //               ),
                                    //             ]
                                    //         ),
                                    //
                                    //
                                    //         TableRow(
                                    //           children: [
                                    //             Container(
                                    //               padding: EdgeInsets.symmetric(vertical: 5),
                                    //               child: Text("Lead Status",style: TextStyle(color: Colors.black54),),
                                    //             ),
                                    //             Container(
                                    //               padding: EdgeInsets.symmetric(vertical: 5),
                                    //               child:  Text(leads[index]['lead_status_label'].toString(),style: TextStyle(
                                    //                   color: leads[index]['lead_status'].toString() == "pending" ? Colors.orange
                                    //                       : leads[index]['lead_status'].toString() == "follow_up" ? Colors.blue
                                    //                       : leads[index]['lead_status'].toString() == "converted" ? Colors.black
                                    //                       : leads[index]['lead_status'].toString() == "interested" ? Colors.green
                                    //                       : leads[index]['lead_status'].toString() == "not_interested" ? Colors.black54
                                    //                       : leads[index]['lead_status'].toString() == "qualified" ? Colors.green
                                    //                       : leads[index]['lead_status'].toString() == "black_list" ? Colors.red
                                    //                       : Colors.black54,
                                    //                   fontFamily: 'poppins_bold'),),
                                    //             ),
                                    //           ],
                                    //         ),
                                    //
                                    //       ]
                                    //   ),
                                    // ),

                                    if (controller.leads[index]['lead_remarks'].toString().trim() != "null")
                                      Container(
                                          margin: EdgeInsets.symmetric(horizontal: 20),
                                          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                          width: Get.width,
                                          decoration: BoxDecoration(color: Colors.grey.withValues(alpha: 0.2), borderRadius: BorderRadius.circular(8)),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              SizedBox(height: 8),
                                              Text("Remark :-".trim(), style: TextStyle(color: Colors.black54, fontStyle: FontStyle.italic)),
                                              SizedBox(height: 5),
                                              Text(controller.leads[index]['lead_remarks'].toString().trim(), style: TextStyle(color: Colors.black, fontSize: 14, fontFamily: 'poppins_regular')),
                                            ],
                                          )),

                                    Divider(color: Colors.black.withValues(alpha: 0.1)),

                                    Container(
                                        margin: EdgeInsets.symmetric(vertical: 12),
                                        child: IntrinsicHeight(
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: GestureDetector(
                                                  onTap: () {
                                                    var content = Uri.parse("Hi ${controller.leads[index]['name'].toString().trim()}");
                                                    launchURL("https://wa.me/+${controller.leads[index]['whatsapp']}?text=${content.toString().trim()}");
                                                  },
                                                  child: Container(
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Image.asset('assets/icons/whatsapp.png', width: Get.width / 17),
                                                        SizedBox(width: 6),
                                                        Text("Whatsapp", textAlign: TextAlign.center, style: TextStyle(color: Colors.green, fontFamily: "poppins_regular")),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              VerticalDivider(color: Colors.black12),
                                              Expanded(
                                                child: GestureDetector(
                                                  onTap: () {
                                                    launchURL('tel:${controller.leads[index]['phone']}');
                                                  },
                                                  child: Container(
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Icon(Icons.call, color: Colors.green, size: 20),
                                                        SizedBox(width: 6),
                                                        Text("Call", textAlign: TextAlign.center, style: TextStyle(color: Colors.green, fontFamily: "poppins_regular")),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )),
                                  ],
                                ),
                              );
                            },
                          ),
              ),
            ],
          ),
        ),
      );
    });
  }

  void fromDatePicker(ctx) {
    controller.follow_from_date.text = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.from_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    showCupertinoModalPopup(
        context: ctx,
        builder: (_) => GetBuilder<CandidateController>(builder: (controller) {
              return Container(
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                height: 410,
                child: Column(
                  children: [
                    Container(
                      width: Get.width,
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(color: Colors.lightBlueAccent, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                      child: Text(
                        'Scroll to select',
                        style: TextStyle(fontSize: 15, color: Colors.white, decoration: TextDecoration.none, fontFamily: ''),
                      ),
                    ),

                    SizedBox(
                      height: 290,
                      child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: DateTime.now(),
                          onDateTimeChanged: (val) {
                            controller.fromDate = val;
                            controller.follow_from_date.text = "${controller.fromDate!.day}-${controller.fromDate!.month}-${controller.fromDate!.year}";
                            controller.from_date = "${controller.fromDate!.day}-${controller.fromDate!.month}-${controller.fromDate!.year}";
                            controller.update();
                          }),
                    ),

                    // Close the modal
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CupertinoButton(
                          child: const Text('Cancel'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                        CupertinoButton(
                          child: const Text('OK'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }));
  }

  void toDatePicker(ctx) {
    controller.follow_to_date.text = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.to_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.update();
    showCupertinoModalPopup(
        context: ctx,
        builder: (_) => GetBuilder<CandidateController>(builder: (controller) {
              return Container(
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                height: 410,
                child: Column(
                  children: [
                    Container(
                      width: Get.width,
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(color: Colors.lightBlueAccent, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                      child: Text('Scroll to select', style: TextStyle(fontSize: 15, color: Colors.white, decoration: TextDecoration.none, fontFamily: '')),
                    ),

                    SizedBox(
                      height: 290,
                      child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: DateTime.now(),
                          onDateTimeChanged: (val) {
                            controller.toDate = val;
                            controller.follow_to_date.text = "${controller.toDate!.day}-${controller.toDate!.month}-${controller.toDate!.year}";
                            controller.to_date = "${controller.toDate!.day}-${controller.toDate!.month}-${controller.toDate!.year}";
                            controller.update();
                          }),
                    ),

                    // Close the modal
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CupertinoButton(
                          child: const Text('Cancel'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                        CupertinoButton(
                          child: const Text('OK'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }));
  }

  // filter bottom sheet sec
  void filter_bottom_sheet() {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      isScrollControlled: true,
      context: context,
      useSafeArea: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      builder: (builder) {
        return GetBuilder<CandidateController>(
          builder: (controller) {
            return Padding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Stack(
                children: [
                  Container(
                    constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height / 2.2),
                    child: Column(
                      children: [
                        SizedBox(height: 30),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Image.asset('assets/icons/filter.png', width: Get.width / 18, color: Colors.black),
                              SizedBox(width: 15),
                              Text("Filter", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 23, fontWeight: FontWeight.w600, color: Colors.black)),
                            ],
                          ),
                        ),
                        Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            physics: ScrollPhysics(),
                            child: Wrap(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      //  // -----------------------------sort  sec--------------------------------------------------------
                                      // Container(
                                      //      padding: EdgeInsets.symmetric(horizontal: 10),
                                      //      child: Column(
                                      //        crossAxisAlignment: CrossAxisAlignment.start,
                                      //        children: [
                                      //          Container(
                                      //            // padding: EdgeInsets.symmetric(horizontal: 15),
                                      //            // color: Colors.yellow,
                                      //              child: Text("Date",style: TextStyle(fontFamily: "poppins_regular",fontSize: MediaQuery.of(context).size.width/25,fontWeight: FontWeight.w500,color: Colors.black),textAlign: TextAlign.left,)
                                      //          ),
                                      //          SizedBox(height: 20,),
                                      //
                                      //          Container(
                                      //            child: Row(
                                      //              children: [
                                      //                Expanded(
                                      //                  child: Text("From date :",style: TextStyle(color: Colors.black54,fontSize: Get.width/34),),
                                      //                ),
                                      //                SizedBox(width: 12,),
                                      //                Expanded(
                                      //                  child: Text("To date :",style: TextStyle(color: Colors.black54,fontSize: Get.width/34),),
                                      //                ),
                                      //              ],
                                      //            ),
                                      //          ),
                                      //          SizedBox(height: 8,),
                                      //
                                      //          Container(
                                      //            child: Row(
                                      //              children: [
                                      //
                                      //                Expanded(
                                      //                  child: Container(
                                      //                    // height: 40,
                                      //                    child: TextField(
                                      //                      onChanged: (val){
                                      //                        // fetleadsby_type();
                                      //                      },
                                      //                      onTap: (){
                                      //                        print('helooooo.....');
                                      //                        _fromDatePicker(context);
                                      //                      },
                                      //                      readOnly: true,
                                      //                      style: TextStyle(
                                      //                          fontSize: 15.0,
                                      //                          height: 1.5,
                                      //                          color: Colors.black
                                      //                      ),
                                      //                      controller: follow_from_date,
                                      //                      decoration: InputDecoration(
                                      //                        focusedBorder: OutlineInputBorder(
                                      //                          borderSide: BorderSide(color: Colors.blue,width: 1),
                                      //                          borderRadius: BorderRadius.circular(10.0),
                                      //                        ),
                                      //                        enabledBorder: OutlineInputBorder(
                                      //                          borderSide: BorderSide(color: Colors.blue,width: 1),
                                      //                          borderRadius: BorderRadius.circular(10.0),
                                      //                        ),
                                      //                        filled: true,
                                      //                        hintStyle: TextStyle(color:  Colors.black,fontFamily: "poppins_regular"),
                                      //                        hintText: from_date.toString(),
                                      //                        fillColor: Colors.blueAccent.withValues(alpha: 0.2),
                                      //                        contentPadding: const EdgeInsets.symmetric(vertical: 10.0,horizontal: 20),
                                      //                        suffixIcon: from_date.toString() == "All" ? Container(width: 0,height: 0,) : IconButton(
                                      //                          onPressed: (){
                                      //                            print("rttested------");
                                      //                            setState(() {
                                      //                              follow_from_date.text = "All";
                                      //                              from_date = "All";
                                      //                            });
                                      //                            // fetleadsby_type();
                                      //                          },
                                      //                          icon: Icon(Icons.close,color: Colors.black,),
                                      //                        ),
                                      //                      ),
                                      //                    ),
                                      //                  ),
                                      //                ),
                                      //                SizedBox(width: 12,),
                                      //                Expanded(
                                      //                  child: Container(
                                      //                    // height: 40,
                                      //                    child: TextField(
                                      //                      onChanged: (val){
                                      //                        // fetleadsby_type();
                                      //                      },
                                      //                      onTap: (){
                                      //                        print('helooooo.....');
                                      //                        _toDatePicker(context);
                                      //                      },
                                      //                      readOnly: true,
                                      //                      style: TextStyle(
                                      //                          fontSize: 15.0,
                                      //                          height: 1.5,
                                      //                          color: Colors.black
                                      //                      ),
                                      //                      controller: follow_to_date,
                                      //                      decoration: InputDecoration(
                                      //                        focusedBorder: OutlineInputBorder(
                                      //                          borderSide: BorderSide(color: Colors.blue,width: 1),
                                      //                          borderRadius: BorderRadius.circular(10.0),
                                      //                        ),
                                      //                        enabledBorder: OutlineInputBorder(
                                      //                          borderSide: BorderSide(color: Colors.blue,width: 1),
                                      //                          borderRadius: BorderRadius.circular(10.0),
                                      //                        ),
                                      //                        filled: true,
                                      //                        hintStyle: TextStyle(color:  Colors.black,fontFamily: "poppins_regular"),
                                      //                        hintText: to_date.toString(),
                                      //                        fillColor: Colors.blueAccent.withValues(alpha: 0.2),
                                      //                        contentPadding: const EdgeInsets.symmetric(vertical: 10.0,horizontal: 20),
                                      //                        suffixIcon: to_date.toString() == "All" ? Container(width: 0,height: 0,) : IconButton(
                                      //                          onPressed: (){
                                      //                            print("rttested------");
                                      //                            setState(() {
                                      //                              follow_to_date.text = "All";
                                      //                              to_date = "All";
                                      //                            });
                                      //                            // fetleadsby_type();
                                      //                          },
                                      //                          icon: Icon(Icons.close,color: Colors.black,),
                                      //                        ),
                                      //                      ),
                                      //                    ),
                                      //                  ),
                                      //                ),
                                      //
                                      //              ],
                                      //            ),
                                      //          ),
                                      //
                                      //          SizedBox(height: 20,),
                                      //
                                      //
                                      //        ],
                                      //      ),
                                      //    ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text(
                                              "Branch",
                                              style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                              textAlign: TextAlign.left,
                                            ),
                                            SizedBox(height: 10),
                                            Container(
                                              width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_branch,
                                                    iconSize: 30,
                                                    dropdownColor: Colors.white,
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('- select a branch -'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_branch = newValue;
                                                      controller.update();
                                                    },
                                                    items: controller.branches.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['id'].toString(),
                                                        child: SizedBox(
                                                          width: MediaQuery.of(context).size.width / 1.6,
                                                          child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      // // -----------------------------sort  sec--------------------------------------------------------
                                      // Container(
                                      //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      //   child: Column(
                                      //     crossAxisAlignment: CrossAxisAlignment.start,
                                      //     children: [
                                      //       SizedBox(height: 10,),
                                      //       Container(
                                      //         // padding: EdgeInsets.symmetric(horizontal: 15),
                                      //         // color: Colors.yellow,
                                      //           child: Text("Status",style: TextStyle(fontFamily: "poppins_regular",fontSize: MediaQuery.of(context).size.width/25,fontWeight: FontWeight.w500,color: Colors.black),textAlign: TextAlign.left,)
                                      //       ),
                                      //       SizedBox(height: 10,),
                                      //
                                      //       Container(
                                      //         width: MediaQuery.of(context).size.width,
                                      //         decoration: BoxDecoration(
                                      //             borderRadius: BorderRadius.circular(10.0),
                                      //             border: Border.all(color: Colors.black26,width: 0.5),
                                      //             color: Colors.white
                                      //         ),
                                      //         child: DropdownButtonHideUnderline(
                                      //           child: ButtonTheme(
                                      //             alignedDropdown: true,
                                      //             child: DropdownButton<String>(
                                      //               value: sel_status,
                                      //               iconSize: 30,
                                      //               // icon: (null),
                                      //               style: TextStyle(
                                      //                 color: Colors.black54,
                                      //                 fontSize: 14,
                                      //               ),
                                      //               hint: Text('- select a status -'),
                                      //               onChanged: (newValue) async {
                                      //                 setState(() {
                                      //                   sel_status = newValue;
                                      //                 });
                                      //               },
                                      //               // items: alldata[0]['lead_status'].map<DropdownMenuItem<String>>((item) {
                                      //               items: alldata[0]['candidate_status'].map<DropdownMenuItem<String>>((item) {
                                      //                 return  DropdownMenuItem<String>(
                                      //                   child:  Container(
                                      //                     width: MediaQuery.of(context).size.width / 1.6,
                                      //                     child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                      //                   ),
                                      //                   value: item['id'].toString(),
                                      //                 );
                                      //               }).toList(),
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //
                                      //       SizedBox(height: 20,),
                                      //
                                      //
                                      //     ],
                                      //   ),
                                      // ),
                                      //
                                      //
                                      // // -----------------------------sort  sec--------------------------------------------------------
                                      // Container(
                                      //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      //   child: Column(
                                      //     crossAxisAlignment: CrossAxisAlignment.start,
                                      //     children: [
                                      //       SizedBox(height: 10,),
                                      //       Container(
                                      //         // padding: EdgeInsets.symmetric(horizontal: 15),
                                      //         // color: Colors.yellow,
                                      //           child: Text("Country",style: TextStyle(fontFamily: "poppins_regular",fontSize: MediaQuery.of(context).size.width/25,fontWeight: FontWeight.w500,color: Colors.black),textAlign: TextAlign.left,)
                                      //       ),
                                      //       SizedBox(height: 10,),
                                      //
                                      //       Container(
                                      //         width: MediaQuery.of(context).size.width,
                                      //         decoration: BoxDecoration(
                                      //             borderRadius: BorderRadius.circular(10.0),
                                      //             border: Border.all(color: Colors.black26,width: 0.5),
                                      //             color: Colors.white
                                      //         ),
                                      //         child: DropdownButtonHideUnderline(
                                      //           child: ButtonTheme(
                                      //             alignedDropdown: true,
                                      //             child: DropdownButton<String>(
                                      //               value: sel_country,
                                      //               iconSize: 30,
                                      //               // icon: (null),
                                      //               style: TextStyle(
                                      //                 color: Colors.black54,
                                      //                 fontSize: 14,
                                      //               ),
                                      //               hint: Text('- select a country -'),
                                      //               onChanged: (newValue) async {
                                      //                 setState(() {
                                      //                   sel_country = newValue;
                                      //                 });
                                      //               },
                                      //               items: alldata[0]['country'].map<DropdownMenuItem<String>>((item) {
                                      //                 return  DropdownMenuItem<String>(
                                      //                   child:  Container(
                                      //                     width: MediaQuery.of(context).size.width / 1.6,
                                      //                     child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                      //                   ),
                                      //                   value: item['id'].toString(),
                                      //                 );
                                      //               }).toList(),
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //
                                      //       SizedBox(height: 20,),
                                      //
                                      //
                                      //     ],
                                      //   ),
                                      // ),
                                      //
                                      //
                                      // // -----------------------------sort  sec--------------------------------------------------------
                                      // // Container(
                                      // //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      // //   child: Column(
                                      // //     crossAxisAlignment: CrossAxisAlignment.start,
                                      // //     children: [
                                      // //       SizedBox(height: 10,),
                                      // //       Container(
                                      // //         // padding: EdgeInsets.symmetric(horizontal: 15),
                                      // //         // color: Colors.yellow,
                                      // //           child: Text("University",style: TextStyle(fontFamily: "poppins_regular",fontSize: MediaQuery.of(context).size.width/25,fontWeight: FontWeight.w500,color: Colors.black),textAlign: TextAlign.left,)
                                      // //       ),
                                      // //       SizedBox(height: 10,),
                                      // //
                                      // //       Container(
                                      // //         width: MediaQuery.of(context).size.width,
                                      // //         decoration: BoxDecoration(
                                      // //             borderRadius: BorderRadius.circular(10.0),
                                      // //             border: Border.all(color: Colors.black26,width: 0.5),
                                      // //             color: Colors.white
                                      // //         ),
                                      // //         child: DropdownButtonHideUnderline(
                                      // //           child: ButtonTheme(
                                      // //             alignedDropdown: true,
                                      // //             child: DropdownButton<String>(
                                      // //               value: sel_university,
                                      // //               iconSize: 30,
                                      // //               // icon: (null),
                                      // //               style: TextStyle(
                                      // //                 color: Colors.black54,
                                      // //                 fontSize: 14,
                                      // //               ),
                                      // //               hint: Text('- select a university -'),
                                      // //               onChanged: (newValue) async {
                                      // //                 setState(() {
                                      // //                   sel_university = newValue;
                                      // //                 });
                                      // //               },
                                      // //               items: alldata[0]['university'].map<DropdownMenuItem<String>>((item) {
                                      // //                 return  DropdownMenuItem<String>(
                                      // //                   child:  Container(
                                      // //                     width: MediaQuery.of(context).size.width / 1.6,
                                      // //                     child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                      // //                   ),
                                      // //                   value: item['id'].toString(),
                                      // //                 );
                                      // //               }).toList(),
                                      // //             ),
                                      // //           ),
                                      // //         ),
                                      // //       ),
                                      // //
                                      // //       SizedBox(height: 20,),
                                      // //
                                      // //
                                      // //     ],
                                      // //   ),
                                      // // ),
                                      //
                                      //
                                      //
                                      // // -----------------------------sort  sec--------------------------------------------------------
                                      // Container(
                                      //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      //   child: Column(
                                      //     crossAxisAlignment: CrossAxisAlignment.start,
                                      //     children: [
                                      //       SizedBox(height: 10,),
                                      //       Container(
                                      //         // padding: EdgeInsets.symmetric(horizontal: 15),
                                      //         // color: Colors.yellow,
                                      //           child: Text("Source",style: TextStyle(fontFamily: "poppins_regular",fontSize: MediaQuery.of(context).size.width/25,fontWeight: FontWeight.w500,color: Colors.black),textAlign: TextAlign.left,)
                                      //       ),
                                      //       SizedBox(height: 10,),
                                      //
                                      //       Container(
                                      //         width: MediaQuery.of(context).size.width,
                                      //         decoration: BoxDecoration(
                                      //             borderRadius: BorderRadius.circular(10.0),
                                      //             border: Border.all(color: Colors.black26,width: 0.5),
                                      //             color: Colors.white
                                      //         ),
                                      //         child: DropdownButtonHideUnderline(
                                      //           child: ButtonTheme(
                                      //             alignedDropdown: true,
                                      //             child: DropdownButton<String>(
                                      //               value: sel_source,
                                      //               iconSize: 30,
                                      //               // icon: (null),
                                      //               style: TextStyle(
                                      //                 color: Colors.black54,
                                      //                 fontSize: 14,
                                      //               ),
                                      //               hint: Text('- select a souce -'),
                                      //               onChanged: (newValue) async {
                                      //                 setState(() {
                                      //                   sel_source = newValue;
                                      //                 });
                                      //               },
                                      //               items: alldata[0]['lead_source'].map<DropdownMenuItem<String>>((item) {
                                      //                 return  DropdownMenuItem<String>(
                                      //                   child:  Container(
                                      //                     width: MediaQuery.of(context).size.width / 1.6,
                                      //                     child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                      //                   ),
                                      //                   value: item['id'].toString(),
                                      //                 );
                                      //               }).toList(),
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //
                                      //       SizedBox(height: 20,),
                                      //
                                      //
                                      //     ],
                                      //   ),
                                      // ),

                                      // // // -----------------------------sort  sec--------------------------------------------------------
                                      // // // if(GetStorage().read('current_role').toString() != "telecaller" || GetStorage().read('current_role').toString() == "team_lead")
                                      //  if(GetStorage().read('current_role').toString() != "academic_counsellor" || GetStorage().read('current_role').toString() == "team_lead")
                                      //    Container(
                                      //      padding: EdgeInsets.symmetric(horizontal: 10),
                                      //      child: Column(
                                      //        crossAxisAlignment: CrossAxisAlignment.start,
                                      //        children: [
                                      //          SizedBox(height: 10,),
                                      //          Container(
                                      //            // padding: EdgeInsets.symmetric(horizontal: 15),
                                      //            // color: Colors.yellow,
                                      //              child: Text("Team",style: TextStyle(fontFamily: "poppins_regular",fontSize: MediaQuery.of(context).size.width/25,fontWeight: FontWeight.w500,color: Colors.black),textAlign: TextAlign.left,)
                                      //          ),
                                      //          SizedBox(height: 10,),
                                      //
                                      //          Container(
                                      //            width: MediaQuery.of(context).size.width,
                                      //            decoration: BoxDecoration(
                                      //                borderRadius: BorderRadius.circular(10.0),
                                      //                border: Border.all(color: Colors.black26,width: 0.5),
                                      //                color: Colors.white
                                      //            ),
                                      //            child: DropdownButtonHideUnderline(
                                      //              child: ButtonTheme(
                                      //                alignedDropdown: true,
                                      //                child: DropdownButton<String>(
                                      //                  value: sel_team,
                                      //                  iconSize: 30,
                                      //                  // icon: (null),
                                      //                  style: TextStyle(
                                      //                    color: Colors.black54,
                                      //                    fontSize: 14,
                                      //                  ),
                                      //                  hint: Text('- select a team -'),
                                      //                  onChanged: (newValue) async {
                                      //                    setState(() {
                                      //                      sel_team = newValue;
                                      //                    });
                                      //                  },
                                      //                  items: alldata[0]['team'].map<DropdownMenuItem<String>>((item) {
                                      //                    return  DropdownMenuItem<String>(
                                      //                      child:  Container(
                                      //                        width: MediaQuery.of(context).size.width / 1.6,
                                      //                        child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                      //                      ),
                                      //                      value: item['id'].toString(),
                                      //                    );
                                      //                  }).toList(),
                                      //                ),
                                      //              ),
                                      //            ),
                                      //          ),
                                      //
                                      //          SizedBox(height: 20,),
                                      //
                                      //
                                      //        ],
                                      //      ),
                                      //    ),

                                      SizedBox(height: 30),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    Get.back();
                                    controller.get_leads();
                                  },
                                  child: Container(
                                    width: Get.width / 4,
                                    decoration: BoxDecoration(color: primaryColor, border: Border.all(color: primaryColor, width: 1), borderRadius: BorderRadius.circular(1000)),
                                    padding: EdgeInsets.symmetric(vertical: 15),
                                    child: Text("Apply", textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontFamily: "poppins_regular", fontWeight: FontWeight.w600)),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 15),
                      ],
                    ),
                  ),
                  Positioned(right: 0, left: 0, top: 0, child: Container(color: Colors.transparent, child: Image.asset('assets/icons/drag_handle.png'))),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
