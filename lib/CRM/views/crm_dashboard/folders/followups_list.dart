import 'package:edutalim/CRM/controller/followupscontroller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../../../../components/constants.dart';
import '../../../../components/utils.dart';

class followups_list extends StatefulWidget {
  String member_id;

  followups_list({required this.member_id});

  @override
  State<followups_list> createState() => _leads_listState();
}

class _leads_listState extends State<followups_list> {
  FollowUpController controller = Get.put(FollowUpController());

  @override
  void initState() {
    controller.member_id = widget.member_id;
    controller.get_followups();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: GetBuilder<FollowUpController>(
        builder: (controller) {
          return Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: true,
              iconTheme: IconThemeData(color: Colors.black),
              backgroundColor: Colors.white,
              elevation: 0,
              centerTitle: true,
              title: Text("Followups", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16)),
              actions: [
                IconButton(
                  onPressed: () {
                    if (controller.isLoading != true) {
                      filter_bottom_sheet();
                    }
                  },
                  icon: Icon(Icons.filter_alt, size: Get.width / 15, color: Colors.black54),
                ),
                SizedBox(width: 10),
              ],
            ),
            body: controller.isLoading
                ? loader()
                : Column(
                    children: [
                      Container(
                        color: Colors.white,
                        child: TabBar(
                          unselectedLabelColor: Colors.black54,
                          labelColor: Colors.black,
                          tabs: [
                            Tab(text: 'Current (${controller.followups[0]['current'].length})'),
                            Tab(text: 'Upcoming (${controller.followups[0]['upcoming'].length})'),
                          ],
                          indicatorSize: TabBarIndicatorSize.tab,
                        ),
                      ),
                      SizedBox(height: 10),
                      controller.from_date.toString() != 'All' || controller.to_date.toString() != 'All'
                          ? Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10),
                                  height: 60,
                                  child: ListView(
                                    scrollDirection: Axis.horizontal,
                                    children: [
                                      controller.from_date.toString() == 'All'
                                          ? Container()
                                          : Container(
                                              color: Colors.transparent,
                                              child: Row(
                                                children: [
                                                  Container(
                                                      padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                                                      margin: EdgeInsets.all(5),
                                                      alignment: Alignment.center,
                                                      color: Colors.white,
                                                      child: Row(
                                                        children: [
                                                          Text("From Date : ${controller.from_date} ", style: TextStyle(color: Colors.black)),
                                                          IconButton(
                                                              padding: EdgeInsets.all(0),
                                                              alignment: Alignment.center,
                                                              onPressed: () {
                                                                controller.from_date = 'All';
                                                                controller.get_followups();
                                                                controller.update();
                                                              },
                                                              icon: Icon(Icons.close))
                                                        ],
                                                      )),
                                                ],
                                              ),
                                            ),
                                      controller.to_date.toString() == 'All'
                                          ? Container()
                                          : Container(
                                              color: Colors.transparent,
                                              child: Row(
                                                children: [
                                                  Container(
                                                      padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                                                      margin: EdgeInsets.all(5),
                                                      alignment: Alignment.center,
                                                      color: Colors.white,
                                                      child: Row(
                                                        children: [
                                                          Text("To Date : ${controller.to_date} ", style: TextStyle(color: Colors.black)),
                                                          IconButton(
                                                              padding: EdgeInsets.all(0),
                                                              alignment: Alignment.center,
                                                              onPressed: () {
                                                                controller.to_date = 'All';
                                                                controller.get_followups();
                                                                controller.update();
                                                              },
                                                              icon: Icon(Icons.close))
                                                        ],
                                                      )),
                                                ],
                                              ),
                                            ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 5),
                              ],
                            )
                          : Container(),
                      Expanded(
                        child: TabBarView(
                          children: [
                            controller.followups[0]['current'].isEmpty
                                ? SizedBox(
                                    width: Get.width,
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Image.asset('assets/icons/no_data.png', width: Get.width / 4),
                                        SizedBox(height: 20),
                                        Text("No Data Found", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16)),
                                      ],
                                    ),
                                  )
                                : ListView.builder(
                                    physics: ScrollPhysics(),
                                    itemCount: controller.followups[0]['current'].length,
                                    shrinkWrap: true,
                                    itemBuilder: (context, index) {
                                      return Container(
                                        decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10)), color: Colors.white),
                                        margin: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                                        child: Column(
                                          children: [
                                            Container(
                                              padding: EdgeInsets.symmetric(horizontal: 15, vertical: 13),
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    width: Get.width / 8,
                                                    height: Get.width / 8,
                                                    decoration: BoxDecoration(border: Border.all(color: primaryColor, width: 2), borderRadius: BorderRadius.circular(50)),
                                                    child: ClipRRect(
                                                      borderRadius: BorderRadius.circular(50),
                                                      child: Image.network(
                                                        controller.followups[0]['current'][index]['profile_picture'].toString(),
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (a, b, c) {
                                                          return Image.asset('assets/images/avater_placeholder.jpg');
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 10),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: Get.width / 1.8,
                                                            child: Text(controller.followups[0]['current'][index]['title'].toString(),
                                                                style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: commontextColor, overflow: TextOverflow.ellipsis)),
                                                          ),
                                                          PopupMenuButton<int>(
                                                            itemBuilder: (context) => [
                                                              PopupMenuItem(
                                                                value: 2,
                                                                child: Row(
                                                                  children: [
                                                                    Icon(Icons.sync),
                                                                    SizedBox(width: 10),
                                                                    Text("Update", style: TextStyle(color: Colors.black)),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                            // offset: Offset(0, 100),
                                                            color: Colors.white,
                                                            icon: Container(
                                                              height: 36,
                                                              width: 48,
                                                              alignment: Alignment.centerRight,
                                                              child: Icon(Icons.more_vert, color: Colors.black),
                                                            ),
                                                            elevation: 5,
                                                            padding: EdgeInsets.all(0),
                                                            onSelected: (value) async {
                                                              if (value == 2) {
                                                                // Get.to(lead_add(action: 'update', data: [controller.followups[0]['current'][index]], member_id: widget.member_id.toString()))!.then((value) {
                                                                //   controller.get_followups();
                                                                // });
                                                              }
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: Get.width / 5,
                                                            child: Text("Phone", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                          ),
                                                          SizedBox(
                                                            width: Get.width / 2.5,
                                                            child: Text(
                                                              controller.followups[0]['current'][index]['phone'].toString() == "null" ? ":  --" : ":  ${controller.followups[0]['current'][index]['phone']}",
                                                              style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: Get.width / 5,
                                                            child: Text("Whatsapp", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                          ),
                                                          SizedBox(
                                                            width: Get.width / 2.5,
                                                            child: Text(
                                                              controller.followups[0]['current'][index]['whatsapp'].toString() == "null" ? ":  --" : ":  ${controller.followups[0]['current'][index]['whatsapp'].toString()}",
                                                              style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: Get.width / 5,
                                                            child: Text("Email", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                          ),
                                                          SizedBox(
                                                            width: Get.width / 2.5,
                                                            child: Text(
                                                              controller.followups[0]['current'][index]['email'].toString() == "null" ? ":  --" : ":  ${controller.followups[0]['current'][index]['email']}",
                                                              style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Divider(color: Colors.black.withValues(alpha: 0.1)),
                                            Container(
                                              padding: EdgeInsets.symmetric(horizontal: 20),
                                              child: Table(children: [
                                                TableRow(children: [
                                                  Container(padding: EdgeInsets.symmetric(vertical: 5), child: Text("Qualification", style: TextStyle(color: Colors.black54))),
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text(
                                                      controller.followups[0]['current'][index]['qualification'].toString(),
                                                      style: TextStyle(color: Colors.black),
                                                    ),
                                                  ),
                                                ]),
                                                TableRow(children: [
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text("Age", style: TextStyle(color: Colors.black54)),
                                                  ),
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text(controller.followups[0]['current'][index]['age'].toString(), style: TextStyle(color: Colors.black)),
                                                  ),
                                                ]),
                                                TableRow(children: [
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text("Place", style: TextStyle(color: Colors.black54)),
                                                  ),
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text(controller.followups[0]['current'][index]['place'].toString(), style: TextStyle(color: Colors.black)),
                                                  ),
                                                ]),
                                                TableRow(
                                                  children: [
                                                    Container(
                                                      padding: EdgeInsets.symmetric(vertical: 5),
                                                      child: Text("Lead Status", style: TextStyle(color: Colors.black54)),
                                                    ),
                                                    Container(
                                                      padding: EdgeInsets.symmetric(vertical: 5),
                                                      child: Text(
                                                        controller.followups[0]['current'][index]['lead_status'].toString(),
                                                        style: TextStyle(
                                                            color: controller.followups[0]['current'][index]['lead_status'].toString() == "pending"
                                                                ? Colors.orange
                                                                : controller.followups[0]['current'][index]['lead_status'].toString() == "follow_up"
                                                                    ? Colors.blue
                                                                    : controller.followups[0]['current'][index]['lead_status'].toString() == "converted"
                                                                        ? Colors.black
                                                                        : controller.followups[0]['current'][index]['lead_status'].toString() == "interested"
                                                                            ? Colors.green
                                                                            : controller.followups[0]['current'][index]['lead_status'].toString() == "not_interested"
                                                                                ? Colors.black54
                                                                                : controller.followups[0]['current'][index]['lead_status'].toString() == "qualified"
                                                                                    ? Colors.green
                                                                                    : controller.followups[0]['current'][index]['lead_status'].toString() == "black_list"
                                                                                        ? Colors.red
                                                                                        : Colors.black54,
                                                            fontFamily: 'poppins_bold'),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ]),
                                            ),
                                            SizedBox(height: 8),
                                            if (controller.followups[0]['current'][index]['lead_remarks'].toString().trim() != "null")
                                              Container(
                                                  margin: EdgeInsets.symmetric(horizontal: 20),
                                                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                                  width: Get.width,
                                                  decoration: BoxDecoration(color: Colors.grey.withValues(alpha: 0.2), borderRadius: BorderRadius.circular(8)),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text("Remark :-".trim(), style: TextStyle(color: Colors.black54, fontStyle: FontStyle.italic)),
                                                      SizedBox(height: 5),
                                                      Text(controller.followups[0]['current'][index]['lead_remarks'].toString().trim(), style: TextStyle(color: Colors.black, fontSize: 14, fontFamily: 'poppins_regular')),
                                                    ],
                                                  )),
                                            Divider(color: Colors.black.withValues(alpha: 0.1)),
                                            Container(
                                                margin: EdgeInsets.symmetric(vertical: 5),
                                                child: IntrinsicHeight(
                                                  child: Row(
                                                    children: [
                                                      Expanded(
                                                        child: GestureDetector(
                                                          onTap: () {
                                                            var content = Uri.parse("Hi ${controller.followups[0]['current'][index]['title'].toString().trim()}");
                                                            launchURL("https://wa.me/+${controller.followups[0]['current'][index]['whatsapp']}?text=${content.toString().trim()}");
                                                          },
                                                          child: Container(
                                                            padding: EdgeInsets.symmetric(vertical: 10),
                                                            child: Row(
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              children: [
                                                                Image.asset('assets/icons/whatsapp.png', width: Get.width / 17),
                                                                SizedBox(width: 6),
                                                                Text("Whatsapp", textAlign: TextAlign.center, style: TextStyle(color: Colors.green, fontFamily: "poppins_regular")),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      VerticalDivider(color: Colors.black.withValues(alpha: 0.2)),
                                                      Expanded(
                                                        child: GestureDetector(
                                                          onTap: () {
                                                            print("cal------------${controller.followups[0]['current'][index]['phone']}");
                                                            launchURL('tel:${controller.followups[0]['current'][index]['phone']}');
                                                          },
                                                          child: Container(
                                                            padding: EdgeInsets.symmetric(vertical: 10),
                                                            child: Row(
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              children: [
                                                                Icon(Icons.call, color: Colors.green, size: 20),
                                                                SizedBox(width: 6),
                                                                Text("Call", textAlign: TextAlign.center, style: TextStyle(color: Colors.green, fontFamily: "poppins_regular")),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                )),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                            controller.followups[0]['upcoming'].isEmpty
                                ? SizedBox(
                                    width: Get.width,
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Image.asset('assets/icons/no_data.png', width: Get.width / 4),
                                        SizedBox(height: 20),
                                        Text("No Data Found", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16)),
                                      ],
                                    ),
                                  )
                                : ListView.builder(
                                    physics: ScrollPhysics(),
                                    itemCount: controller.followups[0]['upcoming'].length,
                                    shrinkWrap: true,
                                    itemBuilder: (context, index) {
                                      return Container(
                                        decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10)), color: Colors.white),
                                        margin: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                                        child: Column(
                                          children: [
                                            Container(
                                              padding: EdgeInsets.symmetric(horizontal: 15, vertical: 13),
                                              child: Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    width: Get.width / 8,
                                                    height: Get.width / 8,
                                                    decoration: BoxDecoration(border: Border.all(color: primaryColor, width: 2), borderRadius: BorderRadius.circular(50)),
                                                    child: ClipRRect(
                                                      borderRadius: BorderRadius.circular(50),
                                                      child: Image.network(
                                                        controller.followups[0]['upcoming'][index]['profile_picture'].toString(),
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (a, b, c) {
                                                          return Image.asset('assets/images/avater_placeholder.jpg');
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 10),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: Get.width / 1.8,
                                                            child: Text(
                                                              controller.followups[0]['upcoming'][index]['title'].toString(),
                                                              style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: commontextColor, overflow: TextOverflow.ellipsis),
                                                            ),
                                                          ),
                                                          PopupMenuButton<int>(
                                                            itemBuilder: (context) => [
                                                              PopupMenuItem(
                                                                value: 2,
                                                                child: Container(
                                                                  child: Row(
                                                                    children: [
                                                                      Icon(Icons.sync),
                                                                      SizedBox(width: 10),
                                                                      Text("Update", style: TextStyle(color: Colors.black)),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                            // offset: Offset(0, 100),
                                                            color: Colors.white,
                                                            icon: Container(
                                                              height: 36,
                                                              width: 48,
                                                              alignment: Alignment.centerRight,
                                                              child: Icon(Icons.more_vert, color: Colors.black),
                                                            ),
                                                            elevation: 5,
                                                            padding: EdgeInsets.all(0),
                                                            onSelected: (value) async {
                                                              if (value == 2) {
                                                                // Get.to(lead_add(action: 'update', data: [controller.followups[0]['current'][index]], member_id: widget.member_id.toString()))!.then((value) {
                                                                //   controller.get_followups();
                                                                // });
                                                              }
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      Container(
                                                        child: Row(
                                                          children: [
                                                            SizedBox(
                                                              width: Get.width / 5,
                                                              child: Text("Phone", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                            ),
                                                            SizedBox(
                                                              width: Get.width / 2.5,
                                                              child: Text(
                                                                controller.followups[0]['upcoming'][index]['phone'].toString() == "null" ? ":  --" : ":  ${controller.followups[0]['upcoming'][index]['phone']}",
                                                                style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: Get.width / 5,
                                                            child: Text("Whatsapp", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                          ),
                                                          SizedBox(
                                                            width: Get.width / 2.5,
                                                            child: Text(controller.followups[0]['upcoming'][index]['whatsapp'].toString() == "null" ? ":  --" : ":  ${controller.followups[0]['upcoming'][index]['whatsapp']}",
                                                                style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                          ),
                                                        ],
                                                      ),
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: Get.width / 5,
                                                            child: Text("Email", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                          ),
                                                          SizedBox(
                                                            width: Get.width / 2.5,
                                                            child: Text(controller.followups[0]['upcoming'][index]['email'].toString() == "null" ? ":  --" : ":  ${controller.followups[0]['upcoming'][index]['email']}",
                                                                style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor)),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Divider(color: Colors.black.withValues(alpha: 0.1)),
                                            Container(
                                              padding: EdgeInsets.symmetric(horizontal: 20),
                                              child: Table(children: [
                                                TableRow(children: [
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text("Qualification", style: TextStyle(color: Colors.black54)),
                                                  ),
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text(controller.followups[0]['upcoming'][index]['qualification'].toString(), style: TextStyle(color: Colors.black)),
                                                  ),
                                                ]),
                                                TableRow(children: [
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text("Age", style: TextStyle(color: Colors.black54)),
                                                  ),
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text(controller.followups[0]['upcoming'][index]['age'].toString(), style: TextStyle(color: Colors.black)),
                                                  ),
                                                ]),
                                                TableRow(children: [
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text("Place", style: TextStyle(color: Colors.black54)),
                                                  ),
                                                  Container(
                                                    padding: EdgeInsets.symmetric(vertical: 5),
                                                    child: Text(controller.followups[0]['upcoming'][index]['place'].toString(), style: TextStyle(color: Colors.black)),
                                                  ),
                                                ]),
                                                TableRow(
                                                  children: [
                                                    Container(
                                                      padding: EdgeInsets.symmetric(vertical: 5),
                                                      child: Text("Lead Status", style: TextStyle(color: Colors.black54)),
                                                    ),
                                                    Container(
                                                      padding: EdgeInsets.symmetric(vertical: 5),
                                                      child: Text(
                                                        controller.followups[0]['upcoming'][index]['lead_status'].toString(),
                                                        style: TextStyle(
                                                            color: controller.followups[0]['upcoming'][index]['lead_status'].toString() == "pending"
                                                                ? Colors.orange
                                                                : controller.followups[0]['upcoming'][index]['lead_status'].toString() == "follow_up"
                                                                    ? Colors.blue
                                                                    : controller.followups[0]['upcoming'][index]['lead_status'].toString() == "converted"
                                                                        ? Colors.black
                                                                        : controller.followups[0]['upcoming'][index]['lead_status'].toString() == "interested"
                                                                            ? Colors.green
                                                                            : controller.followups[0]['upcoming'][index]['lead_status'].toString() == "not_interested"
                                                                                ? Colors.black54
                                                                                : controller.followups[0]['upcoming'][index]['lead_status'].toString() == "qualified"
                                                                                    ? Colors.green
                                                                                    : controller.followups[0]['upcoming'][index]['lead_status'].toString() == "black_list"
                                                                                        ? Colors.red
                                                                                        : Colors.black54,
                                                            fontFamily: 'poppins_bold'),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ]),
                                            ),
                                            SizedBox(height: 8),
                                            if (controller.followups[0]['upcoming'][index]['lead_remarks'].toString().trim() != "null")
                                              Container(
                                                  margin: EdgeInsets.symmetric(horizontal: 20),
                                                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                                  width: Get.width,
                                                  decoration: BoxDecoration(color: Colors.grey.withValues(alpha: 0.2), borderRadius: BorderRadius.circular(8)),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text("Remark :-".trim(), style: TextStyle(color: Colors.black54, fontStyle: FontStyle.italic)),
                                                      SizedBox(height: 5),
                                                      Text(controller.followups[0]['upcoming'][index]['lead_remarks'].toString().trim(), style: TextStyle(color: Colors.black, fontSize: 14, fontFamily: 'poppins_regular')),
                                                    ],
                                                  )),
                                            Divider(color: Colors.black.withValues(alpha: 0.1)),
                                            Container(
                                                margin: EdgeInsets.symmetric(vertical: 5),
                                                child: IntrinsicHeight(
                                                  child: Row(
                                                    children: [
                                                      Expanded(
                                                        child: GestureDetector(
                                                          onTap: () {
                                                            var content = Uri.parse("Hi ${controller.followups[0]['upcoming'][index]['title'].toString().trim()}");
                                                            launchURL("https://wa.me/+${controller.followups[0]['upcoming'][index]['whatsapp']}?text=${content.toString().trim()}");
                                                          },
                                                          child: Container(
                                                            padding: EdgeInsets.symmetric(vertical: 10),
                                                            child: Row(
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              children: [
                                                                Image.asset('assets/icons/whatsapp.png', width: Get.width / 17),
                                                                SizedBox(width: 6),
                                                                Text("Whatsapp", textAlign: TextAlign.center, style: TextStyle(color: Colors.green, fontFamily: "poppins_regular")),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      VerticalDivider(color: Colors.black.withValues(alpha: 0.2)),
                                                      Expanded(
                                                        child: GestureDetector(
                                                          onTap: () {
                                                            launchURL('tel:${controller.followups[0]['upcoming'][index]['phone']}');
                                                          },
                                                          child: Container(
                                                            padding: EdgeInsets.symmetric(vertical: 10),
                                                            child: Row(
                                                              mainAxisAlignment: MainAxisAlignment.center,
                                                              children: [
                                                                Icon(Icons.call, color: Colors.green, size: 20),
                                                                SizedBox(width: 6),
                                                                Text("Call", textAlign: TextAlign.center, style: TextStyle(color: Colors.green, fontFamily: "poppins_regular")),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                )),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                          ],
                        ),
                      ),
                    ],
                  ),
          );
        }
      ),
    );
  }

  void _fromDatePicker(ctx) {
    controller.follow_from_date.text = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.from_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.update();

    showCupertinoModalPopup(
        context: ctx,
        builder: (_) => GetBuilder<FollowUpController>(builder: (controller) {
              return Container(
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                height: 410,
                child: Column(
                  children: [
                    Container(
                      width: Get.width,
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(color: Colors.lightBlueAccent, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                      child: Text('Scrool to select', style: TextStyle(fontSize: 15, color: Colors.white, decoration: TextDecoration.none, fontFamily: '')),
                    ),

                    SizedBox(
                      height: 290,
                      child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: DateTime.now(),
                          onDateTimeChanged: (val) {
                            controller.fromDate = val;
                            controller.follow_from_date.text = "${controller.fromDate!.day}-${controller.fromDate!.month}-${controller.fromDate!.year}";
                            controller.from_date = "${controller.fromDate!.day}-${controller.fromDate!.month}-${controller.fromDate!.year}";
                            controller.update();
                          }),
                    ),

                    // Close the modal
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CupertinoButton(
                          child: const Text('Cancel'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                        CupertinoButton(
                          child: const Text('OK'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }));
  }

  void _toDatePicker(ctx) {
    controller.follow_to_date.text = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    controller.to_date = DateTime.now().day.toString() + "-" + DateTime.now().month.toString() + "-" + DateTime.now().year.toString();
    controller.update();
    showCupertinoModalPopup(
        context: ctx,
        builder: (_) => GetBuilder<FollowUpController>(builder: (controller) {
              return Container(
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                height: 410,
                child: Column(
                  children: [
                    Container(
                      width: Get.width,
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(color: Colors.lightBlueAccent, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                      child: Text('Scroll to select', style: TextStyle(fontSize: 15, color: Colors.white, decoration: TextDecoration.none, fontFamily: '')),
                    ),

                    SizedBox(
                      height: 290,
                      child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: DateTime.now(),
                          onDateTimeChanged: (val) {
                            controller.toDate = val;
                            controller.follow_to_date.text = "${controller.toDate!.day}-${controller.toDate!.month}-${controller.toDate!.year}";
                            controller.to_date = "${controller.toDate!.day}-${controller.toDate!.month}-${controller.toDate!.year}";
                            controller.update();
                          }),
                    ),

                    // Close the modal
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CupertinoButton(
                          child: const Text('Cancel'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                        CupertinoButton(
                          child: const Text('OK'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }));
  }

  // filter bottom sheet sec
  void filter_bottom_sheet() {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      isScrollControlled: true,
      context: context,
      useSafeArea: true,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
      builder: (builder) {
        return GetBuilder<FollowUpController>(
          builder: (controller) {
            return Padding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Stack(
                children: [
                  Container(
                    constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height / 1.2),
                    child: Column(
                      children: [
                        SizedBox(height: 30),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Image.asset('assets/icons/filter.png', width: Get.width / 18, color: Colors.black),
                              SizedBox(width: 15),
                              Text("Filter", style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 23, fontWeight: FontWeight.w600, color: Colors.black)),
                            ],
                          ),
                        ),
                        Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            physics: ScrollPhysics(),
                            child: Wrap(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text("Date",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left),
                                            SizedBox(height: 20),
                                            Row(
                                              children: [
                                                Expanded(child: Text("From date :", style: TextStyle(color: Colors.black54, fontSize: Get.width / 34))),
                                                SizedBox(width: 12),
                                                Expanded(child: Text("To date :", style: TextStyle(color: Colors.black54, fontSize: Get.width / 34))),
                                              ],
                                            ),
                                            SizedBox(height: 8),
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Container(
                                                    child: TextField(
                                                      onTap: () {
                                                        _fromDatePicker(context);
                                                      },
                                                      readOnly: true,
                                                      style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                                                      controller: controller.follow_from_date,
                                                      decoration: InputDecoration(
                                                        focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                        enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                        filled: true,
                                                        hintStyle: TextStyle(color: Colors.black, fontFamily: "poppins_regular"),
                                                        hintText: controller.from_date.toString(),
                                                        fillColor: Colors.blueAccent.withValues(alpha: 0.2),
                                                        contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                                                        suffixIcon: controller.from_date.toString() == "All"
                                                            ? SizedBox(width: 0, height: 0)
                                                            : IconButton(
                                                                onPressed: () {
                                                                  controller.follow_from_date.text = "All";
                                                                  controller.from_date = "All";
                                                                  controller.update();
                                                                },
                                                                icon: Icon(Icons.close, color: Colors.black),
                                                              ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 12,
                                                ),
                                                Expanded(
                                                  child: Container(
                                                    // height: 40,
                                                    child: TextField(
                                                      onTap: () {
                                                        _toDatePicker(context);
                                                      },
                                                      readOnly: true,
                                                      style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                                                      controller: controller.follow_to_date,
                                                      decoration: InputDecoration(
                                                        focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                        enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                        filled: true,
                                                        hintStyle: TextStyle(color: Colors.black, fontFamily: "poppins_regular"),
                                                        hintText: controller.to_date.toString(),
                                                        fillColor: Colors.blueAccent.withValues(alpha: 0.2),
                                                        contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                                                        suffixIcon: controller.to_date.toString() == "All"
                                                            ? SizedBox(width: 0, height: 0)
                                                            : IconButton(
                                                                onPressed: () {
                                                                  controller.follow_to_date.text = "All";
                                                                  controller.to_date = "All";
                                                                  controller.update();
                                                                },
                                                                icon: Icon(Icons.close, color: Colors.black),
                                                              ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      // Container(
                                      //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      //   child: Column(
                                      //     crossAxisAlignment: CrossAxisAlignment.start,
                                      //     children: [
                                      //       SizedBox(height: 10),
                                      //       Text("Country",
                                      //           style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                      //           textAlign: TextAlign.left),
                                      //       SizedBox(height: 10),
                                      //       Container(
                                      //         width: MediaQuery.of(context).size.width,
                                      //         decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                      //         child: DropdownButtonHideUnderline(
                                      //           child: ButtonTheme(
                                      //             alignedDropdown: true,
                                      //             child: DropdownButton<String>(
                                      //               value: controller.sel_country,
                                      //               iconSize: 30,
                                      //               style: TextStyle(color: Colors.black54, fontSize: 14),
                                      //               hint: Text('- select a country -'),
                                      //               onChanged: (newValue) async {
                                      //                 controller.sel_country = newValue;
                                      //                 controller.update();
                                      //               },
                                      //               items: controller.alldata[0]['country'].map<DropdownMenuItem<String>>((item) {
                                      //                 return DropdownMenuItem<String>(
                                      //                   value: item['id'].toString(),
                                      //                   child: SizedBox(
                                      //                     width: MediaQuery.of(context).size.width / 1.6,
                                      //                     child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                      //                   ),
                                      //                 );
                                      //               }).toList(),
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //       SizedBox(height: 20),
                                      //     ],
                                      //   ),
                                      // ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      // Container(
                                      //   padding: EdgeInsets.symmetric(horizontal: 10),
                                      //   child: Column(
                                      //     crossAxisAlignment: CrossAxisAlignment.start,
                                      //     children: [
                                      //       SizedBox(height: 10),
                                      //       Text("University",
                                      //           style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                      //           textAlign: TextAlign.left),
                                      //       SizedBox(height: 10),
                                      //       Container(
                                      //         width: MediaQuery.of(context).size.width,
                                      //         decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                      //         child: DropdownButtonHideUnderline(
                                      //           child: ButtonTheme(
                                      //             alignedDropdown: true,
                                      //             child: DropdownButton<String>(
                                      //               value: controller.sel_university,
                                      //               iconSize: 30,
                                      //               style: TextStyle(color: Colors.black54, fontSize: 14),
                                      //               hint: Text('- select a university -'),
                                      //               onChanged: (newValue) async {
                                      //                 controller.sel_university = newValue;
                                      //                 controller.update();
                                      //               },
                                      //               items: controller.alldata[0]['university'].map<DropdownMenuItem<String>>((item) {
                                      //                 return DropdownMenuItem<String>(
                                      //                   value: item['id'].toString(),
                                      //                   child: SizedBox(
                                      //                     width: MediaQuery.of(context).size.width / 1.6,
                                      //                     child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                      //                   ),
                                      //                 );
                                      //               }).toList(),
                                      //             ),
                                      //           ),
                                      //         ),
                                      //       ),
                                      //       SizedBox(height: 20),
                                      //     ],
                                      //   ),
                                      // ),

                                      // -----------------------------sort  sec--------------------------------------------------------

                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text("Source",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            Container(
                                              width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_source,
                                                    iconSize: 30,
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('- select a souce -'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_source = newValue;
                                                      controller.update();
                                                    },
                                                    items: controller.alldata[0]['lead_source'].map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['id'].toString(),
                                                        child: SizedBox(
                                                          width: MediaQuery.of(context).size.width / 1.6,
                                                          child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      // if(GetStorage().read('current_role').toString() != "telecaller" || GetStorage().read('current_role').toString() == "team_lead")
                                      if (GetStorage().read('current_role').toString() != "academic_counsellor" || GetStorage().read('current_role').toString() == "team_lead")
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 10),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              SizedBox(height: 10),
                                              Text("Team",
                                                  style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                  textAlign: TextAlign.left),
                                              SizedBox(height: 10),
                                              Container(
                                                width: MediaQuery.of(context).size.width,
                                                decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                                child: DropdownButtonHideUnderline(
                                                  child: ButtonTheme(
                                                    alignedDropdown: true,
                                                    child: DropdownButton<String>(
                                                      value: controller.sel_team,
                                                      iconSize: 30,
                                                      style: TextStyle(color: Colors.black54, fontSize: 14),
                                                      hint: Text('- select a team -'),
                                                      onChanged: (newValue) async {
                                                        controller.sel_team = newValue;
                                                        controller.update();
                                                      },
                                                      items: controller.alldata[0]['team'].map<DropdownMenuItem<String>>((item) {
                                                        return DropdownMenuItem<String>(
                                                          value: item['id'].toString(),
                                                          child: SizedBox(
                                                            width: MediaQuery.of(context).size.width / 1.6,
                                                            child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                          ),
                                                        );
                                                      }).toList(),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(height: 20),
                                            ],
                                          ),
                                        ),

                                      SizedBox(height: 20),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    controller.get_followups();
                                    Get.back();
                                  },
                                  child: Container(
                                    width: Get.width / 4,
                                    decoration: BoxDecoration(color: primaryColor, border: Border.all(color: primaryColor, width: 1), borderRadius: BorderRadius.circular(1000)),
                                    padding: EdgeInsets.symmetric(vertical: 15),
                                    child: Text("Apply", textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontFamily: "poppins_regular", fontWeight: FontWeight.w600)),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                  Positioned(right: 0, left: 0, top: 0, child: Container(color: Colors.transparent, child: Image.asset('assets/icons/drag_handle.png'))),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
