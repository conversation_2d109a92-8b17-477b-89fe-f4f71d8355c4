import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../controllers/ai_mentor/ai_chat_sceen_controller.dart';

class ai_chat_screen extends StatefulWidget {
  @override
  State<ai_chat_screen> createState() => _ai_chat_screenState();
}

class _ai_chat_screenState extends State<ai_chat_screen> with SingleTickerProviderStateMixin {
  AiChatScreenController controller = Get.put(AiChatScreenController());

  @override
  void initState() {
    controller.getMessages();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AiChatScreenController>(builder: (controller) {
      return GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          appBar: AppBar(
            backgroundColor: textwhiteColor,
            surfaceTintColor: textwhiteColor,
            title: Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  LottieBuilder.asset("assets/lotties/speak_1.json", height: Get.width / 3, width: Get.width / 10),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        width: Get.width / 1.7,
                        child: Text("${appfullName} AI", style: TextStyle(color: textblackColor, fontSize: Get.width / 28, fontFamily: font_regular, fontWeight: FontWeight.w600), textAlign: TextAlign.center),
                      ),
                      SizedBox(height: 3),
                    ],
                  ),
                ],
              ),
            ),
          ),
          body: Column(
            children: [
              Expanded(
                child: Container(
                  color: primaryColor.withOpacity(0.1),
                  child: Stack(
                    children: [
                      controller.isMessageLoading
                          ? loader()
                          : SingleChildScrollView(
                              controller: controller.chatScrollController,
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 15),
                                child: ListView.builder(
                                  physics: const ScrollPhysics(),
                                  padding: EdgeInsets.only(bottom: Get.height / 5.5),
                                  shrinkWrap: true,
                                  itemCount: controller.chat.length,
                                  itemBuilder: (BuildContext context, int index) {
                                    var data = controller.chat[index];
                                    return GestureDetector(
                                        onTap: () {},
                                        child: Container(
                                          alignment: data['role'].toString() != "user" ? Alignment.centerLeft : Alignment.centerRight,
                                          margin: const EdgeInsets.symmetric(vertical: 12),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment: data['role'].toString() != "user" ? CrossAxisAlignment.start : CrossAxisAlignment.end,
                                            children: [
                                              Container(
                                                decoration: BoxDecoration(
                                                  color: data['role'].toString() != "user" ? secondaryColor.withValues(alpha: 0.70) : textwhiteColor,
                                                  borderRadius: BorderRadius.only(
                                                    topRight: const Radius.circular(20),
                                                    bottomRight: Radius.circular(data['role'].toString() != "user" ? 20 : 0),
                                                    topLeft: Radius.circular(data['role'].toString() != "user" ? 0 : 20),
                                                    bottomLeft: const Radius.circular(20),
                                                  ),
                                                ),
                                                constraints: BoxConstraints(maxWidth: Get.width / 1.35),
                                                padding: const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10),
                                                child: IntrinsicWidth(
                                                  child: Column(
                                                    crossAxisAlignment: data['role'].toString() == 'user' ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                                                    children: [
                                                      Row(
                                                        mainAxisAlignment: data['role'].toString() == 'user' ? MainAxisAlignment.end : MainAxisAlignment.start,
                                                        children: [
                                                          Expanded(
                                                            child: Text(
                                                              data['content'].toString(),
                                                              style: TextStyle(color: data['role'].toString() == "user" ? textblackColor : Colors.black, fontFamily: font_regular, fontSize: Get.width / 30),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(height: 3),
                                                      Text('10:36 AM', style: TextStyle(color: textblackColor.withOpacity(0.4), fontFamily: font_regular, fontSize: Get.width / 45)),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ));
                                  },
                                ),
                              ),
                            ),
                      Positioned(
                        bottom: 15,
                        left: 0,
                        right: 0,
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      width: Get.width,
                                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(1000), color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 5)]),
                                      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                      padding: const EdgeInsets.symmetric(horizontal: 5),
                                      child: Row(
                                        children: [
                                          controller.isListening
                                              ? Expanded(
                                                  child: Container(
                                                    padding: EdgeInsets.only(left: 10,top: 10,bottom: 10,right: 20),
                                                    child: Row(
                                                      children: [
                                                        Container(decoration: BoxDecoration(color: Colors.red.withValues(alpha: 0.1,),borderRadius: BorderRadius.circular(1000)),child: IconButton(onPressed: (){
                                                          controller.forceStop();
                                                        }, icon: Icon(CupertinoIcons.delete,color: Colors.red,size: Get.width/24,))),
                                                        const SizedBox(width: 10,),
                                                        Expanded(child: Text(controller.spokenText, style: TextStyle(fontWeight: FontWeight.w600))),
                                                        const SizedBox(width: 10),
                                                        LottieBuilder.asset("assets/lotties/wave.json", width: Get.width / 10)
                                                      ],
                                                    ),
                                                  ),
                                                )
                                              : Expanded(
                                                  child: TextField(
                                                    onTap: () {},
                                                    onChanged: (text) {
                                                      if (text.trim().isEmpty) {
                                                        controller.msgTextController.text = "";
                                                      }
                                                      controller.update();
                                                    },
                                                    maxLines: null,
                                                    keyboardType: TextInputType.text,
                                                    controller: controller.msgTextController,
                                                    style: TextStyle(fontSize: Get.width / 28, color: Colors.black),
                                                    decoration: InputDecoration(
                                                      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent, width: 1), borderRadius: BorderRadius.circular(10.0)),
                                                      filled: true,
                                                      hintStyle: TextStyle(fontFamily: font_regular, color: Colors.black38, fontSize: Get.width / 28),
                                                      hintText: "Type your message here",
                                                      fillColor: Colors.transparent,
                                                      contentPadding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20),
                                                    ),
                                                  ),
                                                )
                                        ],
                                      ),
                                    ),
                                  ),
                                  // SizedBox(width: 10,),
                                  GestureDetector(
                                    onTap: () async {
                                      if (controller.isListening) {
                                        controller.stopListening();
                                      }

                                      if (controller.msgTextController.text.isNotEmpty) {
                                        controller.sendMessage("you", controller.msgTextController.text, "text", "");
                                      }
                                    },
                                    onLongPress: () {
                                      controller.isListening ? controller.stopListening() : controller.startListening();
                                    },
                                    child: AnimatedScale(
                                      scale: controller.animate ? 1.3 : 1.0,
                                      duration: const Duration(milliseconds: 300),
                                      curve: Curves.easeInOut,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: primaryColor,
                                          borderRadius: BorderRadius.circular(1000),
                                        ),
                                        width: MediaQuery.of(context).size.width / 9,
                                        height: MediaQuery.of(context).size.width / 9,
                                        alignment: Alignment.center,
                                        child: controller.isGenerating
                                            ? const SizedBox(width: 25, height: 25, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                                            : Icon(
                                                controller.msgTextController.text.isNotEmpty
                                                    ? Icons.send_outlined
                                                    : controller.isListening
                                                        ? Icons.send_outlined
                                                        : CupertinoIcons.mic,
                                                color: textwhiteColor,
                                                size: 20,
                                              ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

// List chat = [
//   'Hi!',
//   'Hello!',
//   'How can I improve my pronunciation?',
//   'Practice tongue twisters and mimic native speakers.',
//   'How do I sound more natural?',
//   'Work on intonation and mimic actors from movies.',
//   'How can I understand different accents?',
//   'Listen to various accents through podcasts and use subtitles.',
//   'Any tips for learning idioms?',
//   'Learn idioms in context by reading and listening.'
// ];
}
