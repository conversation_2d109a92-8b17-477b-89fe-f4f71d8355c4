import 'dart:math';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/LMS/views/dashboard/my_course/discussion_forum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/mycoursecontroller.dart';
import '../../InteractivePlayer/Controller/VideoPlayerController.dart';
import '../../InteractivePlayer/CustomMp4Player.dart';
import '../../InteractivePlayer/better_player.dart';

class lesson_files_better_player extends StatefulWidget {
  String lesson_id, title;

  lesson_files_better_player({required this.lesson_id, required this.title});

  @override
  State<lesson_files_better_player> createState() => _lesson_files_better_playerState();
}

class _lesson_files_better_playerState extends State<lesson_files_better_player> with SingleTickerProviderStateMixin {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());

  VideoPlayerGetXController vidcontroller = Get.put(VideoPlayerGetXController());

  @override
  void initState() {
    vidcontroller.lessonId = widget.lesson_id.toString();
    vidcontroller.get_lesson_files(widget.lesson_id.toString());
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    vidcontroller.lesson_files = [];
    vidcontroller.sel_index = 0;
    vidcontroller.sel_video = null;
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VideoPlayerGetXController>(builder: (controller) {
      return Scaffold(
        backgroundColor: textwhiteColor,
        body: Container(
          width: Get.width,
          height: Get.height,
          decoration: BoxDecoration(
            color: textwhiteColor,
          ),
          child: Column(
            children: [
              AppBar(
                backgroundColor: appbarwhiteColor,
                surfaceTintColor: appbarwhiteColor,
                centerTitle: false,
                title: Text(
                  widget.title.toString(),
                  style: TextStyle(color: textblackColor, fontWeight: FontWeight.w500, fontSize: Get.width / 22),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: controller.isLessonFileLoading
                      ? Container(
                          height: Get.height / 3.8,
                          width: Get.width / 1.1,
                          child: LinearProgressIndicator(
                            color: textblackColor.withOpacity(0.05),
                            backgroundColor: textblackColor.withOpacity(0.025),
                          ),
                        )
                      : controller.selvidindexloading
                          ? AspectRatio(
                              aspectRatio: 16 / 9,
                              child: Center(child: CircularProgressIndicator(color: Colors.red, strokeWidth: 1)),
                            )
                          : controller.sel_video['video_links'].isEmpty
                              ? AspectRatio(
                                  aspectRatio: 16 / 9,
                                  child: Container(
                                    decoration: BoxDecoration(color: Colors.black),
                                    child: Center(child: Text("No Video Found", style: TextStyle(color: Colors.white))),
                                  ),
                                )
                              : AspectRatio(aspectRatio: 16 / 9, child: better_player_page(data: controller.sel_video ?? {"video_links": []})),
                ),
              ),
              controller.isLessonFileLoading
                  ? Container(
                      // alignment: Alignment.centerLeft,
                      margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      height: 20,
                      width: Get.width / 1.5,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(color: textblackColor.withOpacity(0.05), backgroundColor: textblackColor.withOpacity(0.025)),
                      ),
                    )
                  : Container(
                      padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                      decoration: BoxDecoration(
                        color: textwhiteColor,
                        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.05), spreadRadius: 1, blurRadius: 10, offset: Offset(0, 5))],
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: EdgeInsets.only(left: 5),
                                  width: Get.width,
                                  child: Text(controller.sel_video == null ? "--" : controller.sel_video['title'] ?? "--", style: TextStyle(fontWeight: FontWeight.w800, fontSize: Get.width / 22)),
                                ),
                                Container(
                                  padding: EdgeInsets.only(left: 5),
                                  width: Get.width,
                                  child: Text(controller.sel_video == null ? "--" : controller.sel_video['description'] ?? "---------------", style: TextStyle(fontWeight: FontWeight.w300, fontSize: Get.width / 30)),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 15),
                          // Image.asset(
                          //   'assets/temp_images/download.png',
                          //   width: Get.width / 10,
                          // ),
                        ],
                      ),
                    ),
              SizedBox(height: 0),
              Expanded(
                child: controller.isLessonFileLoading
                    ? loader()
                    : SingleChildScrollView(
                        child: Column(
                          children: [
                            SizedBox(height: 20),
                            controller.lesson_files.isEmpty
                                ? Container(padding: EdgeInsets.all(80), child: Text("No Video's Found"))
                                : Container(
                                    padding: EdgeInsets.symmetric(horizontal: 10),
                                    child: ListView.builder(
                                      physics: NeverScrollableScrollPhysics(),
                                      padding: EdgeInsets.all(0),
                                      shrinkWrap: true,
                                      itemCount: controller.lesson_files.length,
                                      itemBuilder: (BuildContext context, int index) {
                                        Map data = controller.lesson_files[index];
                                        return GestureDetector(
                                          onTap: () async {
                                            controller.selvidindexloading = true;
                                            controller.update();

                                            await Future.delayed(Duration(milliseconds: 500), () {
                                              controller.sel_index = index;
                                              controller.sel_video = data;
                                              controller.selvidindexloading = false;
                                              controller.update();
                                            });
                                          },
                                          child: Container(
                                            margin: EdgeInsets.only(bottom: 10),
                                            child: Stack(
                                              children: [
                                                Container(
                                                  padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(10),
                                                    border: Border.all(color: index == controller.sel_index ? secondaryColor.withOpacity(0.4) : textblackColor.withOpacity(0.1)),
                                                    color: index == controller.sel_index ? secondaryColor.withOpacity(0.1) : textwhiteColor,
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                          children: [
                                                            SizedBox(
                                                              width: Get.width,
                                                              child: Text('Video  - ${index + 1}', style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 30), maxLines: 2),
                                                            ),
                                                            const SizedBox(height: 5),
                                                            SizedBox(
                                                              width: Get.width,
                                                              child: Text(
                                                                data['title'].toString(),
                                                                style: TextStyle(color: textblackColor, fontWeight: FontWeight.w400, fontSize: Get.width / 25, fontFamily: font_medium),
                                                              ),
                                                            ),
                                                            SizedBox(height: 5),
                                                          ],
                                                        ),
                                                      ),
                                                      const SizedBox(width: 10),
                                                      data['premium_lock'].toString() == "1" || data['sequence_lock'].toString() == "1"
                                                          ? Container(padding: EdgeInsets.all(10), decoration: BoxDecoration(color: Colors.grey.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(100)), child: Icon(Icons.lock_outline, size: 20))
                                                          : Icon(controller.sel_index == index ? Icons.pause_circle_filled_rounded : Icons.play_circle_outline_rounded, size: 28)
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ),
        floatingActionButton: GestureDetector(
          onTap: () {
            if (vidcontroller.playerController.value.isPlaying) {
              vidcontroller.playerController.pause();
            }
            Get.to(DiscussionForum());
          },
          child: SizedBox(
            width: Get.width / 3,
            child: Image.asset('assets/temp_images/discussion_1.png'),
          ),
        ),
      );
    });
  }
}
