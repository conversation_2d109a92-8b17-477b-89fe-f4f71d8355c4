import 'dart:async';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lottie/lottie.dart';
import 'package:share_plus/share_plus.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';

import '../../../../views/auth/login.dart';


class main_drawer extends StatelessWidget {

  @override
  Widget build(BuildContext context) {
    return Drawer(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0)
      ),
      child: Container(
        decoration: BoxDecoration(
            // image: DecorationImage(
            //     fit: BoxFit.cover,
            //     image: AssetImage('assets/bg/bg_common.png')
            // ),
            color: Colors.white
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [

            DrawerHeader(
              child: Container(
                padding: EdgeInsets.all(10),
                // child: Image.asset('assets/logo/logo_transp.png',),
                child: Image.asset('assets/logo/logo.png',),
              ),
            ),
            // if(data[0]['role_id'].toString() == '3')
            // GestureDetector(
            //   onTap: (){
            //       Get.to(()=> instructor_intro(from: 'edit', more_info: data[0]['more_info'], is_approved: is_approved.toString(),));
            //   },
            //   child:  ListTile(
            //     // leading: SvgPicture.asset('assets/icons/notification.svg',width: Get.width/22,color: primaryColor,),
            //     leading: Icon(CupertinoIcons.info,size: Get.width/20,color: primaryColor,),
            //     title: Text("Add more information",style: TextStyle(fontSize: 16,color: primaryColor,fontWeight: FontWeight.w600),),
            //   ),
            // ),
            // GestureDetector(
            //   onTap: (){
            //     // Get.to(()=> notifications());
            //   },
            //   child:  ListTile(
            //     leading: SvgPicture.asset('assets/icons/notification.svg',width: Get.width/22,color: primaryColor,),
            //     title: Text("Notifications",style: TextStyle(fontSize: 16,color: primaryColor,fontWeight: FontWeight.w600),),
            //   ),
            // ),

            GestureDetector(
              onTap: (){
                Share.share(share_description.toString());
              },
              child:  ListTile(
                leading: Image.asset('assets/icons/share1.png',width: Get.width/22,color: primaryColor,),
                title: Text("Share the App",style: TextStyle(fontSize: 16,color: primaryColor,fontWeight: FontWeight.w600),),
              ),
            ),



            GestureDetector(
              onTap: (){
                launchURL('$privacy_policy_url');
              },
              child:  ListTile(
                leading: Image.asset('assets/icons/privacypolicy.png',width: Get.width/22,color: primaryColor,),
                title: Text("Privacy Policy",style: TextStyle(fontSize: 16,color: primaryColor,fontWeight: FontWeight.w600),),
              ),
            ),

            Divider(thickness: 0.5,),

            GestureDetector(
              onTap: (){
               logout_confirm(context);
              },
              child:  ListTile(
                leading: Image.asset('assets/icons/logout.png',width: Get.width/22,color: Colors.red,),
                title: Text("Logout",style: TextStyle(fontSize: 16,color: Colors.red),),
              ),
            ),

            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 15),
                alignment: Alignment.bottomCenter,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Text(appName,style: TextStyle(color: primaryColor.withOpacity(.7),fontSize: Get.width/34),),
                    // Text("version $ios_version",style: TextStyle(color: primaryColor,fontSize: Get.width/34),),
                    Text(Platform.isAndroid ? "$appName   |   Version $android_version" : "$appName   |   Version $ios_version",style: TextStyle(color: primaryColor,fontSize: Get.width/34),),
                  ],
                )
              ),
            ),

          ],
        ),
      ),
    );
  }
}



void logout_confirm(BuildContext context) {

  showCupertinoDialog(
      context: context,
      builder: (context){
        return CupertinoAlertDialog(
          title: const Text("Logout ?"),
          content: const Wrap(
            alignment: WrapAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 20,),

                  // Lottie.asset('assets/lottie/warning.json',width: Get.width/2,repeat: false),
                  Text( "Are you sure you want to Logout ?"),
                  SizedBox(height: 20,),
                ],
              )
            ],
          ),
          actions: <Widget>[
            CupertinoDialogAction(
                isDefaultAction: true,
                // isDestructiveAction: true,
                onPressed: (){
                  Navigator.pop(context);
                },
                child: Text("Cancel",style: TextStyle(color: Colors.blue),)
            ),
            CupertinoDialogAction(
                textStyle: TextStyle(color: Colors.red),
                isDefaultAction: true,
                onPressed: () async {
                  GetStorage().write('login_status', 'false');
                  Get.offAll(() =>Login());
                },
                child: Text("Logout")
            ),
          ],
        );
      }
  );
}



void delete_confirm(BuildContext context) {

  showCupertinoDialog(
      context: context,
      builder: (context){
        return CupertinoAlertDialog(
          title: Text("Delete Account?"),
          content: Wrap(
            children: [
              Column(
                children: [
                  Text( "Are you sure you want to delete your account permanently?"),
                  SizedBox(height: 20,),
                  Container(
                    child: Text( "if yes, press the 'Delete' button"),
                  ),
                  SizedBox(height: 20,),
                  Container(
                    child: Text( "Note : Your account deletion will be completed after admin review."),
                  ),
                ],
              )
            ],
          ),
          actions: <Widget>[
            CupertinoDialogAction(
                isDefaultAction: true,
                onPressed: (){
                  Navigator.pop(context);
                },
                child: Text("Cancel")
            ),
            CupertinoDialogAction(
                textStyle: TextStyle(color: Colors.red),
                isDefaultAction: true,
                onPressed: () async {
                  Navigator.pop(context);
                  deleted_popup(context);
                },
                child: Text("Delete")
            ),
          ],
        );
      }
  );
}

late Timer _timerr;

void deleted_popup(BuildContext context) {
  showCupertinoDialog(
      context: context,
      builder: (context){
        _timerr = Timer(Duration(seconds: 3), () async {
          var uid = GetStorage().read("user_id").toString();
          await GetStorage().erase();
          GetStorage().write("account_deletion", "true");
          GetStorage().write("account_deletion_userid", uid.toString());
          GetStorage().write("login_status", "false");

          // Get.offAll(() =>Login());

        });
        var size = MediaQuery.of(context).size;
        return CupertinoAlertDialog(
            content: Wrap(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(height: 20,),
                    Lottie.asset('assets/lottie/success.json',width: size.width/6),
                    SizedBox(height: 20,),
                    Container(
                      child: Text("Account deletion request sent successfully to $appName",style: TextStyle(fontFamily: "poppins_regular",fontWeight: FontWeight.w400,color: Colors.green),textAlign: TextAlign.center,),
                    ),
                  ],
                ),
              ],
            )
        );
      }
  ).then((val){
    if (_timerr.isActive) {
      _timerr.cancel();
    }
  });
}
