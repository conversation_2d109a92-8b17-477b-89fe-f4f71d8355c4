import 'dart:math';

import 'package:edutalim/components/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LeaderBoardScreen extends StatelessWidget {
  final String data;

  LeaderBoardScreen({
    Key? key,
    required this.data,
  }) : super(key: key);

  final List<Map<String, dynamic>> leaderboard = [
    {"rank": 4, "name": "Arshad P.P", "score": 95, "avatar": "👤"},
    {"rank": 5, "name": "Anas K.T", "score": 92, "avatar": "👤"},
    {"rank": 6, "name": "Hisam T.c", "score": 87, "avatar": "👤"},
    {"rank": 7, "name": "Lijosh M.M", "score": 70, "avatar": "👤"},
    {"rank": 8, "name": "<PERSON>han <PERSON>", "score": 70, "avatar": "👤"},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: <PERSON>umn(
          children: [
            buildTopBanner(),
            <PERSON><PERSON><PERSON><PERSON>(height: 14),
            <PERSON><PERSON><PERSON>(
              children: [
                // Table Header
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  child: Row(
                    children: const [
                      Expanded(flex: 1, child: Text("Rank", style: TextStyle(fontWeight: FontWeight.bold))),
                      Expanded(flex: 3, child: Text("   Name", style: TextStyle(fontWeight: FontWeight.bold))),
                      Expanded(flex: 2, child: Text("Top Mark", style: TextStyle(fontWeight: FontWeight.bold))),
                    ],
                  ),
                ),
                // const Divider(),

                SizedBox(height: 10),
                // Leaderboard List
                ListView.builder(
                  shrinkWrap: true,
                  // important!
                  physics: NeverScrollableScrollPhysics(),
                  // disables nested scrolling
                  padding: EdgeInsets.all(0),
                  itemCount: leaderboard.length,
                  itemBuilder: (context, index) {
                    final item = leaderboard[index];
                    bool isUser = item["rank"] == 6;
                    return Container(
                      margin: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      decoration: BoxDecoration(
                        color: isUser ? Color(0xFFFFF8E1) : Colors.white,
                        border: isUser ? Border.all(color: Color(0xFFD4A437)) : Border.all(color: Colors.transparent),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 6,
                            offset: Offset(0, 2),
                          )
                        ],
                      ),
                      child: Row(
                        children: [
                          Expanded(flex: 1, child: Text("${item['rank']}")),
                          Expanded(
                            flex: 3,
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: 12,
                                  child: Image.asset('assets/temp_images/profile.png', width: Get.width / 4.5, height: Get.width / 4.5, fit: BoxFit.cover),
                                ),
                                SizedBox(width: 8),
                                Text(item["name"]),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              "${item['score']}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTopBanner() {
    return Container(
      padding: EdgeInsets.only(top: 0, left: 0, right: 0, bottom: 24),
      decoration: BoxDecoration(
        // gradient: LinearGradient(
        //   // center: Alignment.center,
        //   // radius: 1.0,
        //   colors: [primaryColor, primarylightColor],
        // ),
        color: Color(0xff325F63),
        image: DecorationImage(
            image: AssetImage(
              'assets/bg/Retro_bg.png',
            ),
            fit: BoxFit.cover),
        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(40), bottomRight: Radius.circular(40)),
      ),
      child: Column(
        children: [
          // Row(
          //   children: [
          //     Icon(Icons.arrow_back, color: Colors.white),
          //     SizedBox(width: 10),
          //     Text("Exam Name",
          //         style: TextStyle(color: Colors.white, fontSize: 20)),
          //   ],
          // ),
          AppBar(
            backgroundColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
            elevation: 5,
            centerTitle: true,
            leading: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Container(
                // decoration: BoxDecoration(
                //     color: primaryColor,
                //     borderRadius: BorderRadius.circular(100)
                // ),
                margin: EdgeInsets.all(10),
                child: Icon(
                  Icons.arrow_back,
                  color: textwhiteColor,
                ),
              ),
            ),
            title: Container(
              child: Text(
                data,
                style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w700, fontFamily: font_regular, fontSize: Get.width / 24),
              ),
            ),
          ),
          SizedBox(height: 24),
          Leaderboard()
          // LeaderboardPage()
          // Padding(
          //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
          //   child: Image.asset('assets/temp_images/result.png'),
          // )
        ],
      ),
    );
  }

  Widget buildPodiumUser(String name, String score, String label, double size, {bool highlight = false, String image = "👤"}) {
    return Column(
      children: [
        if (!highlight)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Text(label, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12, color: Colors.black)),
          ),
        Stack(
          alignment: Alignment.center,
          children: [
            CircleAvatar(
              radius: size / 2,
              backgroundColor: Colors.white,
              child: Text(image, style: TextStyle(fontSize: size / 2)),
            ),
            if (highlight)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Color(0xFFFFC107),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(label, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 10)),
                ),
              ),
          ],
        ),
        SizedBox(height: 8),
        Text(name, style: TextStyle(color: Colors.white)),
        Text(score, style: TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }
}

class Leaderboard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
        height: Get.height * .2,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Center (1st Place) - on top
            Positioned(
              top: 0,
              child: LeaderboardItem(
                rank: '1st',
                name: 'Jahana',
                score: '5643',
                imagePath: 'assets/temp_images/profile.png',
                color: Colors.orange,
                textColor: Colors.white,
              ),
            ),

            // Row for 2nd and 3rd
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Spacer(),
                  // 3rd Place - Left
                  Padding(
                    padding: const EdgeInsets.only(bottom: 0), // adjust to lower it
                    child: LeaderboardItem(
                      rank: '3rd',
                      name: 'Anas',
                      score: '5643',
                      imagePath: 'assets/temp_images/profile.png',
                      color: Colors.white,
                      textColor: Colors.black,
                    ),
                  ),
                  Spacer(),
                  Spacer(),
                  Spacer(),
                  // 2nd Place - Right
                  Padding(
                    padding: const EdgeInsets.only(bottom: 0), // slightly higher than 3rd
                    child: LeaderboardItem(
                      rank: '2nd',
                      // Fix typo from '2st' to '2nd'
                      name: 'Arshad',
                      score: '5643',
                      imagePath: 'assets/temp_images/profile.png',
                      color: Colors.white,
                      textColor: Colors.black,
                    ),
                  ),
                  Spacer(),
                ],
              ),
            ),
          ],
        ));
  }
}

class LeaderboardItem extends StatelessWidget {
  final String rank;
  final String name;
  final String score;
  final String imagePath;
  final Color color;
  final Color textColor;

  LeaderboardItem({
    required this.rank,
    required this.name,
    required this.score,
    required this.imagePath,
    required this.color,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          alignment: Alignment.topRight,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundImage: AssetImage(imagePath),
            ),
            Container(
              height: 50,
              width: Get.width * .09,
              decoration: BoxDecoration(
                  image: DecorationImage(
                      colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                      image: AssetImage(
                        'assets/images/hexagon.png',
                      ))),
              child: Center(
                child: Text(
                  rank,
                  style: TextStyle(
                    color: textColor,
                    fontWeight: FontWeight.bold,
                    fontSize: Get.width * .03,
                  ),
                ),
              ),
              // height: 50,
            ),
            // Transform.rotate(
            //   angle: pi *.0,
            //   child: CustomPaint(
            //     size: Size(80, 80),
            //     painter: HexagonPainter(
            //         color: color,
            //       // cornerRadius: 2
            //     ),
            //     child: Padding(
            //       padding: const EdgeInsets.all(8.0),
            //       child: Text(
            //         rank,
            //         style: TextStyle(
            //           color: textColor,
            //           fontWeight: FontWeight.bold,
            //           fontSize: Get.width*.03,
            //         ),
            //       ),
            //     ),
            //   ),
            // ),
          ],
        ),
        SizedBox(height: 10),
        Text(
          name,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          score,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}

class HexagonPainter extends CustomPainter {
  final Color color;
  final double curvature; // Controls how much curve to apply

  HexagonPainter({
    required this.color,
    this.curvature = 0, // Default subtle curve
  });

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()..color = color;
    Path path = Path();

    double w = size.width;
    double h = size.height;

    // Define the six corners of the hexagon
    final points = [
      Offset(w * 0.5, 0), // Top
      Offset(w, h * 0.25), // Top-right
      Offset(w, h * 0.75), // Bottom-right
      Offset(w * 0.5, h), // Bottom
      Offset(0, h * 0.75), // Bottom-left
      Offset(0, h * 0.25), // Top-left
    ];

    // Start at the top point
    path.moveTo(points[0].dx, points[0].dy);

    // Draw curved lines between each point
    for (int i = 0; i < points.length; i++) {
      final current = points[i];
      final next = points[(i + 1) % points.length];

      // Calculate control point for the curve
      final controlX = current.dx + (next.dx - current.dx) * (0.5 - curvature);
      final controlY = current.dy + (next.dy - current.dy) * (0.5 + curvature);

      // Create a quadratic bezier curve
      path.quadraticBezierTo(controlX, controlY, next.dx, next.dy);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
