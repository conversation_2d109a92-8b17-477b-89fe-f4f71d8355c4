// import 'dart:io';
// import 'package:dio/dio.dart';
// import 'package:flutter/material.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:open_file/open_file.dart';
// import 'package:permission_handler/permission_handler.dart';
//
// class aaa extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       home: Scaffold(
//         appBar: AppBar(
//           title: Text('Download PDF Example'),
//         ),
//         body: Center(
//           child: ElevatedButton(
//             onPressed: () {
//               downloadAndSavePDF("https://www.antennahouse.com/hubfs/xsl-fo-sample/pdf/basic-link-1.pdf", "sample");
//             },
//             child: Text("Download PDF"),
//           ),
//         ),
//       ),
//     );
//   }
//
//
// }
//
