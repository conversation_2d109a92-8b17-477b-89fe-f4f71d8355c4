import 'dart:async';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../views/dashboard/dashboard.dart';
import '../views/dashboard/payment/razorpay.dart';

class Plancontroller extends GetxController {
  List plans = [];
  String logo = '';
  bool plansLoading = true;

  get_plans(courseid) async {
    // plansLoading = true;
    // var data = await ApiBasehandler.plans(courseid);
    // plans = data['data']['package_list'];
    // logo = data['data']['logo'].toString();
    // GetStorage().write('razorpay_logo', logo);
    // plansLoading = false;
    // update();
  }

  var backButtonPressTime;
  static const snackBarDuration = Duration(seconds: 2);
  String? status, resp, direction = "forward";
  List ress = [];

  //--------------------------press again start---------------------------------------
  Future<bool> onWillPop() async {
    GetStorage().write('home_index', 0);
    Get.offAll(() => dashboard());
    return true;
  }

  //--------------------------press again end-----------------------------------------

  savepaymentinfo(payid, ordid, signature, pack_id,is_redeemed,redeem_amout) async {
    Timer _timer = Timer(Duration(seconds: 6), () async {
      GetStorage().write('home_index', 0);
      Get.offAll(() => dashboard());
    });

    // var data = await ApiBasehandler.complete_order(pack_id, ordid, payid, signature,is_redeemed,redeem_amout);
    _timer;
  }

  get_create_order(phone, email, desc, amount, raz_key, raz_secret_key, pack_id,is_redeemed,redeem_amount) async {
    var dd = DateTime.now().millisecondsSinceEpoch;
    var receipt = GetStorage().read('user_id').toString() + dd.toString();

    // var data = await ApiBasehandler.create_order(receipt.toString(), int.parse(amount.toString()) * 100, 'INR', pack_id,is_redeemed,redeem_amount);

    // print(data['data']['id'].toString());

    // log("${amount.toString()} Amount ------- ");
    // log("${(int.parse(amount) * 100).toString()} Amount ------- ");
    // Get.to(() => razorpay(
    //       phone: "9946801100",
    //       email: "<EMAIL>",
    //       desc: '',
    //       amount: (int.parse(amount) * 100).toString(),
    //       // amount: amount,
    //       razorp_key: 'rzp_test_lcjbRMiZLJjM4t',
    //       package_id: '1',
    //       order_id: '',
    //       logo: '',
    //       from: 'course_plan', isRedeemed: is_redeemed, redeem_amount: redeem_amount,
    //     ));


    // Get.to(() => razorpay(
    //       phone: phone,
    //       email: email,
    //       desc: desc,
    //       amount: data['data']['amount'].toString(),
    //       razorp_key: raz_key,
    //       package_id: pack_id,
    //       order_id: data['data']['id'].toString(),
    //       logo: logo,
    //       from: 'course_plan',
    //       isRedeemed: is_redeemed.toString(),
    //       redeem_amount: redeem_amount.toString(),
    //     ));
  }
}
