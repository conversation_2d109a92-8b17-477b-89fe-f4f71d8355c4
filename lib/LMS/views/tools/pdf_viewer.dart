import 'dart:async';
import 'package:edutalim/LMS/controllers/mycoursecontroller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';

class pdfviewer_new extends StatefulWidget {
  final String pdf_url, title,id;

  const pdfviewer_new({Key? key, required this.pdf_url, required this.title, required this.id}) : super(key: key);

  @override
  _pdfviewer_newState createState() => _pdfviewer_newState();
}

class _pdfviewer_newState extends State<pdfviewer_new> {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());
  final Completer<PDFViewController> _pdfViewController = Completer<PDFViewController>();
  final StreamController<String> _pageCountController = StreamController<String>();
  int? currentpage, pagecount;

  // void addListenerPreventScreenshot_2() async {
  //   ScreenProtector.addListener(() {
  //     // Screenshot
  //     debugPrint('Screenshot:');
  //     // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
  //     //   content: Text('Screenshot!'),
  //     // ));
  //
  //     // controller.pause();
  //     Get.to(()=> const screen_rec_prev());
  //
  //   }, (isCaptured) {
  //     // Screen Record
  //     debugPrint('Screen Record:');
  //     // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
  //     //   content: Text('Screen Record----!'),
  //     // ));
  //
  //     // controller.pause();
  //     Get.to(()=> const screen_rec_prev());
  //
  //   },);
  // }

  @override
  void initState() {
    controller.updateLessonFileProgress(widget.id.toString());
    // For iOS only.
    // addListenerPreventScreenshot_2();
    // For iOS and Android
    // preventScreenshotOn();
    // protectDataLeakageOn();
    super.initState();
  }

  @override
  void dispose() {
    // For iOS only.
    // removeListenerPreventScreenshot();
    // For iOS and Android
    // preventScreenshotOff();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0.5,
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        title: Text(
          widget.title,
          style: TextStyle(color: Colors.black, fontSize: Get.width / 26),
        ),
        leading: GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: const Icon(
            Icons.arrow_back_ios_rounded,
            color: Colors.black87,
            size: 25,
          ),
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(statusBarColor: Color(0xffffffff), statusBarIconBrightness: Brightness.dark),
        actions: <Widget>[
          // IconButton(
          //   onPressed: ()async{
          //
          //
          //   },
          //   icon: Text("END",style: TextStyle(color: Colors.blue),),
          // ),
          StreamBuilder<String>(
              stream: _pageCountController.stream,
              builder: (_, AsyncSnapshot<String> snapshot) {
                if (snapshot.hasData) {
                  return Center(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        // color: Colors.blue[900],
                      ),
                      child: Text(
                        "Page ${snapshot.data!}",
                        style: const TextStyle(color: Colors.black),
                      ),
                    ),
                  );
                }
                return const SizedBox();
              }),
          // if(widget.title.isNotEmpty)
          //   GestureDetector(
          //       onTap: ()async{
          //
          //         var urls= widget.pdf_url.toString();
          //         final url=Uri.parse(urls);
          //         final res =await http.get(url);
          //         final bytes =res.bodyBytes;
          //         final temp_images = await getTemporaryDirectory();
          //         final path ='${temp_images.path}/${widget.title}.pdf';
          //         File(path).writeAsBytesSync(bytes);
          //         Share.shareXFiles([XFile(path)]);
          //
          //
          //
          //
          //         // print(1);
          //         //  if(progress!=0.0){
          //         // toast_success(context, 'Downloaded');
          //         //  }
          //
          //
          //         // downloadFile(widget.url,'${widget.title}.pdf' );
          //
          //
          //
          //         // await  downloadPdf(widget.url, '${widget.title}.pdf');
          //         // await  downloadPdf(widget.url, widget.url);
          //         // var tempDir = await getTemporaryDirectory();
          //         // download(Dio(), imageUrl, tempDir.path + fileName);
          //         // ------    download(Dio(), widget.url, '${widget.title}.pdf');
          //         // FileStorage.writeCounter(widget.url, '${widget.title}.pdf');
          //       },
          //       child: Container(
          //         color:Colors.transparent,
          //         padding:const EdgeInsets.all(15),
          //         child: const Icon(Icons.file_download_rounded,color: Colors.black,),)),
        ],
      ),
      body: PDF(
        onPageChanged: (int? current, int? total) async {
          _pageCountController.add('${current! + 1} / $total');
        },
        onViewCreated: (PDFViewController pdfViewController) async {
          _pdfViewController.complete(pdfViewController);
          final int currentPage = await pdfViewController.getCurrentPage() ?? 0;
          final int? pageCount = await pdfViewController.getPageCount();
          _pageCountController.add('${currentPage + 1} - $pageCount');
          setState(() {
            pagecount = pageCount;
            currentpage = ((currentPage + 1) - pageCount!).toInt();
          });
        },
      ).cachedFromUrl(
        widget.pdf_url,
        placeholder: (double progress) => Center(
            child: Container(
          alignment: Alignment.center,
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.width / 2,
          child: Stack(
            children: [
              SizedBox(
                height: MediaQuery.of(context).size.width / 7,
                width: MediaQuery.of(context).size.width / 7,
                child: CircularProgressIndicator(
                  color: primaryColor,
                ),
              ),
              Positioned(
                top: 20,
                left: 10,
                child: Text(
                  '$progress %',
                  style: TextStyle(fontSize: MediaQuery.of(context).size.width / 37),
                ),
              )
            ],
          ),
        )),
        errorWidget: (dynamic error) => Center(
          child: Container(
            child: Column(
              children: [
                SvgPicture.asset(
                  'img_svg/pdf.svg',
                  height: MediaQuery.of(context).size.width / 6,
                  width: MediaQuery.of(context).size.width / 6,
                ),
                const SizedBox(
                  height: 20,
                ),
                const Text(
                  "Can't load pdf  :(",
                  style: TextStyle(color: Colors.black54),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
