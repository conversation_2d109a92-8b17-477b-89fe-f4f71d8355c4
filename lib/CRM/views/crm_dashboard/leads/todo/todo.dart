import 'package:edutalim/CRM/controller/todocontroller.dart';
import 'package:edutalim/CRM/views/crm_dashboard/leads/todo/add_task.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:edutalim/components/constants.dart';
import 'package:get_storage/get_storage.dart';

class Todo extends StatefulWidget {
  const Todo({super.key});

  @override
  State<Todo> createState() => _TodoState();
}

class _TodoState extends State<Todo> with SingleTickerProviderStateMixin {
  Todocontroller controller = Get.put(Todocontroller());
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    controller.call_todo();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Todocontroller>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: textwhiteColor,
          appBar: AppBar(
            backgroundColor: textwhiteColor,
            elevation: 0,
            title: Text(
              'TODO',
              style: TextStyle(
                  color: textblackColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: textblackColor),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            bottom: TabBar(
              padding: EdgeInsets.all(10),
              indicatorSize: TabBarIndicatorSize.tab,
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                color: primaryColor,
                // shape: BoxShape.circle,
              ),
              dividerHeight: 0,
              labelColor: textwhiteColor,
              unselectedLabelColor: Colors.grey,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.normal,
              ),
              tabs: const [
                Tab(text: 'To Do'),
                Tab(text: 'Completed'),
              ],
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              Get.to(AddTaskPage());
            },
            backgroundColor: primaryColor,
            child: Icon(
              Icons.add,
              color: textwhiteColor,
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              // To Do Tab

              ListView.builder(
                padding: const EdgeInsets.all(10),
                itemCount: controller.pending.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12.0),
                    child: TodoCard(data: controller.pending[index]),
                  );
                },
              ),

              // Completed Tab
              ListView.builder(
                padding: const EdgeInsets.all(10),
                itemCount: controller.completed.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12.0),
                    child: TodoCard(data: controller.completed[index]),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

class TodoCard extends StatefulWidget {
  final Map<String, dynamic> data;
  const TodoCard({super.key, required this.data});

  @override
  State<TodoCard> createState() => _TodoCardState();
}

class _TodoCardState extends State<TodoCard> {
  final box = GetStorage();
  final Todocontroller controller = Get.find();

  late String todoId;

  @override
  void initState() {
    super.initState();
    todoId = widget.data['id']?.toString() ?? '';
  }

  void _handleCheckboxChange(bool? value) async {
    final isAdmin = box.read('user_role').toString() == '1';

    if (value == true || isAdmin) {
      await controller.mark_complete(widget.data['id'].toString());
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text("Only admins can mark this as incomplete."),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final completed = widget.data['is_completed'].toString() == 'true';

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color.fromARGB(255, 198, 197, 197)),
        color: completed ? Colors.grey.shade200 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.15),
            spreadRadius: 2,
            blurRadius: 6,
            offset: const Offset(0, 3),
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Title
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              widget.data['title'] ?? 'No Title',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                decoration: completed ? TextDecoration.lineThrough : null,
                color: Colors.black87,
              ),
            ),
          ),

          /// Description
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(0)),
            child: Text(
              widget.data['description'] ?? 'No Description',
              style: TextStyle(fontSize: 14, color: Colors.grey[800]),
            ),
          ),

          const SizedBox(height: 12),

          /// Due Date
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 16, color: Colors.blue),
                const SizedBox(width: 6),
                Text(
                  'Due Date: ',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade700,
                  ),
                ),
                Expanded(
                  child: Text(
                    widget.data['due_date'] ?? 'Not Set',
                    style: const TextStyle(fontSize: 13, color: Colors.black87),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 15),

          /// Completed Checkbox
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(10),
                bottomRight: Radius.circular(10),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  completed ? 'Mark as Pending' : 'Mark as Completed',
                  style: TextStyle(color: primaryColor, fontSize: 16),
                ),
                Checkbox(
                  value: completed,
                  onChanged: _handleCheckboxChange,
                  activeColor: primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _labelRow(String title, String value, {bool italic = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("$title : ",
            style: TextStyle(
                color: Colors.black54,
                fontStyle: italic ? FontStyle.italic : FontStyle.normal,
                fontSize: Get.width / 30)),
        const SizedBox(width: 5),
        Expanded(
          child: Text(value,
              style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                  fontFamily: 'poppins_regular')),
        ),
      ],
    );
  }

  Widget _timestamp(String label, String value, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(color: color, fontSize: Get.width / 38)),
        Text(value, style: TextStyle(color: color, fontSize: Get.width / 33)),
      ],
    );
  }

  Widget _actionButton(
      {required Widget icon,
      required String label,
      required Color color,
      required VoidCallback onTap}) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          color: Colors.transparent,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon,
              const SizedBox(width: 6),
              Text(label,
                  textAlign: TextAlign.center,
                  style:
                      TextStyle(color: color, fontFamily: "poppins_regular")),
            ],
          ),
        ),
      ),
    );
  }
}
