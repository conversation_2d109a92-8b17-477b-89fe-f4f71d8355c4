import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GroupChatTextField extends StatelessWidget {
  GroupChatTextField(
      {super.key,
      required this.focusNode,
      required this.controller,
      required this.onTap,
      required this.onChanged});
  FocusNode? focusNode;
  TextEditingController? controller;
  void Function(String)? onChanged;
  void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return TextField(
      focusNode: focusNode,
      onTap: onTap,
      onChanged: onChanged,
      maxLines: null,
      keyboardType: TextInputType.text,
      controller: controller,
      style: TextStyle(fontSize: Get.width / 28, color: Colors.black),
      decoration: InputDecoration(
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.transparent, width: 1),
          borderRadius: BorderRadius.circular(10.0),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Colors.transparent, width: 1),
          borderRadius: BorderRadius.circular(10.0),
        ),
        filled: true,
        hintStyle: TextStyle(
            // fontFamily: font_regular,
            color: Colors.black38,
            fontSize: Get.width / 28),
        hintText: "Type your message here",
        fillColor: Colors.transparent,
        contentPadding:
            const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20),
      ),
    );
  }
}
