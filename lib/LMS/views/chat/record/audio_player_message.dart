// import 'dart:async';
// import 'package:audioplayers/audioplayers.dart' as ap;
// import 'package:audioplayers/audioplayers.dart';
// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/controller/chat/speekichatcontroller.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// class AudioPlayermessage extends StatefulWidget {
//   /// Path from where to play recorded audio
//   final String source, duration;
//   final int index;
//   final bool sender_is_ai;
//
//   /// Callback when audio file should be removed
//   /// Setting this to null hides the delete button
//   final VoidCallback onDelete;
//
//   const AudioPlayermessage({
//     super.key,
//     required this.source,
//     required this.duration,
//     required this.index,
//     required this.onDelete,
//     required this.sender_is_ai,
//   });
//
//   @override
//   AudioPlayerState createState() => AudioPlayerState();
// }
//
// class AudioPlayerState extends State<AudioPlayermessage> {
//   // static const double _controlSize = 56;
//   // static const double _deleteBtnSize = 24;
//   //
//   // final _audioPlayer = ap.AudioPlayer()..setReleaseMode(ReleaseMode.stop);
//   // late StreamSubscription<void> _playerStateChangedSubscription;
//   // late StreamSubscription<Duration?> _durationChangedSubscription;
//   // late StreamSubscription<Duration> _positionChangedSubscription;
//   // Duration? _position;
//   // Duration? _duration;
//
//   @override
//   void initState() {
//     controller.playerStateChangedSubscription =
//         controller.chat_audioPlayer.onPlayerComplete.listen((state) async {
//       await stop();
//     });
//     controller.positionChangedSubscription =
//         controller.chat_audioPlayer.onPositionChanged.listen(
//       (position) => setState(() {
//         controller.chat_position = position;
//       }),
//     );
//     controller.durationChangedSubscription =
//         controller.chat_audioPlayer.onDurationChanged.listen(
//       (duration) => setState(() {
//         controller.chat_duration = duration;
//       }),
//     );
//
//     controller.chat_audioPlayer.setSource(_source);
//
//     super.initState();
//   }
//
//   @override
//   void dispose() {
//     controller.playerStateChangedSubscription.cancel();
//     controller.positionChangedSubscription.cancel();
//     controller.durationChangedSubscription.cancel();
//     controller.chat_audioPlayer.dispose();
//     super.dispose();
//   }
//
//   Speekichatcontroller controller = Get.put(Speekichatcontroller());
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<Speekichatcontroller>(builder: (controller) {
//       return LayoutBuilder(
//         builder: (context, constraints) {
//           return Container(
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Container(
//                   decoration: BoxDecoration(
//                     color: !widget.sender_is_ai
//                         ? textwhiteColor
//                         : secondaryColor.withOpacity(0.3),
//                     borderRadius: BorderRadius.circular(1000),
//                   ),
//                   padding: EdgeInsets.symmetric(horizontal: 5),
//                   child: Column(
//                     children: [
//                       controller.current_audio_index == widget.index
//                           ? Row(
//                               children: [
//                                 _buildControl(),
//                                 Column(
//                                   children: [
//                                     Row(
//                                       children: [
//                                         Container(
//                                           width: Get.width / 2.4,
//                                           padding: EdgeInsets.symmetric(
//                                               vertical: 10),
//                                           child: controller
//                                                       .chat_audioPlayer.state ==
//                                                   ap.PlayerState.playing
//                                               ? Row(
//                                                   mainAxisSize:
//                                                       MainAxisSize.min,
//                                                   mainAxisAlignment:
//                                                       MainAxisAlignment.center,
//                                                   children: [
//                                                     Image.asset(
//                                                         'assets/lottie/playing.gif',
//                                                         height: 30),
//                                                     Image.asset(
//                                                         'assets/lottie/playing.gif',
//                                                         height: 30)
//                                                   ],
//                                                 )
//                                               : Container(
//                                                   margin: EdgeInsets.symmetric(
//                                                       horizontal: 10,
//                                                       vertical: 13),
//                                                   height: 3,
//                                                   decoration: BoxDecoration(
//                                                     color: Colors.black
//                                                         .withOpacity(0.1),
//                                                     borderRadius:
//                                                         BorderRadius.circular(
//                                                             1000),
//                                                   ),
//                                                 ),
//                                           // Text('')
//                                           // _buildSlider(constraints.maxWidth),
//                                         ),
//                                       ],
//                                     ),
//
//                                     // Container(
//                                     //   // color: Colors.yellow,
//                                     //   width: Get.width/1.6,
//                                     //   child: Row(
//                                     //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     //     children: [
//                                     //       Container(
//                                     //         child: Text('${_duration.toString().split('.')[0] ?? 0.0}',style: TextStyle(fontSize: Get.width/35),),
//                                     //       ),
//                                     //       Container(
//                                     //         child: Text('  ${_position.toString().split('.')[0] ?? 0.0}',style: TextStyle(fontSize: Get.width/35),),
//                                     //       ),
//                                     //     ],
//                                     //   ),
//                                     // ),
//                                   ],
//                                 ),
//                                 Container(
//                                   child: controller.chat_duration == null
//                                       ? Text(
//                                           '--:--',
//                                           style: TextStyle(
//                                               fontSize: Get.width / 35),
//                                         )
//                                       : Text(
//                                           controller.chat_position != null
//                                               ? controller.chat_audioPlayer
//                                                           .state ==
//                                                       ap.PlayerState.playing
//                                                   ? controller.chat_position!
//                                                           .inMinutes
//                                                           .toString()
//                                                           .padLeft(2, '0') +
//                                                       ":" +
//                                                       controller.chat_position!
//                                                           .inSeconds
//                                                           .toString()
//                                                           .padLeft(2, '0')
//                                                   : controller.chat_duration!
//                                                           .inMinutes
//                                                           .toString()
//                                                           .padLeft(2, '0') +
//                                                       ":" +
//                                                       controller.chat_duration!
//                                                           .inSeconds
//                                                           .toString()
//                                                           .padLeft(2, '0')
//                                               : '--:--',
//                                           style: TextStyle(
//                                               fontSize: Get.width / 35),
//                                         ),
//                                 ),
//                               ],
//                             )
//                           : Row(
//                               children: [
//                                 _buildControlinactive(),
//                                 Column(
//                                   children: [
//                                     Row(
//                                       children: [
//                                         Container(
//                                           width: Get.width / 2.4,
//                                           child: _buildSliderinactive(
//                                               constraints.maxWidth),
//                                         ),
//                                       ],
//                                     ),
//                                   ],
//                                 ),
//                                 Container(
//                                   child: controller.chat_duration == null
//                                       ? Text(
//                                           '--:--',
//                                           style: TextStyle(
//                                               fontSize: Get.width / 35),
//                                         )
//                                       : Text(
//                                           widget.duration.toString(),
//                                           style: TextStyle(
//                                               fontSize: Get.width / 35),
//                                         ),
//                                 ),
//                               ],
//                             ),
//                     ],
//                   ),
//                 ),
//                 SizedBox(
//                   height: 10,
//                 ),
//               ],
//             ),
//           );
//         },
//       );
//     });
//   }
//
//   Widget _buildControl() {
//     Icon icon;
//     Color color;
//
//     if (controller.chat_audioPlayer.state == ap.PlayerState.playing) {
//       icon = Icon(
//         Icons.pause,
//         color: Colors.green,
//       );
//       color = Colors.green.withOpacity(0.1);
//     } else {
//       final theme = Theme.of(context);
//       icon = Icon(
//         Icons.play_arrow,
//         color: theme.primaryColor,
//       );
//       color = theme.primaryColor.withOpacity(0.1);
//     }
//
//     return ClipOval(
//       child: Material(
//         color: color,
//         child: InkWell(
//           child: Container(padding: EdgeInsets.all(8), child: icon),
//           onTap: () {
//             setState(() {
//               controller.chat_position = Duration.zero;
//               controller.current_audio_index = widget.index;
//             });
//
//             if (controller.chat_audioPlayer.state == ap.PlayerState.playing) {
//               pause();
//             } else {
//               play();
//             }
//           },
//         ),
//       ),
//     );
//   }
//
//   Widget _buildControlinactive() {
//     Icon icon;
//     Color color;
//
//     final theme = Theme.of(context);
//     icon = Icon(
//       Icons.play_arrow,
//       color: theme.primaryColor,
//     );
//     color = theme.primaryColor.withOpacity(0.1);
//
//     return ClipOval(
//       child: Material(
//         color: color,
//         child: InkWell(
//           child: Container(padding: EdgeInsets.all(8), child: icon),
//           onTap: () {
//             setState(() {
//               controller.current_audio_index = widget.index;
//             });
//
//             // if (controller.chat_audioPlayer.state == ap.PlayerState.playing) {
//             //   pause();
//             // } else {
//             play();
//             // }
//           },
//         ),
//       ),
//     );
//   }
//
//   Widget _buildSlider(double widgetWidth) {
//     bool canSetValue = false;
//     final duration = controller.chat_duration;
//     final position = controller.chat_position;
//
//     if (duration != null && position != null) {
//       canSetValue = position.inMilliseconds > 0;
//       canSetValue &= position.inMilliseconds < duration.inMilliseconds;
//     }
//
//     // double width = widgetWidth - controller.controlSize - controller.deleteBtnSize;
//     // width -= _deleteBtnSize;
//
//     return Slider(
//       activeColor: primaryColor,
//       inactiveColor: secondaryColor,
//       onChanged: (v) {
//         print(v.toString() + '----------');
//
//         if (duration != null) {
//           final position = v * duration.inMilliseconds;
//           controller.chat_audioPlayer
//               .seek(Duration(milliseconds: position.round()));
//         }
//       },
//       value: canSetValue && duration != null && position != null
//           ? position.inMilliseconds / duration.inMilliseconds
//           : 0.0,
//     );
//   }
//
//   Widget _buildSliderinactive(double widgetWidth) {
//     bool canSetValue = false;
//     final duration = controller.chat_duration;
//     final position = controller.chat_position;
//
//     if (duration != null && position != null) {
//       canSetValue = position.inMilliseconds > 0;
//       canSetValue &= position.inMilliseconds < duration.inMilliseconds;
//     }
//
//     // double width = widgetWidth - controller.controlSize - controller.deleteBtnSize;
//     // width -= _deleteBtnSize;
//
//     return Slider(
//       activeColor: primaryColor,
//       inactiveColor: secondaryColor,
//       onChanged: (v) {
//         print(v.toString() + '----------');
//
//         if (duration != null) {
//           // final position = v * duration.inMilliseconds;
//           // controller.chat_audioPlayer.seek(Duration(milliseconds: position.round()));
//         }
//       },
//       value: 0.0,
//     );
//   }
//
//   Future<void> play() => controller.chat_audioPlayer.play(_source);
//
//   Future<void> pause() async {
//     await controller.chat_audioPlayer.pause();
//     setState(() {});
//   }
//
//   Future<void> stop() async {
//     await controller.chat_audioPlayer.stop();
//     setState(() {});
//   }
//
//   Source get _source =>
//       // kIsWeb ?
//       ap.UrlSource(widget.source)
//       //     :
//       // ap.DeviceFileSource(widget.source)
//       ;
// }
//
//
//
//
// // Container(
// //   width: 100,
// //   height: 50,
// //   child: TweenAnimationBuilder(
// //       tween: Tween(begin: 0.0, end: 1.0),
// //       duration: Duration(seconds: 4),
// //       builder: (context, value, child) {
// //         return Container(
// //           width: 500,
// //           height: 100,
// //           child: Stack(
// //             children: [
// //               ShaderMask(
// //                 shaderCallback: (rect) {
// //                   return LinearGradient(
// //                       begin: Alignment.centerLeft,
// //                       end: Alignment.centerRight,
// //                       stops: [
// //                         value,
// //                         value
// //                       ],
// //                       colors: [
// //                         Colors.blue,
// //                         Colors.grey.withAlpha(100)
// //                       ]).createShader(rect);
// //                 },
// //                 child: Container(
// //                     width: 500,
// //                     height: 100,
// //                     child: Image.asset("assets/img/audio_wave.png"),
// //                     // decoration: BoxDecoration(
// //                     //     image: DecorationImage(
// //                     //         image: Image.asset("assets/img/audio_wave.png").image,
// //                     //     ),
// //                     //   // color: Colors.red,
// //                     // )
// //
// //                 ),
// //               ),
// //             ],
// //           ),
// //         );
// //       }),
// // )