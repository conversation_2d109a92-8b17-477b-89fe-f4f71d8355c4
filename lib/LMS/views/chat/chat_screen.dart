// // import 'package:audioplayers/audioplayers.dart';
// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/components/utils.dart';
// import 'package:prepscale/view/chat/controller/speekichatcontroller.dart';
// import 'package:prepscale/view/chat/record/audio_player_message.dart';
// import 'package:prepscale/view/chat/record/audio_recorder.dart';
// import 'package:prepscale/view/chat/session_overview.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_html/flutter_html.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:lottie/lottie.dart';
//
// class chat_screen extends StatefulWidget {
//   String sel_country;
//   chat_screen({required this.sel_country});
//
//   @override
//   State<chat_screen> createState() => _chat_screenState();
// }
//
// class _chat_screenState extends State<chat_screen> {
//
//   Speekichatcontroller controller = Get.put(Speekichatcontroller());
//   int? sel_index;
//   // final AudioPlayer _audioPlayer = AudioPlayer();
//   @override
//   void dispose() {
//     controller.spchatscrollController.dispose();
//     super.dispose();
//   }
//
//   // void _playAudio() async {
//   //   await _audioPlayer.play(AssetSource('assets/lottie/recording_stop.mp3'));
//   // }
//
//   @override
//   void initState() {
//     super.initState();
//     controller.call_chats(widget.sel_country.toString(), '', 'true', 'true');
//   }
//
//   String normalizeString(String input) {
//     return input
//         .replaceAll(RegExp(r'‘|’'), "'") // Replace curly single quotes
//         .replaceAll(RegExp(r'“|”'), '"') // Replace curly double quotes
//         .replaceAll('…', '...') // Replace ellipsis
//         .replaceAll(RegExp(r'–|—'), '-') // Replace dashes with standard dash
//         .replaceAll('a^', ',') // Replace 'a^' with a normal comma
//         .replaceAll(
//             RegExp(r'[^\x00-\x7F]+'), "'"); // Remove any non-ASCII characters
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<Speekichatcontroller>(builder: (controller) {
//       return GestureDetector(
//         onTap: () {
//           FocusScope.of(context).unfocus();
//         },
//         child: Scaffold(
//           appBar: controller.isspchatloading
//               ? AppBar()
//               : AppBar(
//                   backgroundColor: textwhiteColor,
//                   surfaceTintColor: textwhiteColor,
//                   leading: GestureDetector(
//                     onTap: () {
//                       Get.back();
//                     },
//                     child: Container(
//                       padding: const EdgeInsets.all(10),
//                       child: Image.asset('assets/icons/close.png'),
//                     ),
//                   ),
//                   title: Container(
//                     child: Row(
//                       children: [
//                         Image.asset(
//                           'assets/logo/logo_plain.png',
//                           width: Get.width / 11,
//                         ),
//                         const SizedBox(
//                           width: 10,
//                         ),
//                         Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Container(
//                               alignment: Alignment.centerLeft,
//                               child: Text(
//                                 '$appName',
//                                 style: TextStyle(
//                                     color: textblackColor,
//                                     fontFamily: font_medium,
//                                     fontSize: Get.width / 26),
//                                 textAlign: TextAlign.center,
//                               ),
//                             ),
//                             const SizedBox(
//                               height: 3,
//                             ),
//                             Container(
//                               alignment: Alignment.centerLeft,
//                               child: Text(
//                                 'Online',
//                                 style: TextStyle(
//                                     color: primaryColor,
//                                     fontFamily: font_regular,
//                                     fontSize: Get.width / 33),
//                                 textAlign: TextAlign.center,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                   actions: [
//                     // IconButton(onPressed: _playAudio, icon: Icon(Icons.ac_unit))
//                     // Image.asset('assets/icons/more_vert.png',height: Get.width/19,),
//                     // SizedBox(width: 15,),
//                   ],
//                 ),
//           body: controller.isspchatloading
//               ? loader()
//               : Container(
//                   child: Column(
//                     children: [
//                       Expanded(
//                         child: Container(
//                           color: chatbgColor,
//                           child: Stack(
//                             children: [
//                               Container(
//                                 child: SingleChildScrollView(
//                                   controller: controller.spchatscrollController,
//                                   child: Container(
//                                     padding: const EdgeInsets.symmetric(
//                                         horizontal: 15),
//                                     child: Column(
//                                       children: [
//                                         const SizedBox(
//                                           height: 10,
//                                         ),
//                                         // Text(controller.my_messages.toString()),
//                                         ListView.builder(
//                                           physics:
//                                               const NeverScrollableScrollPhysics(),
//                                           // padding: EdgeInsets.only(bottom: Get.height/5.5),
//                                           padding: const EdgeInsets.all(0),
//                                           shrinkWrap: true,
//                                           itemCount: controller
//                                               .spchatdata[0]['chat'].length,
//                                           itemBuilder: (BuildContext context,
//                                               int index) {
//                                             Map data = controller.spchatdata[0]
//                                                 ['chat'][index];
//
//                                             // print('file path-------------------');
//                                             // print(data['audio'].toString());
//                                             //
//                                             //
//                                             // final waveformData =  controller.wavecontroller.extractWaveformData(
//                                             //   path: '/var/mobile/Containers/Data/Application/E75FB819-B710-44A0-B75B-3BD65094F8A2/Documents/audio_1727417227245.m4a',
//                                             //   noOfSamples: 100,
//                                             // );
//                                             // // Or directly extract from preparePlayer and initialise audio player
//                                             //  controller.wavecontroller.preparePlayer(
//                                             //   path: '/var/mobile/Containers/Data/Application/E75FB819-B710-44A0-B75B-3BD65094F8A2/Documents/audio_1727417227245.m4a',
//                                             //   shouldExtractWaveform: true,
//                                             //   noOfSamples: 100,
//                                             //   volume: 1.0,
//                                             // );
//
//                                             return data['sender'].toString() ==
//                                                     'ai'
//                                                 ?
//
//                                                 // ai
//                                                 GestureDetector(
//                                                     onTap: () {},
//                                                     child: Container(
//                                                       alignment:
//                                                           Alignment.centerLeft,
//                                                       margin: const EdgeInsets
//                                                           .symmetric(
//                                                           vertical: 12),
//                                                       child: Row(
//                                                         mainAxisSize:
//                                                             MainAxisSize.min,
//                                                         crossAxisAlignment:
//                                                             CrossAxisAlignment
//                                                                 .start,
//                                                         children: [
//                                                           Container(
//                                                             decoration:
//                                                                 BoxDecoration(
//                                                                     color:
//                                                                         textwhiteColor,
//                                                                     borderRadius:
//                                                                         const BorderRadius
//                                                                             .only(
//                                                                       topRight:
//                                                                           Radius.circular(
//                                                                               50),
//                                                                       bottomRight:
//                                                                           Radius.circular(
//                                                                               50),
//                                                                       topLeft: Radius
//                                                                           .circular(
//                                                                               0),
//                                                                       bottomLeft:
//                                                                           Radius.circular(
//                                                                               50),
//                                                                     ),
//                                                                     boxShadow: [
//                                                                   // BoxShadow(
//                                                                   //     color:
//                                                                   //         textwhiteColor,
//                                                                   //     blurRadius:
//                                                                   //         10,
//                                                                   //     spreadRadius:
//                                                                   //         1),
//                                                                 ]),
//                                                             constraints:
//                                                                 BoxConstraints(
//                                                               maxWidth:
//                                                                   Get.width /
//                                                                       1.35,
//                                                             ),
//                                                             padding:
//                                                                 const EdgeInsets
//                                                                     .symmetric(
//                                                                     horizontal:
//                                                                         15,
//                                                                     vertical:
//                                                                         13),
//                                                             child: Column(
//                                                               children: [
//                                                                 // AudioFileWaveforms(
//                                                                 //     size: Size(MediaQuery.of(context).size.width, 100.0),
//                                                                 //     playerController: controller.wavecontroller,
//                                                                 //     enableSeekGesture: true,
//                                                                 //     waveformType: WaveformType.long,
//                                                                 //     waveformData: [],
//                                                                 //     playerWaveStyle: const PlayerWaveStyle(
//                                                                 //         fixedWaveColor: Colors.white54,
//                                                                 //         liveWaveColor: Colors.blueAccent,
//                                                                 //         spacing: 6,
//                                                                 //     ),
//                                                                 // ),
//
//                                                                 Container(
//                                                                   alignment:
//                                                                       Alignment
//                                                                           .centerLeft,
//                                                                   margin: const EdgeInsets
//                                                                       .only(
//                                                                       right: 13,
//                                                                       bottom:
//                                                                           10),
//                                                                   child: Image
//                                                                       .asset(
//                                                                     // controller.chat_audioPlayer.state.toString() ==
//                                                                     //         'PlayerState.playing'
//                                                                     //     ? 'assets/logo/logo_plain_bottom.png'
//                                                                     //     :
//                                                                     'assets/logo/logo_plain_right.png',
//                                                                     width:
//                                                                         Get.width /
//                                                                             10,
//                                                                   ),
//                                                                 ),
//
//                                                                 // Text(controller.chat_audioPlayer.state.toString()),
//
//                                                                 // if (data['audio']
//                                                                 //         .toString() !=
//                                                                 //     "")
//                                                                 //   // Container(
//                                                                 //   //   child: Html(
//                                                                 //   //     data: data[
//                                                                 //   //             'duration']
//                                                                 //   //         .toString(),
//                                                                 //   //   ),
//                                                                 //   // ),
//                                                                 //
//                                                                 //   AudioPlayermessage(
//                                                                 //     // source: 'https://commondatastorage.googleapis.com/codeskulptor-demos/DDR_assets/Kangaroo_MusiQue_-_The_Neverwritten_Role_Playing_Game.mp3',
//                                                                 //     index:
//                                                                 //         index,
//                                                                 //     duration: data[
//                                                                 //             'duration']
//                                                                 //         .toString(),
//                                                                 //     source: data[
//                                                                 //             'audio']
//                                                                 //         .toString(),
//                                                                 //     sender_is_ai:
//                                                                 //         true,
//                                                                 //     onDelete:
//                                                                 //         () {
//                                                                 //       // setState(() => controller.showPlayer = false);
//                                                                 //     },
//                                                                 //   ),
//                                                                 Container(
//                                                                   child: Html(
//                                                                       data: normalizeString(
//                                                                           data['message']
//                                                                               .toString())),
//                                                                 ),
//                                                                 // Text(
//                                                                 //   normalizeString(
//                                                                 //       data['message']
//                                                                 //           .toString()),
//                                                                 //   style: TextStyle(
//                                                                 //       color:
//                                                                 //           textblackColor,
//                                                                 //       fontFamily:
//                                                                 //           font_medium),
//                                                                 // ),
//
//                                                                 const SizedBox(
//                                                                   height: 15,
//                                                                 ),
//                                                               ],
//                                                             ),
//                                                           ),
//                                                         ],
//                                                       ),
//                                                     ))
//                                                 :
//                                                 // user
//                                                 GestureDetector(
//                                                     onTap: () {
//                                                       setState(() {
//                                                         if (sel_index ==
//                                                             index) {
//                                                           sel_index = null;
//                                                         } else {
//                                                           sel_index = index;
//                                                         }
//                                                       });
//                                                     },
//                                                     child: Container(
//                                                       alignment:
//                                                           Alignment.centerRight,
//                                                       margin: const EdgeInsets
//                                                           .symmetric(
//                                                           vertical: 12),
//                                                       child: Row(
//                                                         mainAxisSize:
//                                                             MainAxisSize.min,
//                                                         crossAxisAlignment:
//                                                             CrossAxisAlignment
//                                                                 .end,
//                                                         children: [
//                                                           Container(
//                                                             decoration:
//                                                                 BoxDecoration(
//                                                               color: secondaryColor
//                                                                   .withOpacity(
//                                                                       0.3),
//                                                               borderRadius:
//                                                                   const BorderRadius
//                                                                       .only(
//                                                                 topRight: Radius
//                                                                     .circular(
//                                                                         50),
//                                                                 bottomRight:
//                                                                     Radius
//                                                                         .circular(
//                                                                             0),
//                                                                 topLeft: Radius
//                                                                     .circular(
//                                                                         50),
//                                                                 bottomLeft: Radius
//                                                                     .circular(
//                                                                         50),
//                                                               ),
//                                                             ),
//                                                             constraints:
//                                                                 BoxConstraints(
//                                                               maxWidth:
//                                                                   Get.width /
//                                                                       1.35,
//                                                             ),
//                                                             padding:
//                                                                 const EdgeInsets
//                                                                     .symmetric(
//                                                                     horizontal:
//                                                                         15,
//                                                                     vertical:
//                                                                         23),
//                                                             child: Column(
//                                                               children: [
//                                                                 Align(
//                                                                     alignment:
//                                                                         Alignment
//                                                                             .centerLeft,
//                                                                     child: Icon(
//                                                                       data['sender'].toString() == 'user' &&
//                                                                               sel_index ==
//                                                                                   index
//                                                                           ? Icons
//                                                                               .keyboard_arrow_up_outlined
//                                                                           : Icons
//                                                                               .keyboard_arrow_down_outlined,
//                                                                       size: 25,
//                                                                     )),
//                                                                 // if(data['audio'].toString() != "")
//                                                                 //   AudioPlayermessage(
//                                                                 //   // source: 'https://commondatastorage.googleapis.com/codeskulptor-demos/DDR_assets/Kangaroo_MusiQue_-_The_Neverwritten_Role_Playing_Game.mp3',
//                                                                 //   source: data['audio'].toString(),
//                                                                 //   onDelete: () {
//                                                                 //     // setState(() => controller.showPlayer = false);
//                                                                 //   },
//                                                                 // ),
//
//                                                                 // if (data['audio']
//                                                                 //         .toString() !=
//                                                                 //     "")
//                                                                 //   AudioPlayermessage(
//                                                                 //     // source: 'https://commondatastorage.googleapis.com/codeskulptor-demos/DDR_assets/Kangaroo_MusiQue_-_The_Neverwritten_Role_Playing_Game.mp3',
//                                                                 //     index:
//                                                                 //         index,
//                                                                 //     duration: data[
//                                                                 //             'duration']
//                                                                 //         .toString(),
//                                                                 //     source: data[
//                                                                 //             'audio']
//                                                                 //         .toString(),
//                                                                 //     sender_is_ai:
//                                                                 //         false,
//                                                                 //     onDelete:
//                                                                 //         () {
//                                                                 //       // setState(() => controller.showPlayer = false);
//                                                                 //     },
//                                                                 //   ),
//                                                                 Align(
//                                                                   alignment:
//                                                                       Alignment
//                                                                           .centerRight,
//                                                                   child: Html(
//                                                                       shrinkWrap:
//                                                                           true,
//                                                                       data: data[
//                                                                               'message']
//                                                                           .toString()),
//                                                                 ),
//                                                                 if (data['sender']
//                                                                             .toString() ==
//                                                                         'user' &&
//                                                                     sel_index ==
//                                                                         index)
//                                                                   Column(
//                                                                     children: [
//                                                                       Container(
//                                                                         padding:
//                                                                             EdgeInsets.symmetric(vertical: 10),
//                                                                         child:
//                                                                             Divider(
//                                                                           thickness:
//                                                                               0.5,
//                                                                           color:
//                                                                               secondaryColor,
//                                                                         ),
//                                                                       ),
//                                                                       Container(
//                                                                         // color: Colors.yellow,
//                                                                         alignment:
//                                                                             Alignment.centerRight,
//                                                                         child:
//                                                                             Html(
//                                                                           style: {},
//                                                                           data:
//                                                                               data['feedback'].toString(),
//                                                                         ),
//                                                                         // Text(data['message'].toString()),
//                                                                       ),
//                                                                     ],
//                                                                   ),
//                                                                 // Container(
//                                                                 //   alignment:
//                                                                 //       Alignment
//                                                                 //           .centerRight,
//                                                                 //   child: Text(
//                                                                 //     normalizeString(
//                                                                 //         data['message']
//                                                                 //             .toString()),
//                                                                 //     style: TextStyle(
//                                                                 //         color:
//                                                                 //             textblackColor,
//                                                                 //         fontFamily:
//                                                                 //             font_medium),
//                                                                 //   ),
//                                                                 // ),
//                                                               ],
//                                                             ),
//                                                           ),
//                                                           Container(
//                                                             height:
//                                                                 Get.width / 10,
//                                                             width:
//                                                                 Get.width / 10,
//                                                             margin:
//                                                                 const EdgeInsets
//                                                                     .only(
//                                                                     left: 13),
//                                                             child: ClipRRect(
//                                                               borderRadius:
//                                                                   BorderRadius
//                                                                       .circular(
//                                                                           1000),
//                                                               child:
//                                                                   Image.network(
//                                                                 GetStorage()
//                                                                     .read(
//                                                                         'user_image')
//                                                                     .toString(),
//                                                                 errorBuilder:
//                                                                     (a, b, c) {
//                                                                   return ClipRRect(
//                                                                     borderRadius:
//                                                                         BorderRadius.circular(
//                                                                             1000),
//                                                                     child: Image
//                                                                         .asset(
//                                                                             'assets/img/user_placeholder.png'),
//                                                                   );
//                                                                 },
//                                                                 width:
//                                                                     Get.width /
//                                                                         10,
//                                                                 fit: BoxFit
//                                                                     .cover,
//                                                               ),
//                                                             ),
//                                                           ),
//                                                         ],
//                                                       ),
//                                                     ));
//                                           },
//                                         ),
//
//                                         if (controller.chat_reponse_waiting)
//                                           Container(
//                                             margin:
//                                                 const EdgeInsets.only(top: 30),
//                                             alignment: Alignment.centerLeft,
//                                             child: Row(
//                                               children: [
//                                                 Container(
//                                                   margin: const EdgeInsets.only(
//                                                       right: 13),
//                                                   child: Image.asset(
//                                                     'assets/logo/logo_plain.png',
//                                                     width: Get.width / 10,
//                                                   ),
//                                                 ),
//                                                 Container(
//                                                   decoration: BoxDecoration(
//                                                       color: textwhiteColor,
//                                                       borderRadius:
//                                                           const BorderRadius
//                                                               .only(
//                                                         bottomLeft:
//                                                             Radius.circular(50),
//                                                         topRight:
//                                                             Radius.circular(50),
//                                                         bottomRight:
//                                                             Radius.circular(50),
//                                                       )),
//                                                   padding: const EdgeInsets
//                                                       .symmetric(
//                                                       horizontal: 30,
//                                                       vertical: 10),
//                                                   child: Lottie.asset(
//                                                       'assets/lottie/chat_loading.json',
//                                                       width: Get.width / 10),
//                                                 ),
//                                               ],
//                                             ),
//                                           ),
//                                         SizedBox(
//                                           height: controller.show_mic_sec
//                                               ? Get.height / 4.5
//                                               : Get.height / 9,
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                               Positioned(
//                                 bottom: 0,
//                                 left: 0,
//                                 right: 0,
//                                 child: Container(
//                                   // color: Colors.yellow,
//                                   // height: 100,
//                                   child: controller.show_mic_sec
//                                       ? Stack(
//                                           children: [
//                                             // if (!controller.showPlayer)
//                                             Column(
//                                               children: [
//                                                 Container(
//                                                   height: Get.height / 12,
//                                                   color: Colors.transparent,
//                                                 ),
//                                                 Container(
//                                                   width: Get.width,
//                                                   decoration:
//                                                       const BoxDecoration(
//                                                     color: Colors.transparent,
//                                                     image: DecorationImage(
//                                                         image: AssetImage(
//                                                             'assets/bg/chat_bottombar.png'),
//                                                         fit: BoxFit.fill),
//                                                   ),
//                                                   padding: EdgeInsets.symmetric(
//                                                       horizontal:
//                                                           Get.width / 10,
//                                                       vertical:
//                                                           Get.height / 50),
//                                                   child: ClipRRect(
//                                                       borderRadius:
//                                                           const BorderRadius
//                                                               .all(
//                                                               Radius.circular(
//                                                                   0)),
//                                                       child: Container(
//                                                         child: Row(
//                                                           mainAxisAlignment:
//                                                               MainAxisAlignment
//                                                                   .spaceBetween,
//                                                           children: [
//                                                             GestureDetector(
//                                                               onTap: () {
//                                                                 // _playAudio();
//                                                                 setState(() {
//                                                                   controller
//                                                                           .show_mic_sec =
//                                                                       false;
//                                                                 });
//                                                                 controller
//                                                                     .message_fld_nd
//                                                                     .requestFocus();
//                                                               },
//                                                               child: Container(
//                                                                 child: Column(
//                                                                   children: [
//                                                                     Image.asset(
//                                                                       'assets/icons/type_new.png',
//                                                                       width:
//                                                                           Get.width /
//                                                                               10,
//                                                                     ),
//                                                                     const SizedBox(
//                                                                       height: 3,
//                                                                     ),
//                                                                     Text(
//                                                                       "Type Here",
//                                                                       style: TextStyle(
//                                                                           fontSize:
//                                                                               Get.width / 38),
//                                                                     )
//                                                                   ],
//                                                                 ),
//                                                               ),
//                                                             ),
//                                                             GestureDetector(
//                                                               onTap: () {
//                                                                 Get.to(
//                                                                     session_overview(
//                                                                   chat_id: controller
//                                                                       .spchatdata[
//                                                                           0][
//                                                                           'chat_id']
//                                                                       .toString(),
//                                                                 ));
//                                                               },
//                                                               child: Container(
//                                                                 child: controller
//                                                                         .isspchatloading
//                                                                     ? Container(
//                                                                         padding: const EdgeInsets
//                                                                             .all(
//                                                                             3),
//                                                                         child:
//                                                                             CircularProgressIndicator(
//                                                                           color:
//                                                                               secondaryColor,
//                                                                         ),
//                                                                       )
//                                                                     : Column(
//                                                                         children: [
//                                                                           Image
//                                                                               .asset(
//                                                                             'assets/icons/close_chat.png',
//                                                                             width:
//                                                                                 Get.width / 11,
//                                                                           ),
//                                                                           const SizedBox(
//                                                                             height:
//                                                                                 3,
//                                                                           ),
//                                                                           Text(
//                                                                             "End Session",
//                                                                             style:
//                                                                                 TextStyle(fontSize: Get.width / 38),
//                                                                           )
//                                                                         ],
//                                                                       ),
//                                                                 // : Container(child: Icon(Icons.check_circle_rounded, size: Get.width / 7,color: Colors.green.withOpacity(0.8),)),
//                                                                 //    : Container(
//                                                                 //       // width: Get.width / 7,
//                                                                 //       height: Get.width / 10,
//                                                                 //       padding: EdgeInsets.symmetric(horizontal: 10),
//                                                                 //       decoration: BoxDecoration(
//                                                                 //         color: Colors.red.withOpacity(0.85),
//                                                                 //         borderRadius: BorderRadius.circular(10),
//                                                                 //       ),
//                                                                 //       child: Center(
//                                                                 //         child: Text("End Session",
//                                                                 //           style: TextStyle(
//                                                                 //             color: Colors.white,
//                                                                 //             fontFamily: font_semibold,
//                                                                 //             fontSize: Get.width / 40,
//                                                                 //           ),
//                                                                 //           textAlign: TextAlign.center,
//                                                                 //         ),
//                                                                 //       ),
//                                                                 //     ),
//                                                               ),
//                                                             ),
//                                                           ],
//                                                         ),
//                                                       )
//                                                       // 5 tabs -- tab, // 4 tabs -- tab2
//                                                       ),
//                                                 ),
//                                               ],
//                                             ),
//                                             // Align(
//                                             //   alignment: const Alignment(0, 0),
//                                             //   child: GestureDetector(
//                                             //     onTap: () {
//                                             //       _playAudio();
//                                             //       // await _playSound();
//                                             //     },
//                                             //     child: controller
//                                             //             .ischataudiosent
//                                             //         ? Lottie.asset(
//                                             //             'assets/lottie/audio_loading.json',
//                                             //             width: Get.width / 2)
//                                             //         : controller.showPlayer
//                                             //             ? Recorder(
//                                             //                 sel_country: widget
//                                             //                     .sel_country
//                                             //                     .toString(),
//                                             //                 chat_id: controller
//                                             //                     .spchatdata[0]
//                                             //                         ['chat_id']
//                                             //                     .toString(),
//                                             //                 onStop: (path) {
//                                             //                   _playAudio();
//                                             //                   // if (kDebugMode)
//                                             //                   print(
//                                             //                       'Recorded file path: $path');
//                                             //                   setState(() {
//                                             //                     controller
//                                             //                             .audioPath =
//                                             //                         path;
//                                             //                     controller
//                                             //                             .showPlayer =
//                                             //                         true;
//                                             //                   });
//                                             //                 },
//                                             //               )
//                                             //             : Recorder(
//                                             //                 sel_country: widget
//                                             //                     .sel_country
//                                             //                     .toString(),
//                                             //                 chat_id: controller
//                                             //                     .spchatdata[0]
//                                             //                         ['chat_id']
//                                             //                     .toString(),
//                                             //                 onStop: (path) {
//                                             //                   _playAudio();
//                                             //                   // if (kDebugMode)
//                                             //                   print(
//                                             //                       'Recorded file path: $path');
//                                             //                   setState(() {
//                                             //                     controller
//                                             //                             .audioPath =
//                                             //                         path;
//                                             //                     controller
//                                             //                             .showPlayer =
//                                             //                         true;
//                                             //                   });
//                                             //                 },
//                                             //               ),
//                                             //   ),
//                                             // ),
//                                           ],
//                                         )
//                                       : Container(
//                                           color: textwhiteColor,
//                                           padding: const EdgeInsets.only(
//                                               bottom: 20,
//                                               left: 10,
//                                               right: 10,
//                                               top: 7),
//                                           width: Get.width,
//                                           // height: 100,
//                                           child: Row(
//                                             children: [
//                                               Expanded(
//                                                 child: TextField(
//                                                   onTap: () {
//                                                     print('hlooop---');
//                                                     setState(() {
//                                                       controller
//                                                           .startTimer(200);
//                                                     });
//                                                   },
//                                                   onChanged: (v) {
//                                                     setState(() {
//                                                       if (controller
//                                                           .message_fld_ctr
//                                                           .text
//                                                           .isEmpty) {
//                                                         controller
//                                                                 .is_send_enabled =
//                                                             false;
//                                                       } else {
//                                                         controller
//                                                                 .is_send_enabled =
//                                                             true;
//                                                       }
//                                                     });
//                                                   },
//                                                   focusNode:
//                                                       controller.message_fld_nd,
//                                                   style: TextStyle(
//                                                       fontSize: Get.width / 22,
//                                                       height: 1.5,
//                                                       color: Colors.black),
//                                                   controller: controller
//                                                       .message_fld_ctr,
//                                                   decoration: InputDecoration(
//                                                     focusedBorder:
//                                                         OutlineInputBorder(
//                                                       borderSide:
//                                                           const BorderSide(
//                                                               color: Colors
//                                                                   .transparent,
//                                                               width: 1),
//                                                       borderRadius:
//                                                           BorderRadius.circular(
//                                                               10.0),
//                                                     ),
//                                                     enabledBorder:
//                                                         OutlineInputBorder(
//                                                       borderSide:
//                                                           const BorderSide(
//                                                               color: Colors
//                                                                   .transparent,
//                                                               width: 1),
//                                                       borderRadius:
//                                                           BorderRadius.circular(
//                                                               10.0),
//                                                     ),
//                                                     filled: true,
//                                                     hintStyle: TextStyle(
//                                                         fontFamily:
//                                                             font_regular,
//                                                         color: Colors.black38,
//                                                         fontSize:
//                                                             Get.width / 27),
//                                                     hintText:
//                                                         "Type your message here",
//                                                     fillColor: Colors.white,
//                                                     contentPadding:
//                                                         const EdgeInsets
//                                                             .symmetric(
//                                                             vertical: 15.0,
//                                                             horizontal: 20),
//                                                   ),
//                                                 ),
//                                               ),
//                                               controller.is_send_enabled
//                                                   ? GestureDetector(
//                                                       onTap: () {
//                                                         setState(() {
//                                                           controller
//                                                                   .chat_reponse_waiting =
//                                                               true;
//
//                                                           // call_send_chat(chat_id,language_code,type,audio,word);
//                                                           // controller.show_mic_sec = true;
//                                                           controller.call_send_chat(
//                                                               controller
//                                                                   .spchatdata[0]
//                                                                       [
//                                                                       'chat_id']
//                                                                   .toString(),
//                                                               widget
//                                                                   .sel_country,
//                                                               'word',
//                                                               '',
//                                                               controller
//                                                                   .message_fld_ctr
//                                                                   .text
//                                                                   .toString());
//                                                           controller
//                                                               .startTimer(100);
//
//                                                           controller
//                                                               .message_fld_ctr
//                                                               .text = '';
//                                                           controller
//                                                                   .is_send_enabled =
//                                                               false;
//                                                           // controller.message_fld_nd.unfocus();
//                                                         });
//                                                       },
//                                                       child: Container(
//                                                         decoration:
//                                                             BoxDecoration(
//                                                           color: primaryColor
//                                                               .withOpacity(0.1),
//                                                           borderRadius:
//                                                               BorderRadius
//                                                                   .circular(
//                                                                       1000),
//                                                         ),
//                                                         padding:
//                                                             const EdgeInsets
//                                                                 .all(7),
//                                                         child: Image.asset(
//                                                           'assets/icons/send_2.png',
//                                                           color: primaryColor,
//                                                           width: Get.width / 18,
//                                                         ),
//                                                       ),
//                                                     )
//                                                   : GestureDetector(
//                                                       onTap: () {
//                                                         setState(() {
//                                                           controller
//                                                                   .show_mic_sec =
//                                                               true;
//                                                         });
//                                                       },
//                                                       child: Container(
//                                                         decoration:
//                                                             BoxDecoration(
//                                                           color: primaryColor
//                                                               .withOpacity(0.1),
//                                                           borderRadius:
//                                                               BorderRadius
//                                                                   .circular(
//                                                                       1000),
//                                                         ),
//                                                         padding:
//                                                             const EdgeInsets
//                                                                 .all(7),
//                                                         child: Icon(
//                                                           Icons.mic,
//                                                           color: primaryColor,
//                                                         ),
//                                                       ),
//                                                     ),
//                                             ],
//                                           ),
//                                         ),
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//         ),
//       );
//     });
//   }
// }
