// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/components/utils.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:prepscale/view/chat/controller/groupchatcontroller.dart';
//
// class CommentPage extends StatefulWidget {
//   CommentPage({
//     super.key,
//     required this.chatId,
//     required this.from,
//     required this.chatType,
//     required this.chatTypeId,
//   });
//   String chatId, from;
//   String chatType, chatTypeId;
//
//   @override
//   State<CommentPage> createState() => _CommentPageState();
// }
//
// class _CommentPageState extends State<CommentPage> {
//   GroupchatController controller = Get.put(GroupchatController());
//   final FocusNode _focusNode = FocusNode();
//
//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       controller.messageComments(widget.chatId);
//     });
//   }
//
//   @override
//   void dispose() {
//     controller.imageFile = null;
//     // TODO: implement dispose
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<GroupchatController>(
//       builder: (controller) {
//         var chatList = controller.commentData["chat_list"];
//         return Scaffold(
//           backgroundColor: chatbgColor,
//           body: (widget.from == "chat" && controller.messageCommentLoading)
//               ? loader()
//               : Column(
//                   // mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Expanded(
//                       child: SingleChildScrollView(
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//
//                             const SizedBox(height: 30,),
//
//                             Container(
//                               color: Colors.white,
//                               child: ListTile(
//                                 leading: GestureDetector(
//                                     onTap: () {
//                                       Navigator.of(context).pop();
//                                     },
//                                     child: const Icon(Icons.arrow_back_ios)),
//                                 title: Row(
//                                   children: [
//                                     CircleAvatar(
//                                       backgroundImage: NetworkImage(
//                                           "${chatList[0]['sender_image']}"),
//                                     ),
//                                     const SizedBox(
//                                       width: 20,
//                                     ),
//                                     Column(
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       children: [
//                                         Row(
//                                           children: [
//                                             Text(
//                                               "${chatList[0]['sender_name']}",
//                                               style:
//                                                   const TextStyle(fontSize: 13),
//                                             ),
//                                             const SizedBox(
//                                               width: 5,
//                                             ),
//                                             if (chatList[0]['is_highlight']
//                                                     .toString() ==
//                                                 "1")
//                                               Container(
//                                                   margin: const EdgeInsets.only(
//                                                       top: 4),
//                                                   child: Icon(
//                                                     Icons.verified,
//                                                     color: Colors.blue,
//                                                     size: Get.width / 28,
//                                                   )),
//                                           ],
//                                         ),
//                                         Text(
//                                           "${chatList[0]['message_date']}",
//                                           style: const TextStyle(
//                                               fontSize: 10, color: Colors.grey),
//                                         ),
//                                       ],
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             ),
//                             Column(
//                               children: [
//                                 if (chatList[0]['message_type'] == "1")
//                                   Align(
//                                     alignment: Alignment.topLeft,
//                                     child: Card(
//                                         color: textwhiteColor,
//                                         margin: const EdgeInsets.all(10),
//                                         child: Padding(
//                                           padding: const EdgeInsets.all(8.0),
//                                           child:
//                                               Text('${chatList[0]['message']}'),
//                                         )),
//                                   ),
//                                 if (chatList[0]['message_type'] == "2")
//                                   Align(
//                                     alignment: Alignment.topLeft,
//                                     child: Card(
//                                       color: textwhiteColor,
//                                       child: Padding(
//                                         padding: const EdgeInsets.all(8.0),
//                                         child: Image.network(
//                                           height: Get.width - 125,
//                                           width: Get.width - 150,
//                                           chatList[0]['message_file'],
//                                           fit: BoxFit.contain,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 // if (chatList[0]['message_type'] == "4")
//                                 //   Expanded(
//                                 //     child: SimpleAudioPlayer(
//                                 //         senderName: chatList[0]['sender_name']
//                                 //             .toString(),
//                                 //         audioUrl: chatList[0]['message_file']),
//                                 //   ),
//                                 ListView.builder(
//                                   shrinkWrap: true,
//                                   physics: const NeverScrollableScrollPhysics(),
//                                   itemCount:
//                                       chatList[0]['reply_messages'].length,
//                                   itemBuilder: (context, index) {
//                                     var data =
//                                         chatList[0]['reply_messages'][index];
//                                     return Padding(
//                                       padding: const EdgeInsets.all(8.0),
//                                       child: Row(
//                                         mainAxisAlignment:
//                                             data['sender_name'].toString() !=
//                                                     "You"
//                                                 ? MainAxisAlignment.start
//                                                 : MainAxisAlignment.end,
//                                         children: [
//                                           const SizedBox(width: 10),
//                                           Container(
//                                             margin: const EdgeInsets.only(
//                                                 right: 13),
//                                             child: ClipRRect(
//                                               borderRadius:
//                                                   BorderRadius.circular(1000),
//                                               child: Image.network(
//                                                 data['sender_image'],
//                                                 width: Get.width / 10,
//                                                 height: Get.width / 10,
//                                                 fit: BoxFit.cover,
//                                                 errorBuilder: (a, b, c) {
//                                                   return ClipRRect(
//                                                     borderRadius:
//                                                         BorderRadius.circular(
//                                                             1000),
//                                                     child: Image.asset(
//                                                       "assets/img/user_placeholder.png",
//                                                       width: Get.width / 10,
//                                                     ),
//                                                   );
//                                                 },
//                                               ),
//                                             ),
//                                           ),
//                                           Container(
//                                             decoration: BoxDecoration(
//                                               color: data['sender_name']
//                                                           .toString() !=
//                                                       "You"
//                                                   ? Colors.white
//                                                   : primaryColor,
//                                               borderRadius:
//                                                   const BorderRadius.only(
//                                                 topRight: Radius.circular(20),
//                                                 bottomRight: Radius.circular(0),
//                                                 topLeft: Radius.circular(0),
//                                                 bottomLeft: Radius.circular(20),
//                                               ),
//                                             ),
//                                             constraints: BoxConstraints(
//                                               maxWidth: Get.width / 1.35,
//                                             ),
//                                             padding: const EdgeInsets.only(
//                                               left: 15,
//                                               right: 15,
//                                               top: 10,
//                                               bottom: 10,
//                                             ),
//                                             child: IntrinsicWidth(
//                                               child: Column(
//                                                 crossAxisAlignment:
//                                                     CrossAxisAlignment.start,
//                                                 children: [
//                                                   Row(
//                                                     children: [
//                                                       Text(
//                                                         '${data['sender_name']}'
//                                                             .toString(),
//                                                         style: TextStyle(
//                                                           color: data['sender_name']
//                                                                       .toString() ==
//                                                                   "You"
//                                                               ? Colors.white
//                                                               : Colors.black,
//                                                           fontFamily:
//                                                               font_medium,
//                                                           fontSize:
//                                                               Get.width / 40,
//                                                         ),
//                                                       ),
//                                                     ],
//                                                   ),
//                                                   const SizedBox(height: 3),
//                                                   Text(
//                                                     data['message'],
//                                                     style: TextStyle(
//                                                       color: data['sender_name']
//                                                                   .toString() ==
//                                                               "You"
//                                                           ? Colors.white
//                                                           : Colors.black,
//                                                       fontFamily: font_regular,
//                                                       fontSize: Get.width / 30,
//                                                     ),
//                                                     softWrap: true,
//                                                   ),
//                                                   const SizedBox(height: 3),
//                                                   Text(
//                                                     data['message_time'],
//                                                     style: TextStyle(
//                                                       color: data['sender_name']
//                                                                   .toString() ==
//                                                               "You"
//                                                           ? Colors.white
//                                                               .withOpacity(0.5)
//                                                           : Colors.black
//                                                               .withOpacity(0.5),
//                                                       fontFamily: font_regular,
//                                                       fontSize: Get.width / 45,
//                                                     ),
//                                                   ),
//                                                   if (data['message_type'] ==
//                                                       "2")
//                                                     Image.network(
//                                                       height: Get.width - 125,
//                                                       width: Get.width - 150,
//                                                       data['message_file'],
//                                                       fit: BoxFit.cover,
//                                                     )
//                                                 ],
//                                               ),
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                                     );
//                                   },
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                     controller.commonLoader || controller.messageCommentLoading
//                         ? loader()
//                         : const SizedBox(),
//                     if (controller.showAttachmentSection)
//                       Stack(
//                         children: [
//                           Container(
//                             decoration: BoxDecoration(
//                                 color: Colors.transparent,
//                                 borderRadius:
//                                     const BorderRadius.all(Radius.circular(10)),
//                                 boxShadow: [
//                                   BoxShadow(
//                                       color: Colors.black.withOpacity(0.1),
//                                       blurRadius: 1)
//                                 ]),
//                             margin: const EdgeInsets.symmetric(horizontal: 20),
//                             padding: const EdgeInsets.only(
//                                 left: 20, right: 20, top: 20, bottom: 10),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 GestureDetector(
//                                   onTap: () {
//                                     controller.getFromGallery();
//                                   },
//                                   child: Column(
//                                     children: [
//                                       Container(
//                                         padding: const EdgeInsets.all(15),
//                                         decoration: BoxDecoration(
//                                           color: primaryColor.withOpacity(0.5),
//                                           borderRadius:
//                                               BorderRadius.circular(15),
//                                         ),
//                                         child: const Icon(
//                                           Icons.image_rounded,
//                                           size: 25,
//                                           color: Colors.white,
//                                         ),
//                                       ),
//                                       const SizedBox(
//                                         height: 5,
//                                       ),
//                                       Text(
//                                         'Image',
//                                         style:
//                                             TextStyle(fontSize: Get.width / 38),
//                                       )
//                                     ],
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   width: 20,
//                                 ),
//                                 GestureDetector(
//                                   onTap: () {
//                                     controller.getFromCamera();
//                                   },
//                                   child: Column(
//                                     children: [
//                                       Container(
//                                         padding: const EdgeInsets.all(15),
//                                         decoration: BoxDecoration(
//                                           color: primaryColor.withOpacity(0.5),
//                                           borderRadius:
//                                               BorderRadius.circular(15),
//                                         ),
//                                         child: const Icon(
//                                           CupertinoIcons.camera_circle_fill,
//                                           size: 25,
//                                           color: Colors.white,
//                                         ),
//                                       ),
//                                       const SizedBox(
//                                         height: 5,
//                                       ),
//                                       Text(
//                                         'Camera',
//                                         style:
//                                             TextStyle(fontSize: Get.width / 38),
//                                       )
//                                     ],
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           Positioned(
//                               top: 2,
//                               right: 22,
//                               child: GestureDetector(
//                                 onTap: () {
//                                   setState(() {
//                                     controller.showAttachmentSection = false;
//                                   });
//                                 },
//                                 child: Container(
//                                   padding: const EdgeInsets.all(5),
//                                   child: const Icon(Icons.close_rounded),
//                                 ),
//                               ))
//                         ],
//                       ),
//                     if (controller.imageFile != null)
//                       Container(
//                         decoration: const BoxDecoration(
//                           color: Colors.white,
//                           borderRadius: BorderRadius.all(Radius.circular(15)),
//                         ),
//                         padding: const EdgeInsets.all(10),
//                         margin: const EdgeInsets.all(10),
//                         child: Stack(
//                           children: [
//                             ClipRRect(
//                               borderRadius: BorderRadius.circular(10),
//                               child: Image.file(controller.imageFile!,
//                                   height: Get.width, width: Get.width),
//                             ),
//                             Positioned(
//                               bottom: 55,
//                               right: 10,
//                               child: GestureDetector(
//                                 onTap: () {
//                                   setState(() {
//                                     controller.imageFile = null;
//                                   });
//                                 },
//                                 child: Container(
//                                   decoration: BoxDecoration(
//                                       shape: BoxShape.circle,
//                                       color: Colors.white,
//                                       boxShadow: [
//                                         BoxShadow(
//                                             color:
//                                                 Colors.black.withOpacity(0.3),
//                                             blurRadius: 5)
//                                       ]),
//                                   padding: const EdgeInsets.all(6),
//                                   child: Icon(Icons.close_rounded,
//                                       color: textblackColor),
//                                 ),
//                               ),
//                             ),
//                             Positioned(
//                               bottom: 10,
//                               right: 10,
//                               child: GestureDetector(
//                                 onTap: () async {
//                                   widget.from = "";
//                                   print('Image url: ${controller.imageFile}');
//                                   //send message
//                                   controller.sendMessage(
//                                       chatType: controller.messageList[0]
//                                           ['chat_type'],
//                                       chatTypeId: controller.messageList[0]
//                                           ['chat_type_id'],
//                                       messageType: "2",
//                                       message: "",
//                                       isTaggedMessage: "1",
//                                       taggedMsgId: widget.chatId,
//                                       filePath: controller.imageFile);
//                                   // await controller
//                                   //     .messageListData(widget.chatId);
//                                   // await controller
//                                   //     .messageListData(widget.chatId);
//
//                                   setState(() {
//                                     controller.imageFile = null;
//                                   });
//                                 },
//                                 child: Container(
//                                   decoration: BoxDecoration(
//                                       shape: BoxShape.circle,
//                                       color: Colors.white,
//                                       boxShadow: [
//                                         BoxShadow(
//                                             color:
//                                                 Colors.black.withOpacity(0.3),
//                                             blurRadius: 5)
//                                       ]),
//                                   padding: const EdgeInsets.all(6),
//                                   child: Icon(Icons.check_rounded,
//                                       color: textblackColor),
//                                 ),
//                               ),
//                             )
//                           ],
//                         ),
//                       ),
//                     Container(
//                       margin: const EdgeInsets.all(5),
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(40),
//                         color: textwhiteColor,
//                       ),
//                       child: Padding(
//                         padding: const EdgeInsets.all(8.0),
//                         child: Row(
//                           children: [
//                             Expanded(
//                               child: TextField(
//                                 focusNode: _focusNode,
//                                 onTap: () {
//                                   setState(() {
//                                     controller.showAttachmentSection = false;
//                                   });
//                                 },
//                                 onChanged: (text) {
//                                   setState(() {
//                                     controller.isText = text.isNotEmpty;
//                                   });
//                                 },
//                                 maxLines: null,
//                                 keyboardType: TextInputType.text,
//                                 controller: controller.commentController,
//                                 style: TextStyle(
//                                   fontSize: Get.width / 28,
//                                   color: Colors.black,
//                                 ),
//                                 decoration: InputDecoration(
//                                   focusedBorder: OutlineInputBorder(
//                                     borderSide: const BorderSide(
//                                         color: Colors.transparent, width: 1),
//                                     borderRadius: BorderRadius.circular(10.0),
//                                   ),
//                                   enabledBorder: OutlineInputBorder(
//                                     borderSide: const BorderSide(
//                                         color: Colors.transparent, width: 1),
//                                     borderRadius: BorderRadius.circular(10.0),
//                                   ),
//                                   filled: true,
//                                   hintStyle: TextStyle(
//                                     fontFamily: font_regular,
//                                     color: Colors.black38,
//                                     fontSize: Get.width / 28,
//                                   ),
//                                   hintText: "Type your message here",
//                                   fillColor: Colors.transparent,
//                                   contentPadding: const EdgeInsets.symmetric(
//                                     vertical: 15.0,
//                                     horizontal: 20,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             GestureDetector(
//                               onTap: () {
//                                 setState(() {
//                                   controller.showAttachmentSection =
//                                       controller.showAttachmentSection
//                                           ? false
//                                           : true;
//                                 });
//                               },
//                               child: SizedBox(
//                                 height: 50,
//                                 width: 40,
//                                 child: Padding(
//                                   padding: const EdgeInsets.symmetric(
//                                       horizontal: 5.0),
//                                   child: GestureDetector(
//                                     child: const Icon(
//                                       Icons.attachment_rounded,
//                                       size: 25,
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             IconButton(
//                                 onPressed: () {
//                                   widget.from = "";
//                                   controller.sendMessage(
//                                       chatType: widget.chatType,
//                                       chatTypeId: widget.chatTypeId,
//                                       messageType: "1",
//                                       message: controller.commentController.text
//                                           .toString(),
//                                       isTaggedMessage: "1",
//                                       taggedMsgId: widget.chatId.toString());
//                                   controller.messageComments(widget.chatId);
//                                   controller.commentController.clear();
//                                 },
//                                 icon: const Icon(Icons.send)),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//         );
//       },
//     );
//   }
// }
