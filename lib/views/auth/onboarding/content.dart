
import 'package:edutalim/components/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class content extends StatelessWidget {
  const content({
    Key? key,
    this.title,
    this.text,
    this.image,
    this.color,
  }) : super(key: key);
  final String? title,text, image,color;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[


        Container(
          width: Get.width/1.2,
          // color: Colors.yellow,
          child: Text(
            title.toString(),
            style: TextStyle(
              fontSize: Get.width/20,
              color: textwhiteColor,
              fontFamily: font_bold,
              fontWeight: FontWeight.w800,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        SizedBox(height: 15,),
        Container(
         width: Get.width/1.2,
         // color: Colors.yellow,
         child:  Text(
           text!,
           textAlign: TextAlign.center,
           style: TextStyle(
             color: textwhiteColor,
           ),
         ),
       ),

      ],
    );
  }
}
