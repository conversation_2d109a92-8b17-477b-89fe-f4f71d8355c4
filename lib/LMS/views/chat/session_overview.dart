// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/components/utils.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:prepscale/view/chat/controller/speekichatcontroller.dart';
// import 'package:step_progress_indicator/step_progress_indicator.dart';
//
//
// class session_overview extends StatefulWidget {
//   String chat_id;
//   session_overview({required this.chat_id});
//
//   @override
//   State<session_overview> createState() => _session_overviewState();
// }
//
// class _session_overviewState extends State<session_overview> {
//
//   Speekichatcontroller controller = Get.put(Speekichatcontroller());
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     controller.call_chat_summary(widget.chat_id);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<Speekichatcontroller>(builder: (controller){
//       return Scaffold(
//         backgroundColor: textwhiteColor,
//         body: Container(
//           height: Get.height,
//           width: Get.width,
//           child: controller.issummaruloading ?
//           loader()
//           :
//           Column(
//             children: [
//
//               Container(
//                 width: Get.width,
//                 decoration: BoxDecoration(
//                     color: textwhiteColor,
//                     gradient: LinearGradient(
//                         colors: [Color(0xff2767C1).withOpacity(0.3),textwhiteColor],
//                         begin: Alignment.topCenter,
//                         end: Alignment.bottomCenter
//                     )
//                 ),
//                 child: Column(
//                   children: [
//                     AppBar(
//                       // toolbarHeight: 0,
//                       backgroundColor: appbarwhiteColor,
//                       surfaceTintColor: Colors.transparent,
//                       elevation: 0,
//                     ),
//                     SizedBox(height: Get.height/30,),
//                     Image.asset('assets/img/trophy_1.png',width: Get.width/3.8,),
//                     SizedBox(height: 15,),
//                     Container(
//                       color: Colors.transparent,
//                       child: Text('Your Session Is\nCompleted. Well done!!',style: TextStyle(color: textblackColor,fontFamily: font_semibold,fontSize: Get.width/22),textAlign: TextAlign.center,),
//                     ),
//                     SizedBox(height: 10,),
//                     Container(
//                       color: Colors.transparent,
//                       child: Text(controller.chatsummary[0]['data']['total_score'].toString()+'%',style: TextStyle(color: primaryColor,fontFamily: font_medium,fontSize: Get.width/11),textAlign: TextAlign.center,),
//                     ),
//                     SizedBox(height: Get.height/25,),
//                   ],
//                 ),
//               ),
//
//               SizedBox(height: 10,),
//               Expanded(
//                 child: Container(
//                     decoration: BoxDecoration(
//                         color: textwhiteColor,
//                         // boxShadow: [BoxShadow(
//                         //   color: textblackColor.withOpacity(0.1),
//                         //   blurRadius: 35,
//                         //   spreadRadius: 3
//                         // ),],
//                         border: Border(top: BorderSide(color: textblackColor.withOpacity(0.1))),
//                         borderRadius: BorderRadius.vertical(top: Radius.circular(40))
//                     ),
//                     padding: EdgeInsets.only(top: 30),
//                     child: SingleChildScrollView(
//                       child: Column(
//                         children: [
//                           Container(
//                             color: Colors.transparent,
//                             alignment: Alignment.centerLeft,
//                             padding: EdgeInsets.symmetric(horizontal: 20),
//                             child: Text('Assessment Summary',style: TextStyle(color: textblackColor,fontFamily: font_semibold,fontSize: Get.width/22),),
//                           ),
//                           SizedBox(height: 8,),
//                           Container(
//                             alignment: Alignment.centerLeft,
//                             padding: EdgeInsets.symmetric(horizontal: 20),
//                             child: Text('Your language skills are impressive, but,there is room for improvement.',style: TextStyle(color: Colors.black54,fontFamily: font_regular,fontSize: Get.width/29),),
//                           ),
//
//                           SizedBox(height: 30,),
//
//                           Container(
//                             margin: EdgeInsets.symmetric(horizontal: 20),
//                             decoration: BoxDecoration(
//                                 color: Color(0xffFFFCF0),
//                                 borderRadius: BorderRadius.circular(20),
//                                 border: Border.all(color: textblackColor.withOpacity(0.09))
//                             ),
//                             padding: EdgeInsets.all(14),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 Row(
//                                   children: [
//                                     Image.asset('assets/img/ses_pronounce.png',width: Get.width/11,),
//                                     SizedBox(width: 15,),
//                                     Container(
//                                       child: Text('Pronunciation',style: TextStyle(color: textblackColor,fontFamily: font_semibold,fontSize: Get.width/25),textAlign: TextAlign.center,),
//                                     ),
//                                   ],
//                                 ),
//
//                                 Container(
//                                   width: Get.width/9,
//                                   height: Get.width/9,
//                                   child: CircularStepProgressIndicator(
//                                     totalSteps: 100,
//                                     currentStep: int.parse(controller.chatsummary[0]['data']['pronunciation'].toString()),
//                                     stepSize: 5,
//                                     selectedColor: secondaryColor,
//                                     unselectedColor: Color(0xffE0E0E0),
//                                     padding: 0,
//                                     selectedStepSize: 5,
//                                     roundedCap: (_, __) => true,
//                                     child: Container(
//                                       alignment: Alignment.center,
//                                       child: Text(controller.chatsummary[0]['data']['pronunciation'].toString()+"%",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w600,fontSize: Get.width/45),),
//                                     ),
//                                   ),
//                                 ),
//
//                               ],
//                             ),
//                           ),
//
//                           SizedBox(height: 20,),
//
//                           Container(
//                             margin: EdgeInsets.symmetric(horizontal: 20),
//                             decoration: BoxDecoration(
//                                 color: Color(0xffFFFCF0),
//                                 borderRadius: BorderRadius.circular(20),
//                                 border: Border.all(color: textblackColor.withOpacity(0.09))
//                             ),
//                             padding: EdgeInsets.all(14),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 Row(
//                                   children: [
//                                     Image.asset('assets/img/ses_vocab.png',width: Get.width/11,),
//                                     SizedBox(width: 15,),
//                                     Container(
//                                       child: Text('Vocabulary',style: TextStyle(color: textblackColor,fontFamily: font_semibold,fontSize: Get.width/25),textAlign: TextAlign.center,),
//                                     ),
//                                   ],
//                                 ),
//
//                                 Container(
//                                   width: Get.width/9,
//                                   height: Get.width/9,
//                                   child: CircularStepProgressIndicator(
//                                     totalSteps: 100,
//                                     currentStep: int.parse(controller.chatsummary[0]['data']['vocabulary'].toString()),
//                                     stepSize: 5,
//                                     selectedColor: secondaryColor,
//                                     unselectedColor: Color(0xffE0E0E0),
//                                     padding: 0,
//                                     selectedStepSize: 5,
//                                     roundedCap: (_, __) => true,
//                                     child: Container(
//                                       alignment: Alignment.center,
//                                       child: Text(controller.chatsummary[0]['data']['vocabulary'].toString()+"%",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w600,fontSize: Get.width/45),),
//                                     ),
//                                   ),
//                                 ),
//
//                               ],
//                             ),
//                           ),
//
//
//                           SizedBox(height: 20,),
//
//                           Container(
//                             margin: EdgeInsets.symmetric(horizontal: 20),
//                             decoration: BoxDecoration(
//                                 color: Color(0xffFFFCF0),
//                                 borderRadius: BorderRadius.circular(20),
//                                 border: Border.all(color: textblackColor.withOpacity(0.09))
//                             ),
//                             padding: EdgeInsets.all(14),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 Row(
//                                   children: [
//                                     Image.asset('assets/img/ses_grammer.png',width: Get.width/11,),
//                                     SizedBox(width: 15,),
//                                     Container(
//                                       child: Text('Grammar',style: TextStyle(color: textblackColor,fontFamily: font_semibold,fontSize: Get.width/25),textAlign: TextAlign.center,),
//                                     ),
//                                   ],
//                                 ),
//
//                                 Container(
//                                   width: Get.width/9,
//                                   height: Get.width/9,
//                                   child: CircularStepProgressIndicator(
//                                     totalSteps: 100,
//                                     currentStep: int.parse(controller.chatsummary[0]['data']['grammer'].toString()),
//                                     stepSize: 5,
//                                     selectedColor: secondaryColor,
//                                     unselectedColor: Color(0xffE0E0E0),
//                                     padding: 0,
//                                     selectedStepSize: 5,
//                                     roundedCap: (_, __) => true,
//                                     child: Container(
//                                       alignment: Alignment.center,
//                                       child: Text(controller.chatsummary[0]['data']['grammer'].toString()+"%",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w600,fontSize: Get.width/45),),
//                                     ),
//                                   ),
//                                 ),
//
//                               ],
//                             ),
//                           ),
//
//
//                         ],
//                       ),
//                     )
//                 ),
//               ),
//
//
//               Container(
//                 padding: EdgeInsets.only(bottom: Get.height/40,top: 20),
//                 child: cust_elevatedbutton(
//                   width: Get.width/1.1,
//                   onPressed: () {
//                     // Get.to(chat_transcript(
//                     //   chat_id: widget.chat_id.toString(),
//                     // ));
//                   },
//                   borderRadius: BorderRadius.circular(10),
//                   height: 60,
//                   gradient: LinearGradient(
//                     colors: [Colors.white,Colors.white,],
//                     begin: Alignment.centerLeft,
//                     end: Alignment.centerRight,
//                   ),
//                   child:
//                   // controller.isloding ?
//                   // Container(
//                   //   height: 25,
//                   //   width: 25,
//                   //   child: CircularProgressIndicator(color: textwhiteColor,),
//                   // )
//                   //     :
//                   Text("View Transcript",style: TextStyle(fontFamily: font_medium,color: textwhiteColor),),
//                 ),
//               ),
//
//
//             ],
//           ),
//         ),
//       );
//     });
//   }
// }
