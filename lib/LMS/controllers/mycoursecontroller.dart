import 'dart:developer';
import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:edutalim/LMS/api/live_api.dart';
import 'package:edutalim/LMS/views/Zoom/zoom_meeting_android.dart';
import '../../components/constants.dart';
import '../../components/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../api/main_apis.dart';
import '../views/Zoom/zoom_meeting_ios.dart';
import '../views/dashboard/dashboard.dart';

class Mycoursecontroller extends GetxController {


  bool isLessonsLoading = false;
  List lessons = [];

  get_lessons(lesson_id) async {
    isLessonsLoading = true;
    var data = await ApiBaseHandler.lessonsSection(lesson_id);
    lessons = data['data'];
    isLessonsLoading = false;
    update();
  }

  bool isMyCourseLoading = true;
  List myCourseData = [];

  get_mycourse() async {
    isMyCourseLoading = true;

    var data = await ApiBaseHandler.myCourse();

    myCourseData = [data['data']];

    if (myCourseData[0]['course_details'].toString() != "[]") {
      GetStorage().write('course_id', myCourseData[0]['course_details']['course_id'].toString());
    }

    isMyCourseLoading = false;
    update();
  }

  bool isEnrolledListLoading = true;
  List enrolledCourseList = [];

  enrolled_courses() async {
    var data = await ApiBaseHandler.userCourses();
    if (data['status'].toString() == "true" || data['status'].toString() == "1") {
      enrolledCourseList = data['data'];
    } else {
      toast_info(data['message'].toString());
    }

    isEnrolledListLoading = false;
    update();
  }

  bool updateLoading = false;

  switch_course(course_id) async {
    updateLoading = true;
    update();

    var data = await ApiBaseHandler.switchCourse(course_id);

    log("Log Dataaaaa : " + data.toString());
    if (data['status'].toString() == "true" || data['status'].toString() == "1") {
      toast_success(data['message'].toString());
      GetStorage().write('home_index', '1');
      Get.offAll(dashboard());
    } else {
      toast_info(data['message'].toString());
    }

    updateLoading = false;
    update();
  }

  bool isMaterialLoading = true;
  List materialList = [];

  getMaterial(course_id, section_id, lesson_id) async {
    log("Log Dataaaaa : $course_id");
    log("Log Dataaaaa : $section_id");
    log("Log Dataaaaa : $lesson_id");

    isMaterialLoading = true;
    var data = await ApiBaseHandler.materials(course_id, section_id, lesson_id);

    if (data['status'].toString() == "true" || data['status'].toString() == "1") {
      materialList = data['data'];
      log("Matei Length"+materialList.length.toString());
      log(materialList.toString());
    } else {
      toast_info(data['message'].toString());
    }

    isMaterialLoading = false;
    update();
  }

  bool isExamLoading = true;
  var exams;

  getExam(course_id) async {
    isExamLoading = true;
    var data = await ApiBaseHandler.exams(course_id);
    if (data['status'].toString() == "true" || data['status'].toString() == "1") {
      exams = data['data'];
    } else {
      toast_info(data['message'].toString());
    }
    isExamLoading = false;
    update();
  }

  bool isPracticeLoading = true;
  var practicePage;

  getPractices(course_id) async {
    isPracticeLoading = true;
    var data = await ApiBaseHandler.practices(course_id);

    if (data['status'].toString() == "true" || data['status'].toString() == "1") {
      practicePage = data['data'];
    } else {
      toast_info(data['message'].toString());
    }

    isPracticeLoading = false;
    update();
  }

  updateLessonFileProgress(lesson_file_id) async {
    var data = await ApiBaseHandler.update_lesson_file_progress(lesson_file_id.toString());
  }



  bool isLiveClassesLoading = true;

  var liveClassData = {};
  get_live_classes(course_id) async {
    isLiveClassesLoading = true;
    var data = await LiveApiHandler.live_class(course_id.toString());
    liveClassData = data['data'];
    log(liveClassData['live'].toString());
    isLiveClassesLoading = false;
    update();
  }

  bool isJwtCreating = false;
  create_jwt(var zoom,) async {
    isJwtCreating = true;
    var data = await LiveApiHandler.create_jwt(zoom['meeting_number'].toString());

    if(data['status'].toString() == "1" || data['status'].toString() == "true"){
      if(Platform.isAndroid){
        // Get.to(() => zoom_meeting_android(data: zoom, jwt_token: data['data']['jwt_token'].toString()));
      } else if(Platform.isIOS){
        Get.to(() => zoom_meeting_ios(data: zoom));
      }
    } else {
      toast_info(data['message'].toString());
    }

    // log(liveClassData['live'].toString());
    isJwtCreating = false;
    update();
  }

  List all_liveclass = [];
  bool isliveclassloading = false;

  @override
  void onInit() {
    super.onInit();
  }
}
