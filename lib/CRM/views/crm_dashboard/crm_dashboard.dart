import 'dart:async';
import 'dart:io';
import 'package:edutalim/CRM/controller/dashboardcontroller.dart';
import 'package:edutalim/CRM/views/call_log/call_log.dart';
import 'package:edutalim/CRM/views/crm_dashboard/leads/due_leads_list.dart';
import 'package:edutalim/CRM/views/crm_dashboard/leads/todo/todo.dart';
import 'package:edutalim/CRM/views/crm_dashboard/teams/teams.dart';
import 'package:edutalim/LMS/views/dashboard/common/main_drawer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lottie/lottie.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';

import '../../../common/storage_info_card.dart';
import 'candidates/candidate_list.dart';
import 'common/report.dart';
import 'folders/followups_list.dart';
import 'leads/leads_list.dart';

class crm_dashboard extends StatefulWidget {
  @override
  State<crm_dashboard> createState() => _counselor_dashState();
}

class _counselor_dashState extends State<crm_dashboard> {
  Dashboardcontroller controller = Get.put(Dashboardcontroller());

  @override
  void initState() {
    controller.fethomedata(true);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _refreshData() async {
    // Simulate fetching new data (replace with your actual logic)
    await Future.delayed(Duration(seconds: 2));
    setState(() {
      controller.fethomedata(true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Dashboardcontroller>(builder: (controller) {
      return Scaffold(
        drawer: main_drawer(),
        onDrawerChanged: (va) {},
        body: controller.homeLoading
            ? Center(
                child: Image.asset(
                  'assets/gif/loading_2.gif',
                  width: Get.width / 5,
                ),
              )
            : RefreshIndicator(
                onRefresh: _refreshData,
                color: primaryColor,
                child: Container(
                  width: Get.width,
                  height: Get.height,
                  decoration: BoxDecoration(
                      color: bgColor,
                      image: DecorationImage(
                        fit: BoxFit.cover,
                        opacity: 0.18,
                        image: AssetImage('assets/images/bg3.png'),
                      )),
                  child: Column(
                    children: [
                      AppBar(
                        backgroundColor: primaryColor,
                        iconTheme: IconThemeData(
                          color: Colors.white,
                        ),
                        centerTitle: false,
                        title: Container(
                          padding: EdgeInsets.symmetric(vertical: 10),
                          width: Get.width,
                          child: Column(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 10),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "$appName",
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontFamily: '',
                                          fontSize: Get.width / 25),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        elevation: 0,
                      ),
                      Expanded(
                        child: Container(
                          // color: bgColor,
                          child: Column(
                            children: [
                              // -------------------------------------------------------------
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 20),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.vertical(
                                      bottom: Radius.circular(800)),
                                  color: primaryColor,
                                ),
                                height: Get.height / 6,
                                child: Column(
                                  children: [
                                    Expanded(
                                      child: Stack(
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(10)),
                                                color: Colors.white,
                                                image: DecorationImage(
                                                    image: AssetImage(
                                                        'assets/images/bg_card_2.png'),
                                                    fit: BoxFit.cover,
                                                    opacity: 0.9)),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 15),
                                            child: Column(
                                              children: [
                                                SizedBox(
                                                  height: 8,
                                                ),
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Container(
                                                      width: Get.width / 5,
                                                      height: Get.width / 5,
                                                      decoration: BoxDecoration(
                                                        border: Border.all(
                                                            color: primaryColor,
                                                            width: 2),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(50),
                                                      ),
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(50),
                                                        child: Image.network(
                                                          controller.homedata[
                                                                  'user_data'][
                                                                  'profile_image']
                                                              .toString(),
                                                          fit: BoxFit.cover,
                                                          errorBuilder:
                                                              (a, b, c) {
                                                            return Image.asset(
                                                                'assets/images/avater_placeholder.jpg');
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 20,
                                                    ),
                                                    Container(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          SizedBox(
                                                            height: 15,
                                                          ),
                                                          Container(
                                                            // color: Colors.yellow,
                                                            width:
                                                                Get.width / 1.9,
                                                            child: Text(
                                                              controller
                                                                  .homedata[
                                                                      'user_data']
                                                                      ['name']
                                                                  .toString(),
                                                              style: TextStyle(
                                                                  fontSize: 18,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  color:
                                                                      commontextColor,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis),
                                                            ),
                                                          ),
                                                          Container(
                                                            child: Text(
                                                              controller
                                                                      .homedata[
                                                                          'user_data']
                                                                          [
                                                                          'country_code']
                                                                      .toString() +
                                                                  controller
                                                                      .homedata[
                                                                          'user_data']
                                                                          [
                                                                          'phone']
                                                                      .toString(),
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                  color:
                                                                      commontextColor),
                                                            ),
                                                          ),
                                                          Container(
                                                            child: Text(
                                                              controller
                                                                  .homedata[
                                                                      'user_data']
                                                                      ['email']
                                                                  .toString(),
                                                              style: TextStyle(
                                                                  fontSize: 12,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                  color:
                                                                      commontextColor),
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            height: 15,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                          Positioned(
                                            bottom: 13,
                                            right: 0,
                                            child: Container(
                                              padding: EdgeInsets.symmetric(
                                                  vertical: 2, horizontal: 8),
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.horizontal(
                                                          left: Radius.circular(
                                                              10)),
                                                  color: Colors.white),
                                              // controller.homedata[0]['data']['user_data']['role_text'].toString()
                                              child: Text(
                                                controller.homedata['user_data']
                                                        ['role_title']
                                                    .toString(),
                                                style: TextStyle(
                                                    fontFamily:
                                                        'poppins_regular',
                                                    fontSize: Get.width / 33,
                                                    color: Colors.black,
                                                    fontWeight:
                                                        FontWeight.w600),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // -------------------------------------------------------------
                              Expanded(
                                child: SingleChildScrollView(
                                  physics: ScrollPhysics(),
                                  child: Container(
                                    child: Column(
                                      children: [
                                        SizedBox(
                                          height: 20,
                                        ),

                                        // ------------------ overview --------------------------
                                        if (controller
                                            .homedata['lead_status'].isNotEmpty)
                                          Container(
                                            child: Column(
                                              children: [
                                                Container(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 20),
                                                  child: GridView.builder(
                                                    physics:
                                                        NeverScrollableScrollPhysics(),
                                                    padding: EdgeInsets.all(0),
                                                    shrinkWrap: true,
                                                    itemCount: controller
                                                        .homedata['lead_status']
                                                        .length,
                                                    gridDelegate:
                                                        SliverGridDelegateWithFixedCrossAxisCount(
                                                      // childAspectRatio: 2/1.60,
                                                      childAspectRatio:
                                                          2 / 1.60,
                                                      // childAspectRatio: 1.3,
                                                      crossAxisCount: 3,
                                                      // mainAxisExtent: 90,
                                                      mainAxisSpacing: 10,
                                                      crossAxisSpacing: 10,
                                                    ),
                                                    itemBuilder:
                                                        (context, index) {
                                                      var clr =
                                                          getRandomColor();
                                                      Map data = controller
                                                              .homedata[
                                                          'lead_status'][index];
                                                      return GestureDetector(
                                                        onTap: () {
                                                          Get.to(
                                                              () => leads_list(
                                                                    status_id: data[
                                                                            'id']
                                                                        .toString(),
                                                                    status_title:
                                                                        data['title']
                                                                            .toString(),
                                                                  ))?.then(
                                                              (value) {
                                                            controller
                                                                .fethomedata(
                                                                    false);
                                                          });
                                                        },
                                                        child: Container(
                                                          decoration: BoxDecoration(
                                                              color:
                                                                  Colors.white,
                                                              border: Border.all(
                                                                  color: clr
                                                                      .withValues(
                                                                          alpha:
                                                                              1),
                                                                  width: 0.5),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          10)),
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .center,
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                            children: [
                                                              Container(
                                                                color: Colors
                                                                    .transparent,
                                                                child: Text(
                                                                  // formatnumber(
                                                                  int.parse(data[
                                                                              'count']
                                                                          .toString())
                                                                      .toString(),
                                                                  // ),
                                                                  style: TextStyle(
                                                                      color:
                                                                          clr,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                      fontSize:
                                                                          Get.width /
                                                                              20),
                                                                  textAlign:
                                                                      TextAlign
                                                                          .center,
                                                                ),
                                                              ),
                                                              Container(
                                                                color: Colors
                                                                    .transparent,
                                                                child: Text(
                                                                  data['title']
                                                                      .toString(),
                                                                  maxLines: 3,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                  style: TextStyle(
                                                                      color: Colors
                                                                          .black,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w400,
                                                                      fontSize:
                                                                          Get.width /
                                                                              32),
                                                                  textAlign:
                                                                      TextAlign
                                                                          .center,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        // ------------------ overview --------------------------

                                        SizedBox(height: 10),

                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              SizedBox(height: 10),
                                              GridView(
                                                padding: EdgeInsets.all(0),
                                                physics:
                                                    NeverScrollableScrollPhysics(),
                                                shrinkWrap: true,
                                                gridDelegate:
                                                    SliverGridDelegateWithFixedCrossAxisCount(
                                                        crossAxisCount: 2,
                                                        crossAxisSpacing: 10,
                                                        mainAxisSpacing: 10,
                                                        childAspectRatio:
                                                            1 / 0.9),
                                                children: [
                                                  if (GetStorage()
                                                          .read('user_role')
                                                          .toString() ==
                                                      "1")
                                                    GestureDetector(
                                                      onTap: () {
                                                        Get.to(() => teams());
                                                      },
                                                      child: home_card_image(
                                                        image:
                                                            'assets/icons/teams.png',
                                                        title: "Teams",
                                                      ),
                                                    ),
                                                  GestureDetector(
                                                    onTap: () {
                                                      Get.to(() =>
                                                              due_leads_list())
                                                          ?.then((value) {
                                                        controller
                                                            .fethomedata(false);
                                                      });
                                                    },
                                                    child: home_card_image(
                                                      image:
                                                          'assets/icons/lead_analysy.png',
                                                      // title: "Candidate"
                                                      title: "Due Leads",
                                                    ),
                                                  ),
                                                  if (Platform.isAndroid)
                                                    GestureDetector(
                                                      // onTap: () {
                                                      //   Get.to(() =>
                                                      //       // CallLogPage());
                                                      // },
                                                      child: home_card_image(
                                                        image:
                                                            'assets/icons/call_history.png',
                                                        title: "Call History",
                                                      ),
                                                    ),
                                                  GestureDetector(
                                                    onTap: () {
                                                      Get.to(Todo());
                                                    },
                                                    child: home_card_image(
                                                      image:
                                                          'assets/icons/todo.jpg',
                                                      title: "To Do",
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),

                                        //------------------------android only --------------------

                                        SizedBox(
                                          height: 40,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              // -------------------------------------------------------------
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      );
    });
  }

  int index = 0;
  List<Color> colors = [
    Colors.red,
    Colors.orange,
    Colors.yellow,
    Colors.green,
    Colors.blue,
    Colors.purple
  ];
  Duration duration = const Duration(milliseconds: 250);
  Timer? _timer;
}
