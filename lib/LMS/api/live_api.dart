import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;

import '../../components/constants.dart';
import '../../components/utils.dart';
import '../../views/auth/login.dart';
import '../views/tools/error_404.dart';
import '../views/tools/error_500.dart';

class LiveApiHandler {


  static Future<dynamic> live_class(String courseId) async {
    return _handleGetRequest('live-class/course_live_class/$courseId', allowBadRequest: true);
  }

  static Future<dynamic> create_jwt(String meeting_number) async {
    return _handleGetRequest('live-class/generate_jwt_token?meeting_number=$meeting_number', allowBadRequest: true);
  }

// =============================================================================
// API METHODS
// =============================================================================

  // Cache storage instance for better performance
  static final GetStorage _storage = GetStorage();

  // Common headers for authenticated requests
  static Map<String, String> get _authHeaders => {
        "Content-Type": "application/json",
        "Authorization": "Bearer ${_storage.read('auth_token') ?? ''}",
      };

  // Common headers for non-authenticated requests
  static Map<String, String> get _headers => {
        "Content-Type": "application/json",
      };

  /// Generic method to handle all GET requests
  static Future<dynamic> _handleGetRequest(
    String endpoint, {
    bool requireAuth = true,
    bool allowBadRequest = false,
    String? customErrorMessage,
  }) async {
    log(_storage.read('auth_token').toString());
    try {

      final url = Uri.parse('$api2$endpoint');
      log('GET Request: $url');

      final response = await http.get(
        url,
        headers: requireAuth ? _authHeaders : _headers,
      );

      log(response.body.toString());

      return _handleResponse(
        response,
        allowBadRequest: allowBadRequest,
        customErrorMessage: customErrorMessage,
      );
    } catch (e) {
      log('GET Request Error: $e');
      return 'failed';
    }
  }

  /// Generic method to handle all POST requests
  static Future<dynamic> _handlePostRequest(
    String endpoint,
    Map<String, String> body, {
    bool allowBadRequest = false,
    String? customErrorMessage,
  }) async {
    try {
      final url = Uri.parse('$api2$endpoint');
      log('POST Request: $url');

      final response = await http.post(
        url,
        headers: {"Authorization": "Bearer ${_storage.read('auth_token') ?? ''}"},
        body: body,
      );

      return _handleResponse(
        response,
        allowBadRequest: allowBadRequest,
        customErrorMessage: customErrorMessage,
      );
    } catch (e) {
      log('POST Request Error: $e');
      return 'failed';
    }
  }

  /// Centralized response handling
  static dynamic _handleResponse(
    http.Response response, {
    bool allowBadRequest = false,
    String? customErrorMessage,
  }) {
    log('Response Status: ${response.statusCode}');

    switch (response.statusCode) {
      case 200:
        return jsonDecode(utf8.decode(response.bodyBytes));

      case 400:
        if (allowBadRequest) {
          return jsonDecode(utf8.decode(response.bodyBytes));
        }
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        if (data['message'] != null) {
          toast_error(data['message'].toString());
        }
        return data;

      case 401:
        _handleSessionExpired();
        return 'failed';

      case 404:
        _navigateToErrorPage(() => error_404());
        return 'failed';

      case 500:
        _navigateToErrorPage(() => error_500());
        return 'failed';

      default:
        if (customErrorMessage != null) {
          toast_error(customErrorMessage);
        }
        return 'failed';
    }
  }

  /// Handle session expiration
  static void _handleSessionExpired() {
    toast_info("Session Expired");
    Get.offAll(() => Login());
  }

  /// Navigate to error pages
  static void _navigateToErrorPage(Widget Function() errorPage) {
    Get.offAll(errorPage);
  }

// =============================================================================
// API METHODS
// =============================================================================
}
