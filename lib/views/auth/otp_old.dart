// import 'package:edutalim/LMS/views/dashboard/dashboard.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:otp_autofill/otp_autofill.dart';
// import '../../../components/constants.dart';
// import 'package:edutalim/components/utils.dart';
// import '../../controllers/OtpControllerOld.dart';
//
// class OtpPageOld extends StatefulWidget {
//   String userId, phone, country_code, from;
//
//   OtpPageOld({super.key, required this.userId, required this.phone, required this.country_code, required this.from});
//
//   @override
//   State<OtpPageOld> createState() => _OtpPageOldState();
// }
//
// class _OtpPageOldState extends State<OtpPageOld> {
//   TextEditingController allotp = TextEditingController();
//   TextEditingController otp1 = TextEditingController();
//   TextEditingController otp2 = TextEditingController();
//   TextEditingController otp3 = TextEditingController();
//   TextEditingController otp4 = TextEditingController();
//
//   FocusNode otp1OneFocus = FocusNode();
//   FocusNode otp2OneFocus = FocusNode();
//   FocusNode otp3OneFocus = FocusNode();
//   FocusNode otp4OneFocus = FocusNode();
//
//   String otp = '';
//
//   OtpController controller = Get.put(OtpController());
//
//   // List otpNumbers = [];
//
//   //-----------------------OTP auto sec ------------------------------
//   late OTPTextEditController otptextfldcontroller;
//   late OTPInteractor _otpInteractor;
//
//   //-----------------------OTP auto sec ------------------------------
//
//   Future<void> _initInteractor() async {
//     _otpInteractor = OTPInteractor();
//     // You can receive your app signature by using this method.
//     final appSignature = await _otpInteractor.getAppSignature();
//
//     if (kDebugMode) {
//       print('Your app signature: $appSignature');
//     }
//   }
//
//   getOTP() {
//     otptextfldcontroller = OTPTextEditController(
//       codeLength: 4,
//       onCodeReceive: (code) {
//         print('otp received-------------------------------------');
//         print('Your Application receive code - $code');
//
//         var codedata = [];
//         codedata = code.toString().split('');
//         if (codedata.length > 0) {
//           otp1.text = codedata[0];
//         }
//
//         if (codedata.length > 1) {
//           otp2.text = codedata[1];
//         }
//
//         if (codedata.length > 2) {
//           otp3.text = codedata[2];
//         }
//
//         if (codedata.length > 3) {
//           otp4.text = codedata[3];
//         }
//       },
//       otpInteractor: _otpInteractor,
//     )..startListenUserConsent(
//         (code) {
//           print('otp received---------------2----------------------' + code.toString());
//
//           final exp = RegExp(r'(\d{4})');
//           print(exp.stringMatch(code.toString()).toString());
//           var otp123 = exp.stringMatch(code ?? '') ?? '';
//           print("OTP NEW ---------- " + otp123);
//
//           otp1.text = otp123[0].toString();
//           otp2.text = otp123[1].toString();
//           otp3.text = otp123[2].toString();
//           otp4.text = otp123[3].toString();
//
//           if (otp123.length == 4) {
//             controller.verifyOTP(widget.from.toString(), widget.phone.toString(), widget.country_code.toString(), otp123);
//           }
//
//           return exp.stringMatch(code ?? '') ?? '';
//         },
//       );
//   }
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//
//     // controller.startTimer('','');
//     otp1OneFocus.requestFocus();
//
//     _initInteractor();
//     getOTP();
//     // controller.startTimer_otp();
//   }
//
//   @override
//   void dispose() {
//     // TODO: implement dispose
//     super.dispose();
//     otptextfldcontroller.stopListen();
//     _otpInteractor.stopListenForCode();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<OtpController>(builder: (controller) {
//       return GestureDetector(
//         onTap: () {
//           FocusScope.of(context).unfocus();
//         },
//         child: Scaffold(
//           backgroundColor: textwhiteColor,
//           appBar: AppBar(
//             toolbarHeight: 0,
//             backgroundColor: textwhiteColor,
//             surfaceTintColor: textwhiteColor,
//           ),
//           body: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               SizedBox(height: Get.height / 10),
//
//               Text('OTP Verification', style: TextStyle(fontFamily: font_semibold, fontSize: Get.width / 17, color: textblackColor)),
//
//               const SizedBox(
//                 height: 8,
//               ),
//
//               Container(
//                 width: Get.width,
//                 alignment: Alignment.center,
//                 padding: EdgeInsets.symmetric(horizontal: 70),
//                 child: Text(
//                   "We have sent the code to your \nmobile number ${widget.country_code.toString()}${widget.phone.toString()}",
//                   style: TextStyle(color: textblackColor, fontSize: Get.width / 29, fontFamily: font_regular),
//                   textAlign: TextAlign.center,
//                 ),
//               ),
//
//               const SizedBox(height: 40),
//
//               Container(
//                 width: Get.width,
//                 margin: EdgeInsets.symmetric(horizontal: 20),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     _textFieldOTP(first: true, last: false, controllerr: otp1, node: otp1OneFocus),
//                     const SizedBox(width: 12),
//                     _textFieldOTP(first: false, last: false, controllerr: otp2, node: otp2OneFocus),
//                     const SizedBox(width: 12),
//                     _textFieldOTP(first: false, last: false, controllerr: otp3, node: otp3OneFocus),
//                     const SizedBox(width: 12),
//                     _textFieldOTP(first: false, last: true, controllerr: otp4, node: otp4OneFocus),
//                   ],
//                 ),
//               ),
//               // text sec-------------------------------------------------------
//
//               SizedBox(
//                 height: 10,
//               ),
//
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   Container(
//                       alignment: Alignment.centerLeft,
//                       color: Colors.transparent,
//                       padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
//                       child: controller.start == 0
//                           ? GestureDetector(
//                               onTap: () {
//                                 controller.startTimer(widget.phone, widget.country_code);
//                                 _initInteractor();
//                                 getOTP();
//                               },
//                               child: Container(
//                                 color: Colors.transparent,
//                                 padding: EdgeInsets.symmetric(vertical: 3),
//                                 child: Wrap(
//                                   children: [
//                                     Text("Don’t Receive a Code ?", style: TextStyle(color: textblackColor, fontWeight: FontWeight.w400, fontSize: Get.width / 26)),
//                                     SizedBox(
//                                       width: 10,
//                                     ),
//                                     Text("Resend", style: TextStyle(color: primaryColor, fontSize: Get.width / 26)),
//                                   ],
//                                 ),
//                               ),
//                             )
//                           : Wrap(
//                               children: [
//                                 Text('00:', style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 26)),
//                                 Text(
//                                   controller.start.toString(),
//                                   style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 26),
//                                 ),
//                                 SizedBox(width: 4),
//                                 Text(
//                                   " to Resend OTP",
//                                   style: TextStyle(
//                                       color: Colors.black54,
//                                       // fontFamily: font_medium,
//                                       fontSize: Get.width / 28),
//                                 ),
//                               ],
//                             )),
//                   // SizedBox(width: 40,),
//                 ],
//               ),
//
//               SizedBox(
//                 height: Get.height / 33,
//               ),
//
//               Expanded(
//                 child: Container(
//                   height: Get.width,
//                   alignment: Alignment.bottomCenter,
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.end,
//                     children: [
//                       common_button_gradient(
//                         onPressed: () {
//                           setState(() {
//                             otp = otp1.text.toString() + otp2.text.toString() + otp3.text.toString() + otp4.text.toString();
//                             if (otp.length == 4) {
//                               controller.verifyOTP(widget.from.toString(), widget.phone.toString(), widget.country_code.toString(), otp);
//                             }
//                           });
//                           // if(otpLength == 4){
//                           //   GetStorage().write('login_status', "true");
//                           //   Get.offAll(()=> dashboard());
//                           // }
//                           // else{
//                           //   // toast_info('');
//                           // }
//                         },
//                         // bg: controller.otp_numbers.length == 4
//                         // bg: otp.length == 4
//                         color1: otpLength == 4 ? secondarylightColor : secondarylightColor.withValues(alpha: 0.4),
//                         width: Get.width,
//                         color2: otpLength == 4 ? secondaryColor : secondaryColor.withValues(alpha: 0.4),
//                         child: controller.isLoading
//                             ? SizedBox(
//                                 height: 25,
//                                 width: 25,
//                                 child: CircularProgressIndicator(
//                                   color: textwhiteColor,
//                                 ),
//                               )
//                             : Text(
//                                 "Confirm",
//                                 style: TextStyle(color: textwhiteColor, fontFamily: font_medium, fontSize: Get.width / 26),
//                               ),
//                       ),
//                       SizedBox(
//                         height: Get.width / 4,
//                       )
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//     });
//   }
//
//   int otpLength = 0;
//
//   Widget _textFieldOTP({bool? first, last, TextEditingController? controllerr, FocusNode? node}) {
//     return Container(
//       // height: Get.height / 12,
//       height: 70,
//       alignment: Alignment.center,
//       child: AspectRatio(
//         aspectRatio: 0.82,
//         child: TextField(
//           focusNode: node,
//           controller: controllerr,
//           autofocus: true,
//           onChanged: (value) {
//             print('this...........');
//             print(value.toString());
//
//             print(otptextfldcontroller.text.toString() + "-----fld");
//
//             String otp = otp1.text.toString() + otp2.text.toString() + otp3.text.toString() + otp4.text.toString();
//             otpLength = otp.length;
//             print('otpLength--------------------------$otpLength');
//
//             if (value.length == 1 && last == false) {
//               FocusScope.of(context).nextFocus();
//             }
//
//             if (value.isEmpty && first == false) {
//               FocusScope.of(context).previousFocus();
//             }
//
//             if (value.length == 1 && last == true) {
//               controller.verifyOTP(widget.from, widget.phone.toString(), widget.country_code.toString(), otp);
//               // if(otpLength == 4){
//               //   GetStorage().write('login_status', "true");
//               //   Get.offAll(()=> dashboard());
//               // }
//               // else{
//               //   // toast_info('');
//               // }
//               // GetStorage().write('login_status', "true");
//               // Get.offAll(()=> dashboard());
//             }
//           },
//           showCursor: false,
//           readOnly: false,
//           textAlign: TextAlign.center,
//           style: TextStyle(color: textblackColor, fontWeight: FontWeight.w800, fontSize: Get.width / 20),
//           keyboardType: TextInputType.number,
//           maxLength: 1,
//           decoration: InputDecoration(
//             counter: Offstage(),
//             enabledBorder: OutlineInputBorder(borderSide: BorderSide(width: 0.5, color: Colors.black26), borderRadius: BorderRadius.circular(5)),
//             focusedBorder: OutlineInputBorder(
//               // borderSide: BorderSide(width: 0.5, color: Colors.black),
//               borderSide: BorderSide(width: 0.7, color: primaryColor),
//               borderRadius: BorderRadius.circular(5),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
