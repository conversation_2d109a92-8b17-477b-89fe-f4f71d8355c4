import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

import '../Controller/VideoPlayerController.dart';

class PlayerControllers extends StatefulWidget {
  bool isFullScreen;
  PlayerControllers({ required this.isFullScreen, super.key,});

  @override
  _PlayerControllersState createState() => _PlayerControllersState();
}

class _PlayerControllersState extends State<PlayerControllers> {

  VideoPlayerGetXController controller = Get.put(VideoPlayerGetXController());

  @override
  void initState() {
    super.initState();
    controller.duration = controller.playerController.value.duration;
    controller.progress = controller.playerController.value.position;
    controller.playerController.addListener(controller.updateProgress);
  }

  @override
  void dispose() {
    controller.playerController.removeListener(controller.updateProgress);
    super.dispose();
  }


  void showMenuBar(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return GetBuilder<VideoPlayerGetXController>(
          builder: (controller) {
            return Container(
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [

                  if(controller.videoFiles.length >1)
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      showQualitySheet(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: const BoxDecoration(
                        color: Colors.transparent,
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.high_quality_outlined),
                          const SizedBox(width: 15),
                          Text(
                            "Quality (${ controller.selectedFile!['rendition'].toString()  == "adaptive" ||  controller.selectedFile!['rendition'].toString() == "null" ?"Auto" : controller.selectedFile!['rendition'].toString()})",
                            style: TextStyle(
                              fontSize: 15,

                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      showSpeedSheet(context);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: const BoxDecoration(
                        color: Colors.transparent,
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.speed),
                          const SizedBox(width: 15),
                          Text(
                            "Playback Speed (${controller.playbackSpeed.toString()}x)",
                            style: TextStyle(
                              fontSize: 15,
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void showQualitySheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return GetBuilder<VideoPlayerGetXController>(
          builder: (controller) {
            return Container(
              height: 300,
                margin: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.all(16.0),
                child: ListView.builder(
                  itemCount: controller.videoFiles.length,
                  shrinkWrap: true,
                  itemBuilder: (_,index){
                    Map data = controller.videoFiles[index];
                    return GestureDetector(
                      onTap: () async {
                        Navigator.pop(context);
                        controller.changeVideo(data);
                        controller.update();
                      },
                      child: Container(
                        padding: const EdgeInsets.all(15),
                        child: Text(
                          data['rendition'].toString()  == "adaptive" ||
                              data['rendition'].toString() == "null"
                                  ? "Auto"
                                  : data['rendition'].toString()
                        ,style: TextStyle(color: Colors.black54,fontSize: 15),),
                      ),
                    );
                  }
                )
              );
          }
        );
      },
    );
  }

  void showSpeedSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return GetBuilder<VideoPlayerGetXController>(
            builder: (controller) {
              return Container(
                  height: 300,
                  margin: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.all(16.0),
                  child: ListView.builder(
                      itemCount: controller.playbackSpeedList.length,
                      shrinkWrap: true,
                      itemBuilder: (_,index){
                        double data = controller.playbackSpeedList[index];
                        return GestureDetector(
                          onTap: () async {
                            Navigator.pop(context);
                            controller.playerController.setPlaybackSpeed(data);
                            controller.update();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            child: Text(data.toString() + "x".toString(),style: TextStyle(color: Colors.black54,fontSize: 15),),
                          ),
                        );
                      }
                  )
              );
            }
        );
      },
    );
  }




  @override
  Widget build(BuildContext context) {
    return GetBuilder<VideoPlayerGetXController>(
      builder: (controller) {
        return Container(
          color: Colors.black.withOpacity(0.3),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [

              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(Icons.more_vert_outlined, color: Colors.white,),
                    onPressed: (){
                      showMenuBar(context);
                    },

                  ),
                ],
              ),


              // controller.playerController.value.isBuffering
              //   ?  SizedBox(width: Get.width/18,height: Get.width/18,child: const CircularProgressIndicator(color: Colors.red,strokeWidth: 2,),)
              //     :

              Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [

                      GestureDetector(
                        onTap: (){
                          controller.rewind5Sec();
                        },
                        child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 5),
                            color: Colors.transparent,
                            child: Icon(Icons.replay_5,color: Colors.white,size: 30)
                        ),
                      ),

                      IconButton(
                          icon: Icon(controller.playerController.value.isPlaying ? Icons.pause_circle_rounded : Icons.play_circle_fill_rounded, color: Colors.white,size: 50,),
                          onPressed: controller.togglePlayPause,
                        ),


                      GestureDetector(
                        onTap: (){
                          controller.forward5Sec();
                        },
                        child: Container(
                            padding: const  EdgeInsets.symmetric(vertical: 5),
                            color: Colors.transparent,
                            child: const  Icon(Icons.forward_5,color: Colors.white,size: 30,)
                        ),
                      ),
                    ],
                  ),




              Column(
                children: [

                  Row(
                    children: [

                      Padding(
                        padding: const EdgeInsets.only(left: 10.0),
                        child: Text("${controller.formatDuration(controller.progress)} / ${controller.formatDuration(controller.duration)}", style: const TextStyle(color: Colors.white, fontSize: 12),),
                      ),

                      Expanded(
                        child: Slider(
                          value: controller.progress.inMilliseconds.toDouble(),
                          max: controller.duration.inMilliseconds.toDouble(),
                          onChanged: controller.onSliderChanged,
                          activeColor: Colors.red,
                          inactiveColor: Colors.grey,
                        ),
                      ),


                      Padding(
                        padding: const EdgeInsets.only(right: 5.0),
                        child: IconButton(
                          icon: Icon(widget.isFullScreen ? Icons.fullscreen_exit_rounded : Icons.fullscreen_rounded, color: Colors.white,),
                          onPressed: (){
                            controller.toggleFullScreen(widget.isFullScreen);
                          },
                        ),
                      ),

                    ],
                  ),
                ],
              ),


            ],
          ),
        );
      }
    );
  }
}




// Row(
// children: [
//
// Expanded(
// child: GestureDetector(
// onTap: (){
// controller.showControl = controller.showControl == true ? false : true;
// controller.update();
// controller.startShowTimer();
// },
// onDoubleTap: (){
// Duration? current = controller.playerController.value.position;
// current = current - Duration(seconds: 10);
// controller.playerController.seekTo(current);
// controller.showRewindAnimation();
// },
// child: Stack(
// children: [
// Container(
// decoration: BoxDecoration(color: Colors.transparent)
// ),
// AnimatedOpacity(
// opacity: controller.isRewinding ? 1.0 : 0.0,
// duration: const Duration(milliseconds: 300),
// child: Center(
// child: Icon(
// Icons.replay_10,
// size: 50,
// color: Colors.white.withOpacity(0.8),
// ),
// ),
// ),
// ],
// )
// )
// ),
//
//
// Expanded(
// child: GestureDetector(
// onTap: (){
// controller.showControl = controller.showControl == true ? false : true;
// controller.update();
// controller.startShowTimer();
// },
// onDoubleTap: (){

// controller.showForwardAnimation();
// },
// child: Stack(
// children: [
// Container(
// decoration: const BoxDecoration(color: Colors.transparent)
// ),
//
// AnimatedOpacity(
// opacity: controller.isForwarding ? 1.0 : 0.0,
// duration: const Duration(milliseconds: 300),
// child: Center(
// child: Icon(
// Icons.forward_10,
// size: 50,
// color: Colors.white.withOpacity(0.8),
// ),
// ),
// ),
//
// ],
// )
// )
// )
// ],
// ),