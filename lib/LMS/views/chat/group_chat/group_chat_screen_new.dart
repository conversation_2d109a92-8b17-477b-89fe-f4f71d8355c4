// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/components/utils.dart';
// import 'package:prepscale/controller/chat/groupchatcontroller.dart';
// import 'package:prepscale/view/chat/group_chat/chat_textfield.dart';
// import 'package:prepscale/view/chat/group_chat/comment_page2.dart';
// import 'package:prepscale/view/chat/group_chat/local_widget/chat_bubbe.dart';
// import 'package:prepscale/view/chat/group_chat/local_widget/delete_message_dialogue.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_speed_dial/flutter_speed_dial.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
//
// class GroupChatScreen extends StatefulWidget {
//   GroupChatScreen({
//     super.key,
//     required this.chatId,
//     required this.chatType,
//     required this.chatTypeId,
//     required this.from,
//   });
//
//   final String chatId;
//   final String chatType, chatTypeId;
//   String from;
//
//   @override
//   _GroupChatScreenState createState() => _GroupChatScreenState();
// }
//
// class _GroupChatScreenState extends State<GroupChatScreen> {
//   final GroupchatController controller = Get.put(GroupchatController());
//   final ScrollController _scrollController = ScrollController();
//
//   String? loadingMessageId; // Track the ID of the message being processed.
//
//   @override
//   void initState() {
//     super.initState();
//     controller.fetchMessages(widget.chatId);
//
//     // Listen to scroll events for pagination
//     _scrollController.addListener(() {
//       if (_scrollController.position.pixels >=
//               _scrollController.position.maxScrollExtent - 200 &&
//           !controller.isLoadingMore &&
//           controller.hasMoreMessages) {
//         Future.delayed(const Duration(milliseconds: 500), () {
//           controller.fetchMessages(widget.chatId, loadMore: true);
//         });
//       }
//     });
//   }
//
//   @override
//   void dispose() {
//     _scrollController.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: const Color.fromARGB(255, 211, 210, 210),
//       appBar: AppBar(
//         backgroundColor: textwhiteColor,
//         title: const Text(
//           'Group Chat',
//           style: TextStyle(fontSize: 15),
//         ),
//       ),
//       body: GetBuilder<GroupchatController>(
//         builder: (controller) {
//           if (widget.from == "chat_list" &&
//               controller.messageListNew.isEmpty &&
//               !controller.isLoadingMore) {
//             return Center(child: loader());
//           }
//           return ListView.builder(
//             controller: _scrollController,
//             itemCount: controller.messageListNew.length +
//                 (controller.hasMoreMessages ? 1 : 0),
//             itemBuilder: (context, index) {
//               if (index == controller.messageListNew.length) {
//                 return loader();
//               }
//               final message = controller.messageListNew[index];
//
//               return Stack(
//                 children: [
//                   Opacity(
//                     opacity: loadingMessageId == message['id'] ? 0.5 : 1,
//                     child: ChatBubble(
//                       onLongPress: () {
//                         showModalBottomSheet(
//                           context: context,
//                           builder: (BuildContext context) {
//                             return Padding(
//                               padding: const EdgeInsets.all(10),
//                               child: Column(
//                                 mainAxisSize: MainAxisSize.min,
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   if (GetStorage().read('is_admin') == "1" ||
//                                       controller.messageList[index]
//                                               ['sender_name'] ==
//                                           "You")
//                                     ListTile(
//                                       title: const Text(
//                                         'Delete Message',
//                                         style: TextStyle(fontSize: 12),
//                                       ),
//                                       onTap: () {
//                                         showDialog(
//                                           context: context,
//                                           builder: (context) {
//                                             return DeleteMessageDialogue(
//                                               onPressed: () async {
//                                                 widget.from = "";
//                                                 setState(() {
//                                                   loadingMessageId =
//                                                       message['id'];
//                                                 });
//                                                 Navigator.of(context).pop();
//                                                 Navigator.of(context).pop();
//                                                 await controller.deleteMessage(
//                                                     message['id'].toString());
//
//                                                 setState(() {
//                                                   loadingMessageId = null;
//                                                 });
//                                               },
//                                             );
//                                           },
//                                         );
//                                       },
//                                     ),
//                                 ],
//                               ),
//                             );
//                           },
//                         );
//                       },
//                       messageType: message['message_type'].toString(),
//                       messageFile: message['message_file'].toString(),
//                       isHighlight: message['is_highlight'].toString(),
//                       message: message['message'],
//                       profiel_image_path: message['sender_image'],
//                       username: message['sender_name'],
//                       messageDate:
//                           '${message['message_time']} - ${message['message_date']}',
//                       commentOnTap: () {
//                         controller.commentId = message['id'].toString();
//                         Get.to(
//                           CommentPage2(
//                             from: "group_chat",
//                             chatId: message['id'].toString(),
//                             username: message['sender_name'],
//                             messageDate:
//                                 "${message['message_time']} - ${message['message_date']}",
//                             profileImage: message['sender_image'],
//                             chatType: widget.chatType,
//                             chatTypeId: widget.chatTypeId,
//                           ),
//                         );
//                       },
//                       moreOnTap: () {
//                         // Handle more actions if required
//                       },
//                     ),
//                   ),
//                   if (loadingMessageId == message['id'])
//                     Positioned.fill(
//                       child: Center(child: loader()),
//                     ),
//                 ],
//               );
//             },
//           );
//         },
//       ),
//       floatingActionButton: SpeedDial(
//         elevation: 0,
//         icon: Icons.add,
//         foregroundColor: textwhiteColor,
//         activeIcon: Icons.close,
//         backgroundColor: primaryColor,
//         children: [
//           SpeedDialChild(
//             child: const Icon(Icons.chat, color: Colors.white),
//             backgroundColor: Colors.blue,
//             label: 'Ask a doubt',
//             onTap: () {
//               Get.to(ChatTextField2(
//                 discussionType: widget.chatType,
//                 chatTypeId: widget.chatTypeId,
//               ));
//             },
//           ),
//         ],
//       ),
//     );
//   }
// }
