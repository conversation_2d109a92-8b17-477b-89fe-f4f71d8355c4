import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/leaderboard_controller.dart';

class LeaderBoardScreen extends StatefulWidget {
  final String title;

  LeaderBoardScreen({Key? key, required this.title}) : super(key: key);

  @override
  State<LeaderBoardScreen> createState() => _LeaderBoardScreenState();
}

class _LeaderBoardScreenState extends State<LeaderBoardScreen> with SingleTickerProviderStateMixin{

  late TabController _tabController;
  LeaderboardController controller = Get.put(LeaderboardController());
  int selectedTab = 0;


  @override
  void initState() {
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        if (_tabController.indexIsChanging) {
          print("Selected Tab: ${_tabController.index}");
          selectedTab = _tabController.index;
        }
      });

    });
   controller.get_all_leaderboard_data();
    super.initState();
  }
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LeaderboardController>(
      builder: (controller) {
        return DefaultTabController(
          length: 3,
          child: Scaffold(
            backgroundColor: Colors.white,
            body: controller.isAllLeaderboardLoading ? loader() :  SingleChildScrollView(
              child: Column(
                children: [
                  buildTopBanner(),
                  SizedBox(height: 14),
                  Column(
                    children: [

                      
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                        child: Row(
                          children: [
                            Expanded(flex: 1, child: Text("Rank", style: TextStyle(fontWeight: FontWeight.bold))),
                            Expanded(flex: 3, child: Text("Name", style: TextStyle(fontWeight: FontWeight.bold))),
                            Expanded(flex: 1, child: Text("Top Mark", style: TextStyle(fontWeight: FontWeight.bold))),
                          ],
                        ),
                      ),

                      SizedBox(height: 10),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.all(0),
                        itemCount: selectedTab == 0 ? controller.overallLeaderBoard['daily']?.length : selectedTab == 1 ? controller.overallLeaderBoard['weekly']?.length : controller.overallLeaderBoard['monthly']?.length,
                        itemBuilder: (context, index) {
                          final item = selectedTab == 0 ? controller.overallLeaderBoard['daily']![index] : selectedTab == 1 ? controller.overallLeaderBoard['weekly']![index] : controller.overallLeaderBoard['monthly']![index];
                          return Container(
                            margin: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(color: Colors.transparent),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black12,
                                  blurRadius: 6,
                                  offset: Offset(0, 2),
                                )
                              ],
                            ),
                            child: Row(
                              children: [
                                SizedBox(width: 5),
                                Expanded(flex: 0, child: Text("${item['rank']}")),
                                SizedBox(width: 10),
                                SizedBox(
                                  height: 50,
                                  child: VerticalDivider(
                                    color: textblackColor.withValues(alpha: 0.1),
                                    width: 2,
                                    endIndent: 1,
                                    indent: 2,
                                    thickness: 1,
                                  ),
                                ),
                                SizedBox(width: 10),
                                Expanded(
                                  flex: 3,
                                  child: Row(
                                    children: [
                                      CircleAvatar(
                                        radius: 15,
                                        child: Image.network(
                                          item['profile_image'].toString(),
                                          width: Get.width / 4.5,
                                          height: Get.width / 4.5,
                                          fit: BoxFit.cover,
                                          errorBuilder: (_,__,___){ return ClipRRect(borderRadius: BorderRadius.circular(1000),child: Image.asset("assets/images/avatar_placeholder.png"));},
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Text(item["name"]!),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Text(
                                    "${item['score']}",
                                    textAlign: TextAlign.right,
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                SizedBox(width: 5),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      }
    );
  }

  Widget buildTopBanner() {
    return Container(
      padding: EdgeInsets.only(top: 0, left: 0, right: 0, bottom: 24),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/bg/leader_bg.png'),
          fit: BoxFit.cover,
        ),
        gradient: LinearGradient(colors: [
          Color(0xFF346367),
          Color(0xFF264649),
        ]),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(40),
          bottomRight: Radius.circular(40),
        ),
      ),
      child: Column(
        children: [
          AppBar(
            backgroundColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
            elevation: 5,
            centerTitle: true,
            leading: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Container(
                margin: EdgeInsets.all(10),
                child: Icon(Icons.arrow_back_ios_new_rounded, color: textwhiteColor),
              ),
            ),
            title: Text(
              widget.title,
              style: TextStyle(
                color: textwhiteColor,
                fontWeight: FontWeight.w700,
                fontFamily: font_regular,
                fontSize: Get.width / 24,
              ),
            ),
          ),
          SizedBox(height: 15),
          TabBar(
            padding: EdgeInsets.zero,
            labelPadding: EdgeInsets.zero,
            indicatorPadding: EdgeInsets.zero,
            labelColor: textwhiteColor,
            unselectedLabelColor: textwhiteColor,
            labelStyle: TextStyle(fontWeight: FontWeight.bold),
            indicatorColor: Color(0xFFFEC196),
            controller: _tabController,
            tabs: [
              Tab(text: "Daily"),
              Tab(text: "Weekly"),
              Tab(text: "Monthly"),
            ],
          ),
          SizedBox(height: 24),

          selectedTab == 0 ?  Leaderboard(controller.overallLeaderBoard['daily']!) : selectedTab  == 1  ?
          Leaderboard(controller.overallLeaderBoard['weekly']!) :
          Leaderboard(controller.overallLeaderBoard['monthly']!)

          // TabBarView(children: [
          //   Leaderboard(controller.overallLeaderBoard['daily']),
          //   Leaderboard(controller.overallLeaderBoard['weekly']),
          //   Leaderboard(controller.overallLeaderBoard['monthly']),
          // ])
        ],
      ),
    );
  }
}

class Leaderboard extends StatelessWidget {


  List items;
  Leaderboard(this.items);


  @override
  Widget build(BuildContext context) {
    
    if(items.isEmpty)
      {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 50),
          child: Text("No Profile's found",style: TextStyle(fontFamily: font_medium,color: Colors.white),),
        );
      }
      
    return Container(
      height: Get.height * .2,
      child: Stack(
        alignment: Alignment.center,
        children: [

          if(items.isNotEmpty)
          Positioned(
            top: 0,
            child: LeaderboardItem(
              alignment: Alignment.topRight,
              rank: '1st',
              name: items[0]['name'].toString(),
              score: items[0]['score'].toString(),
              imagePath: items[0]['profile_image'].toString(),
              color: Colors.orange,
              textColor: Colors.white,
            ),
          ),


          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Spacer(),

                items.length >= 3 ?
                Padding(
                  padding: const EdgeInsets.only(bottom: 0),
                  child: LeaderboardItem(
                    alignment: Alignment.topLeft,
                    rank: '3rd',
                    name: items[2]['name'].toString(),
                    score: items[2]['score'].toString(),
                    imagePath: items[2]['profile_image'].toString(),
                    color: Colors.white,
                    textColor: Colors.black,
                  ),
                ) : Container(),

                Spacer(),
                Spacer(),
                Spacer(),

                items.length >= 2 ?
                Padding(
                  padding: const EdgeInsets.only(bottom: 0),
                  child: LeaderboardItem(
                    alignment: Alignment.topRight,
                    rank: '2nd',
                    name: items[1]['name'].toString(),
                    score: items[1]['score'].toString(),
                    imagePath: items[1]['profile_image'].toString(),
                    color: Colors.white,
                    textColor: Colors.black,
                  ),
                ) : Container(),
                Spacer(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LeaderboardItem extends StatelessWidget {
  final String rank;
  final String name;
  final String score;
  final String imagePath;
  final Color color;
  final Color textColor;
  final AlignmentGeometry alignment;

  LeaderboardItem({
    required this.rank,
    required this.name,
    required this.score,
    required this.imagePath,
    required this.color,
    required this.textColor,
    required this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          alignment: alignment,
          children: [

            CircleAvatar(
              radius: 40,
              child: Image.network(
                imagePath,
                width: Get.width / 5.5,
                height: Get.width / 5.5,
                fit: BoxFit.cover,
                errorBuilder: (_,__,___){ return ClipRRect(borderRadius: BorderRadius.circular(1000),child: Image.asset("assets/images/avatar_placeholder.png"));},
              ),
            ),

            Container(
              height: 50,
              width: Get.width * .09,
              decoration: BoxDecoration(
                image: DecorationImage(
                  colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                  image: AssetImage('assets/icons/hexagon.png'),
                ),
              ),
              child: Center(
                child: Text(
                  rank,
                  style: TextStyle(
                    color: textColor,
                    fontWeight: FontWeight.bold,
                    fontSize: Get.width * .03,
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Text(name, style: TextStyle(color: textwhiteColor,fontFamily: font_medium)),
        Text(score, style: TextStyle(color: textwhiteColor, fontSize: 15,fontFamily: font_semibold)),
      ],
    );
  }
}
