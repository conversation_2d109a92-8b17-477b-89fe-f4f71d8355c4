// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/components/utils.dart';
// import 'package:prepscale/view/chat/controller/groupchatcontroller.dart';
// import 'package:prepscale/view/chat/group_chat/local_widget/image_preview.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// class CommentPage2 extends StatefulWidget {
//   CommentPage2(
//       {super.key,
//       required this.chatId,
//       required this.username,
//       required this.profileImage,
//       required this.messageDate,
//       required this.chatType,
//       required this.chatTypeId,
//       required this.from});
//   String chatId;
//   String profileImage, username, messageDate;
//   String chatType, chatTypeId;
//   String from;
//
//   @override
//   State<CommentPage2> createState() => _CommentPage2State();
// }
//
// class _CommentPage2State extends State<CommentPage2> {
//   final controller = Get.put(GroupchatController());
//   TextEditingController _commentController = TextEditingController();
//   FocusNode _focusNode = FocusNode(); // FocusNode to control the keyboard
//
//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       controller.messageComments(widget.chatId);
//     });
//   }
//
//   @override
//   void dispose() {
//     _focusNode.dispose(); // Dispose the FocusNode to avoid memory leaks
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<GroupchatController>(
//       builder: (controller) {
//         var chatList = controller.commentData?["chat_list"] ?? [];
//         return Scaffold(
//           appBar: AppBar(
//             leadingWidth: 25,
//             backgroundColor: textwhiteColor,
//             leading: IconButton(
//               onPressed: () {
//                 Get.back();
//               },
//               icon: const Icon(Icons.arrow_back_ios_new),
//             ),
//             title: Row(
//               children: [
//                 CircleAvatar(
//                   backgroundImage: NetworkImage("${widget.profileImage}"),
//                 ),
//                 const SizedBox(width: 10),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       children: [
//                         Text("${widget.username}",
//                             style: const TextStyle(fontSize: 13)),
//                       ],
//                     ),
//                     Text("${widget.messageDate}",
//                         style:
//                             const TextStyle(fontSize: 10, color: Colors.grey)),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//           body: SafeArea(
//             child: Stack(
//               children: [
//                 Column(
//                   children: [
//                     Expanded(
//                       child: Container(
//                         color: textwhiteColor,
//                         // height: Get.height,
//                         child: SingleChildScrollView(
//                           child: Column(
//                             children: [
//                               SizedBox(height: 20),
//                               controller.messageCommentLoading &&
//                                       widget.from == "group_chat"
//                                   ? loader()
//                                   : Container(
//                                       margin: const EdgeInsets.all(5),
//                                       decoration: BoxDecoration(
//                                         borderRadius: BorderRadius.circular(10),
//                                         color: textwhiteColor,
//                                       ),
//                                       child: Column(
//                                         children: [
//                                           if (chatList.isNotEmpty)
//                                             if (chatList[0]['message_type'] ==
//                                                 "1")
//                                               Text(chatList[0]['message']),
//                                           if (chatList.isNotEmpty)
//                                             if (chatList[0]['message_type'] ==
//                                                 "2")
//                                               GestureDetector(
//                                                 onTap: () {
//                                                   Get.to(ImagePreviewPage(
//                                                       imageUrl: chatList[0]
//                                                           ['message_file']));
//                                                 },
//                                                 child: Image.network(
//                                                   chatList[0]['message_file'],
//                                                   fit: BoxFit.contain,
//                                                 ),
//                                               ),
//                                           SizedBox(height: 20),
//                                           const Divider(
//                                               color: Color.fromARGB(
//                                                   255, 237, 234, 234)),
//                                           SizedBox(height: 20),
//                                           Row(
//                                             mainAxisAlignment:
//                                                 MainAxisAlignment.spaceEvenly,
//                                             children: [
//                                               customIcon(
//                                                   "assets/icons/like.png",
//                                                   "Like",
//                                                   () {}),
//                                               customIcon(
//                                                   "assets/icons/comment.png",
//                                                   "Comment", () {
//                                                 // When the comment button is clicked, focus on the text field to open the keyboard
//                                                 _focusNode.requestFocus();
//                                               }),
//                                               customIcon(
//                                                   "assets/icons/whatsapp_chat.png",
//                                                   "Share",
//                                                   () {}),
//                                             ],
//                                           ),
//                                           SizedBox(height: 20),
//                                         ],
//                                       ),
//                                     ),
//
//                               // if (chatList.isEmpty) Center(child: Text('No comments')),
//                               Padding(
//                                 padding: const EdgeInsets.all(8.0),
//                                 child: Align(
//                                     alignment: Alignment.centerLeft,
//                                     child: Text("Comments")),
//                               ),
//
//                               if (chatList.isNotEmpty)
//                                 ListView.builder(
//                                   shrinkWrap: true,
//                                   physics: const NeverScrollableScrollPhysics(),
//                                   itemCount:
//                                       chatList[0]['reply_messages'].length,
//                                   itemBuilder: (context, index) {
//                                     var data =
//                                         chatList[0]['reply_messages'][index];
//                                     return Padding(
//                                       padding: const EdgeInsets.all(8.0),
//                                       child: Row(
//                                         children: [
//                                           const SizedBox(width: 10),
//                                           Container(
//                                             margin: const EdgeInsets.only(
//                                                 right: 13),
//                                             child: ClipRRect(
//                                               borderRadius:
//                                                   BorderRadius.circular(1000),
//                                               child: Image.network(
//                                                 data['sender_image'],
//                                                 width: Get.width / 10,
//                                                 height: Get.width / 10,
//                                                 fit: BoxFit.cover,
//                                                 errorBuilder: (a, b, c) {
//                                                   return ClipRRect(
//                                                     borderRadius:
//                                                         BorderRadius.circular(
//                                                             1000),
//                                                     child: Image.asset(
//                                                       "assets/img/user_placeholder.png",
//                                                       width: Get.width / 10,
//                                                     ),
//                                                   );
//                                                 },
//                                               ),
//                                             ),
//                                           ),
//                                           Container(
//                                             decoration: BoxDecoration(
//                                               color: Colors.white,
//                                               borderRadius:
//                                                   const BorderRadius.only(
//                                                 topRight: Radius.circular(20),
//                                                 bottomRight: Radius.circular(0),
//                                                 topLeft: Radius.circular(0),
//                                                 bottomLeft: Radius.circular(20),
//                                               ),
//                                             ),
//                                             constraints: BoxConstraints(
//                                                 maxWidth: Get.width / 1.35),
//                                             padding: const EdgeInsets.only(
//                                                 left: 15,
//                                                 right: 15,
//                                                 top: 10,
//                                                 bottom: 10),
//                                             child: Column(
//                                               crossAxisAlignment:
//                                                   CrossAxisAlignment.start,
//                                               children: [
//                                                 Row(
//                                                   children: [
//                                                     Text(
//                                                       '${data['sender_name']}',
//                                                       style: TextStyle(
//                                                         color: Colors.black,
//                                                         fontFamily: font_medium,
//                                                         fontSize:
//                                                             Get.width / 40,
//                                                       ),
//                                                     ),
//                                                   ],
//                                                 ),
//                                                 const SizedBox(height: 3),
//                                                 Text(
//                                                   data['message'],
//                                                   style: TextStyle(
//                                                     color: Colors.black,
//                                                     fontFamily: font_regular,
//                                                     fontSize: Get.width / 30,
//                                                   ),
//                                                 ),
//                                                 const SizedBox(height: 3),
//                                                 Text(
//                                                   data['message_time'],
//                                                   style: TextStyle(
//                                                     color: Colors.black
//                                                         .withOpacity(0.5),
//                                                     fontFamily: font_regular,
//                                                     fontSize: Get.width / 45,
//                                                   ),
//                                                 ),
//                                                 if (data['message_type'] == "2")
//                                                   Image.network(
//                                                     height: Get.width - 125,
//                                                     width: Get.width - 150,
//                                                     data['message_file'],
//                                                     fit: BoxFit.cover,
//                                                   ),
//                                               ],
//                                             ),
//                                           ),
//                                         ],
//                                       ),
//                                     );
//                                   },
//                                 ),
//                               if (controller.messageCommentLoading &&
//                                   widget.from != "group_chat")
//                                 loader(),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//                 Positioned(
//                   left: 0,
//                   right: 0,
//                   bottom: 0,
//                   child: Container(
//                     padding: EdgeInsets.symmetric(horizontal: 10),
//                     height: 70,
//                     decoration: BoxDecoration(
//                       color: Colors.white,
//                       boxShadow: [
//                         BoxShadow(
//                           blurRadius: 1,
//                           spreadRadius: 1,
//                           color: Colors.grey,
//                           offset: Offset(0, 2),
//                         ),
//                       ],
//                     ),
//                     child: Row(
//                       children: [
//                         Icon(Icons.camera_alt),
//                         SizedBox(width: 10),
//                         Expanded(
//                           child: Container(
//                             height: 40,
//                             decoration: BoxDecoration(
//                               color: Colors.grey[300],
//                               borderRadius: BorderRadius.circular(40),
//                             ),
//                             child: TextField(
//                               controller: _commentController,
//                               focusNode: _focusNode, // Attach the FocusNode
//                               decoration: InputDecoration(
//                                 border: InputBorder.none,
//                                 hintText: 'Comment something..',
//                                 contentPadding: EdgeInsets.only(left: 16),
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(width: 5),
//                         IconButton(
//                           icon: Icon(Icons.send),
//                           onPressed: () {
//                             widget.from = "";
//                             controller.sendMessage(
//                                 chatType: widget.chatType,
//                                 chatTypeId: widget.chatTypeId,
//                                 messageType: "1",
//                                 message: _commentController.text.toString(),
//                                 isTaggedMessage: "1",
//                                 taggedMsgId: widget.chatId.toString());
//                             _commentController.clear();
//                             // controller.messageComments(widget.chatId);
//                           },
//                         ),
//                       ],
//                     ),
//                   ),
//                 )
//               ],
//             ),
//           ),
//         );
//       },
//     );
//   }
//
//   InkWell customIcon(image_path, title, void Function()? onTap) {
//     return InkWell(
//       onTap: onTap,
//       child: Column(
//         children: [
//           Row(
//             children: [
//               Image.asset(
//                 image_path,
//                 height: 20,
//               ),
//               SizedBox(width: 5),
//               Text(
//                 title,
//                 style: TextStyle(color: Colors.blueGrey, fontSize: 10),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }
