import 'package:edutalim/views/auth/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'components/constants.dart';

void main() async {

  WidgetsFlutterBinding.ensureInitialized();

  await GetStorage.init();

  // FlutterError.onError = (details) {
  //   Get.offAll(()=> error_404());
  //   // FlutterError.dumpErrorToConsole(details);
  // };


  // if (Firebase.apps.isEmpty) {
  //   await Firebase.initializeApp();
  // } // Ensure proper initialization

  // await PushNotificationService().setupInteractedMessage();

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  GetStorage().write('home_index', 0);
  // GetStorage().write('onboarding', true);

  runApp(
    GetMaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(brightness: Brightness.light, fontFamily: font_regular),
      title: appName,
      home: SplashScreen(),

      // Commented The Call Log & Lead Calll history Pages
    ),
  );
}
