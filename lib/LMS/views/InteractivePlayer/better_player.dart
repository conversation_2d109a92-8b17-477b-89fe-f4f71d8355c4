import 'dart:developer';
import 'dart:io';

import 'package:better_player_plus/better_player_plus.dart';
import 'package:dio/dio.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'Controller/VideoPlayerController.dart';
import 'CustomMp4Player.dart';

class better_player_page extends StatefulWidget {
  final Map data;

  const better_player_page({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  State<better_player_page> createState() => _better_player_pageState();
}

class _better_player_pageState extends State<better_player_page> with WidgetsBindingObserver {
  late VideoPlayerGetXController controller;
  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  Duration? _lastKnownPosition;
  bool _wasPlayingBeforePause = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    controller = Get.put(VideoPlayerGetXController());
    _initializePlayer();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _disposePlayer();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (!_isInitialized) return;

    switch (state) {
      case AppLifecycleState.paused:
        // Store position before pausing
        _lastKnownPosition = controller.betterPlayerController?.videoPlayerController?.value.position;
        _wasPlayingBeforePause = controller.betterPlayerController?.isPlaying() ?? false;
        controller.betterPlayerController?.pause();
        log('App paused, stored position: $_lastKnownPosition');
        break;
      case AppLifecycleState.resumed:
        // Restore position when app resumes
        if (_lastKnownPosition != null && _lastKnownPosition! > Duration.zero) {
          Future.delayed(Duration(milliseconds: 200), () {
            controller.betterPlayerController?.seekTo(_lastKnownPosition!);
            if (_wasPlayingBeforePause) {
              Future.delayed(Duration(milliseconds: 100), () {
                controller.betterPlayerController?.play();
              });
            }
          });
        }
        break;
      case AppLifecycleState.inactive:
        _lastKnownPosition = controller.betterPlayerController?.videoPlayerController?.value.position;
        controller.betterPlayerController?.pause();
        break;
      default:
        break;
    }
  }

  Future<void> _initializePlayer() async {
    try {
      if (widget.data['video_links'] == null || (widget.data['video_links'] as List).isEmpty) {
        setState(() {
          _hasError = true;
          _errorMessage = 'No video links available';
        });
        return;
      }

      controller.videoFiles = widget.data['video_links'];
      controller.videoData = widget.data;

      final Map<String, String> qualityUrls = _buildQualityUrls();

      if (qualityUrls.isEmpty) {
        setState(() {
          _hasError = true;
          _errorMessage = 'No valid video URLs found';
        });
        return;
      }

      final String selectedUrl = _selectBestQuality(qualityUrls);
      log('Using video URL: $selectedUrl');

      await _setupBetterPlayer(selectedUrl, qualityUrls);

      setState(() {
        _isInitialized = true;
        _hasError = false;
      });
    } catch (e) {
      log('Error initializing player: $e');
      setState(() {
        _hasError = true;
        _errorMessage = 'Failed to initialize video player: ${e.toString()}';
      });
    }
  }

  Map<String, String> _buildQualityUrls() {
    final Map<String, String> qualityUrls = {};

    for (var file in widget.data['video_links']) {
      if (file['rendition'] != null && file['rendition'] != 'adaptive' && file['video_url'] != null) {
        qualityUrls[file['rendition']] = file['video_url'];
      }
    }

    return qualityUrls;
  }

  String _selectBestQuality(Map<String, String> qualityUrls) {
    // Priority order for quality selection
    final qualityPriority = ['Auto', '1080p', '720p', '480p', '360p', '240p'];

    for (String quality in qualityPriority) {
      if (qualityUrls.containsKey(quality)) {
        return qualityUrls[quality]!;
      }
    }

    // Fallback to first available
    return qualityUrls.values.first;
  }

  Future<void> _setupBetterPlayer(String selectedUrl, Map<String, String> qualityUrls) async {
    // iOS-specific configuration for better seeking
    final BetterPlayerConfiguration betterPlayerConfiguration = BetterPlayerConfiguration(
      aspectRatio: 16 / 9,
      fit: BoxFit.contain,
      autoPlay: true,
      // Changed to false for better control
      looping: false,
      fullScreenByDefault: false,
      allowedScreenSleep: false,
      startAt: Duration.zero,
      // Ensure it starts at beginning only initially
      deviceOrientationsAfterFullScreen: [
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ],
      systemOverlaysAfterFullScreen: [
        SystemUiOverlay.top,
        SystemUiOverlay.bottom,
      ],
      controlsConfiguration: BetterPlayerControlsConfiguration(
        enableQualities: true,
        enableAudioTracks: true,
        enableSubtitles: false,
        enablePlaybackSpeed: true,
        enableMute: true,
        enableFullscreen: true,
        enablePip: Platform.isIOS,
        enableSkips: true,
        enableProgressBar: true,
        enableProgressText: true,
        progressBarPlayedColor: Colors.blue,
        progressBarHandleColor: Colors.blueAccent,
        progressBarBufferedColor: Colors.grey.withOpacity(0.3),
        progressBarBackgroundColor: Colors.grey.withOpacity(0.2),
        forwardSkipTimeInMilliseconds: 10000,
        backwardSkipTimeInMilliseconds: 10000,
        showControlsOnInitialize: true,
        controlsHideTime: Duration(seconds: 1),
      ),
      // iOS-specific buffer configuration - FIXED for pause/play issue
      // bufferingConfiguration: BetterPlayerBufferingConfiguration(
      //   minBufferMs: Platform.isIOS ? 10000 : 5000,
      //   maxBufferMs: Platform.isIOS ? 30000 : 20000,
      //   bufferForPlaybackMs: Platform.isIOS ? 1500 : 1000,
      //   bufferForPlaybackAfterRebufferMs: Platform.isIOS ? 3000 : 2000,
      //   // Key fix: Maintain buffer during pause
      //   maxBufferDelayMs: Platform.isIOS ? 1000 : 500,
      // ),
      // Error handling
      eventListener: (BetterPlayerEvent event) {
        _handlePlayerEvent(event);
      },
    );

    final BetterPlayerDataSource betterPlayerDataSource = BetterPlayerDataSource(
      BetterPlayerDataSourceType.network,
      selectedUrl,
      resolutions: qualityUrls,
      videoFormat: BetterPlayerVideoFormat.other,
      // iOS-specific headers for better compatibility
      headers: Platform.isIOS
          ? {
              'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
              'Accept': '*/*',
              'Accept-Encoding': 'identity',
              'Connection': 'keep-alive',
            }
          : null,
      // Caching configuration
      cacheConfiguration: BetterPlayerCacheConfiguration(
        useCache: true,
        preCacheSize: Platform.isIOS ? 10 * 1024 * 1024 : 5 * 1024 * 1024,
        // 10MB for iOS, 5MB for Android
        maxCacheSize: Platform.isIOS ? 100 * 1024 * 1024 : 50 * 1024 * 1024,
        // 100MB for iOS, 50MB for Android
        maxCacheFileSize: Platform.isIOS ? 50 * 1024 * 1024 : 25 * 1024 * 1024,
        key: 'video_${widget.data['id'] ?? 'unknown'}',
      ),
    );

    controller.betterPlayerController = BetterPlayerController(
      betterPlayerConfiguration,
      betterPlayerDataSource: betterPlayerDataSource,
    );

    // Add a small delay for iOS initialization
    if (Platform.isIOS) {
      await Future.delayed(Duration(milliseconds: 500));
    }
  }

  void _handlePlayerEvent(BetterPlayerEvent event) {
    switch (event.betterPlayerEventType) {
      case BetterPlayerEventType.initialized:
        log('Player initialized');
        // Restore position if we had one
        if (_lastKnownPosition != null && _lastKnownPosition! > Duration.zero) {
          Future.delayed(Duration(milliseconds: 100), () {
            controller.betterPlayerController?.seekTo(_lastKnownPosition!);
          });
        }
        break;
      case BetterPlayerEventType.play:
        log('Player started playing');
        _wasPlayingBeforePause = true;
        break;
      case BetterPlayerEventType.pause:
        log('Player paused');
        _wasPlayingBeforePause = false;
        // Store current position when paused
        _lastKnownPosition = controller.betterPlayerController?.videoPlayerController?.value.position;
        log('Stored position on pause: $_lastKnownPosition');
        break;
      case BetterPlayerEventType.seekTo:
        log('Player seeked to: ${event.parameters}');
        break;
      case BetterPlayerEventType.exception:
        log('Player exception: ${event.parameters}');
        setState(() {
          _hasError = true;
          _errorMessage = 'Playback error occurred';
        });
        break;
      case BetterPlayerEventType.bufferingStart:
        log('Buffering started');
        break;
      case BetterPlayerEventType.bufferingEnd:
        log('Buffering ended');
        // Restore position after buffering if needed
        if (_lastKnownPosition != null && _lastKnownPosition! > Duration.zero) {
          final currentPosition = controller.betterPlayerController?.videoPlayerController?.value.position;
          if (currentPosition != null && currentPosition == Duration.zero) {
            log('Restoring position after buffering: $_lastKnownPosition');
            controller.betterPlayerController?.seekTo(_lastKnownPosition!);
          }
        }
        break;
      case BetterPlayerEventType.finished:
        controller.updateLessonFileProgress(controller.videoData['id'].toString());
        break;
      default:
        break;
    }
  }

  void _disposePlayer() {
    try {
      // Save playback position if needed
      final position = controller.betterPlayerController.videoPlayerController?.value.position;
      final duration = controller.betterPlayerController.videoPlayerController?.value.duration;

      if (position != null && duration != null && position != Duration.zero) {
        // Uncomment and implement your save function
        // _saveVideoProgress(
        //   widget.data['id'].toString(),
        //   duration.toString().split('.').first,
        //   position.toString().split('.').first,
        // );
      }

      controller.betterPlayerController?.removeEventsListener(_handlePlayerEvent);
      controller.betterPlayerController?.dispose();
    } catch (e) {
      log('Error disposing player: $e');
    }
  }

  Widget _buildErrorWidget() {
    return Container(
      width: Get.width,
      height: Get.height * 0.26,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 48),
            SizedBox(height: 16),
            Text('Video Error', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Padding(padding: EdgeInsets.symmetric(horizontal: 16), child: Text(_errorMessage, style: TextStyle(color: Colors.white70, fontSize: 14), textAlign: TextAlign.center)),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _isInitialized = false;
                });
                _initializePlayer();
              },
              child: Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      width: Get.width,
      height: Get.height * 0.26,
      decoration: BoxDecoration(color: Colors.black, borderRadius: BorderRadius.circular(8)),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(Colors.blue)),
            SizedBox(height: 16),
            Text('Loading Video...', style: TextStyle(color: Colors.white, fontSize: 16)),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return _buildErrorWidget();
    }

    if (!_isInitialized) {
      return _buildLoadingWidget();
    }

    return Container(
      width: Get.width,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: Colors.black),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: BetterPlayer(controller: controller.betterPlayerController!),
        ),
      ),
    );
  }
}
