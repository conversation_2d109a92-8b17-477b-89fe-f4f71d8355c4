import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:edutalim/LMS/views/tools/image_viewer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../../controllers/assignmentcontroller.dart';
import '../../../../../controllers/commoncontroller.dart';
import '../../../tools/pdf_viewer.dart';

class assignment_details extends StatefulWidget {
  String assignment_id, from;

  assignment_details({super.key, required this.assignment_id, required this.from});

  @override
  State<assignment_details> createState() => _assignment_detailsState();
}

class _assignment_detailsState extends State<assignment_details> {
  AssignmentController controller = Get.put(AssignmentController());
  Commoncontroller commoncontroller = Get.put(Commoncontroller());

  @override
  void initState() {
    controller.getAssignmentDetails(widget.assignment_id.toString());
    super.initState();
  }

  @override
  void dispose() {
    controller.assignmentfile = null;
    controller.isAssignmentUpload = false;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AssignmentController>(builder: (controller) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // tool bar
            Container(
              decoration: BoxDecoration(color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(.1), blurRadius: 60, spreadRadius: 0, offset: const Offset(0, 3))]),
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 0),
              child: Column(
                children: [
                  const SizedBox(
                    height: 15,
                  ),
                  AppBar(backgroundColor: Colors.white, surfaceTintColor: Colors.white, toolbarHeight: 0),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          color: Colors.transparent,
                          padding: const EdgeInsets.all(5),
                          child: Icon(CupertinoIcons.left_chevron, color: Colors.black, size: Get.width / 18),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Text("Assignment Details", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 23)),
                    ],
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                ],
              ),
            ),

            Expanded(
              child: controller.isDetailsLoading
                  ? Center(child: loader())
                  : Stack(
                      children: [
                        SingleChildScrollView(
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (widget.from.toString() == "result")
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(height: 25),
                                      Container(
                                        width: Get.width,
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: secondaryColor.withOpacity(0.2)),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text("Teacher Remarks", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 27)),

                                            controller.assignmentDetails['teacher_review'].toString() == "0" ?
                                            Column(
                                              children: [
                                                Padding(
                                                  padding: const EdgeInsets.all(15.0),
                                                  child: Text(controller.assignmentDetails['teacher_content'].toString(), style: TextStyle(fontSize: Get.width / 30, color: Color(0xFF5B5B5B))),
                                                )
                                              ],
                                            ) : 
                                            Column(
                                              children: [
                                                const SizedBox(height: 10),
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Row(
                                                      crossAxisAlignment: CrossAxisAlignment.center,
                                                      children: [
                                                        Text(" Mark :    ", style: TextStyle(fontSize: Get.width / 26, fontWeight: FontWeight.w600, color: primaryColor)),
                                                        Text(controller.assignmentDetails['assignment_mark'].toString(), style: TextStyle(fontSize: Get.width / 22, fontFamily: font_bold, color: primaryColor)),
                                                      ],
                                                    ),
                                                    // Row(
                                                    //   crossAxisAlignment: CrossAxisAlignment.center,
                                                    //   children: [
                                                    //     Text(" Grade :    ", style: TextStyle(fontSize: Get.width / 26, fontWeight: FontWeight.w600, color: primaryColor)),
                                                    //     Container(
                                                    //       decoration: BoxDecoration(color: primaryColor.withValues(alpha: 0.1), border: Border.all(color: primaryColor), borderRadius: BorderRadius.circular(7)),
                                                    //       padding: EdgeInsets.symmetric(horizontal: 10, vertical: 7),
                                                    //       child: Text("A+", style: TextStyle(fontSize: Get.width / 22, fontFamily: font_bold, color: primaryColor)),
                                                    //     ),
                                                    //   ],
                                                    // ),
                                                  ],
                                                ),
                                                const SizedBox(height: 10),
                                                Text(
                                                  controller.assignmentDetails['teacher_remark'].toString(),
                                                  style: TextStyle(fontSize: Get.width / 30, color: Color(0xFF5B5B5B)),
                                                )
                                              ],
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                const SizedBox(
                                  height: 25,
                                ),
                                Text(controller.assignmentDetails['title'].toString(), style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 23)),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  removehtml_tags(controller.assignmentDetails['description'].toString()),
                                  style: TextStyle(color: const Color(0xFF7B7B7B), fontSize: Get.width / 28, fontWeight: FontWeight.w400, fontFamily: font_regular),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                widget.from.toString() == "result"
                                    ? GestureDetector(
                                        onTap: () {

                                          Get.to(() => pdfviewer_new(
                                            pdf_url: controller.assignmentDetails['submitted_file'].toString(),
                                            title: 'Assignment Submission',
                                            id: '',
                                          ));

                                          // if(controller.assignmentDetails['type'].toString() == "pdf"){
                                          //
                                          // } else if(controller.assignmentDetails['type'].toString() == "image")  {
                                          //   Get.to(() => image_viewer(
                                          //     url: controller.assignmentDetails['submitted_file'].toString(),
                                          //     title: 'Assignment Submission',
                                          //   ));
                                          // }

                                        },
                                        child: Container(
                                          decoration: BoxDecoration(color: primaryColor.withValues(alpha: 0.06), borderRadius: BorderRadius.circular(5)),
                                          padding: EdgeInsets.all(13),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Image.asset("assets/icons/pdf.png", width: Get.width / 8),
                                              const SizedBox(width: 10),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Text('Submission date', style: TextStyle(color: textblackColor.withValues(alpha: 0.5), fontSize: Get.width / 28, fontFamily: font_regular)),
                                                    Text(controller.assignmentDetails['submission_date'].toString(), style: TextStyle(color: Colors.black, fontSize: Get.width / 26, fontFamily: font_regular)),
                                                  ],
                                                ),
                                              ),
                                              Text('View File', style: TextStyle(color: primaryColor, fontSize: Get.width / 28, fontFamily: font_bold)),
                                            ],
                                          ),
                                        ),
                                      )
                                    : Row(
                                        children: [
                                          Text('Due date', style: TextStyle(color: textblackColor.withValues(alpha: 0.5), fontSize: Get.width / 28, fontFamily: font_regular)),
                                          Container(width: 15, margin: const EdgeInsets.symmetric(horizontal: 15), child: const Divider(thickness: 1, color: Color(0xFFBABABA), height: 2)),
                                          Text(controller.assignmentDetails['due_date'].toString(), style: TextStyle(color: Colors.black, fontSize: Get.width / 30, fontFamily: font_regular)),
                                        ],
                                      ),
                                const SizedBox(height: 20),
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(color: const Color(0xFFF4F4F4), borderRadius: BorderRadius.circular(4)),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text("Instruction", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 25, color: Colors.black)),
                                      const SizedBox(height: 10),
                                      SizedBox(
                                        width: Get.width,
                                        child: Html(data: controller.assignmentDetails['instructions'].toString()),
                                      ),
                                    ],
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 20),
                                    const Text("Assignment  File", style: TextStyle(color: Colors.black)),
                                    const SizedBox(height: 15),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: GestureDetector(
                                            onTap: () {
                                              if (controller.assignmentDetails['type'].toString() == "pdf") {
                                                Get.to(pdfviewer_new(pdf_url: controller.assignmentDetails['file'].toString(), title: controller.assignmentDetails['title'].toString(), id: ''));
                                              } else {
                                                Get.to(image_viewer(url: controller.assignmentDetails['file'].toString(), title: controller.assignmentDetails['title'].toString()));
                                              }
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                                              decoration: BoxDecoration(color: primaryColor, border: Border.all(color: primaryColor.withValues(alpha: 0.3)), borderRadius: BorderRadius.circular(4)),
                                              child: Text("View File", style: TextStyle(fontWeight: FontWeight.w600, color: textwhiteColor), textAlign: TextAlign.center),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                        Expanded(
                                          child: GestureDetector(
                                            onTap: () {
                                              controller.downloadFile(controller.assignmentDetails['file'].toString(), controller.assignmentDetails['title'].toString());
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                                              decoration: BoxDecoration(color: Colors.white, border: Border.all(color: primaryColor.withValues(alpha: 0.3)), borderRadius: BorderRadius.circular(4)),
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                children: [
                                                  controller.isDownloading
                                                      ? SizedBox(
                                                          height: 25,
                                                          width: 25,
                                                          child: CircularStepProgressIndicator(
                                                              stepSize: 2,
                                                              selectedColor: primaryColor,
                                                              width: 25,
                                                              height: 25,
                                                              unselectedColor: Colors.grey,
                                                              currentStep: controller.downloadProgress.toInt(),
                                                              totalSteps: 100),
                                                        )
                                                      : controller.isDownloadCompleted
                                                          ? Icon(Icons.download_done_rounded, color: primaryColor, size: Get.width / 25)
                                                          : Image.asset("assets/icons/download.png", width: Get.width / 25),
                                                  const SizedBox(width: 5),
                                                  const Expanded(child: Text("Download File", style: TextStyle(fontWeight: FontWeight.w600), textAlign: TextAlign.center)),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                if (widget.from.toString() != "result")
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(height: 30),
                                      const Text("Upload your Assignment", style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600)),
                                      const SizedBox(height: 15),
                                      controller.assignmentfile != null
                                          ? Container(
                                              decoration: BoxDecoration(
                                                border: Border.all(color: textblackColor.withValues(alpha: 0.5), width: 0.5),
                                                borderRadius: BorderRadius.circular(10),
                                              ),
                                              padding: EdgeInsets.all(10),
                                              child: Row(
                                                children: [
                                                  controller.assignmentfile!.path.toString().split('/').last.toString().split('.')[1].toString() == "pdf"
                                                      ? Image.asset('assets/icons/pdf.png', width: Get.width / 10)
                                                      : Container(
                                                          width: Get.width / 5,
                                                          height: Get.width / 5,
                                                          child: ClipRRect(
                                                            borderRadius: BorderRadius.circular(10),
                                                            child: Image.file(File(controller.assignmentfile!.path.toString()), width: Get.width / 10, fit: BoxFit.cover),
                                                          ),
                                                        ),
                                                  SizedBox(width: 10),
                                                  Expanded(child: Text(controller.assignmentfile!.path.toString().split('/').last.toString())),
                                                  SizedBox(width: 10),
                                                  IconButton(
                                                    onPressed: () {
                                                      setState(() => controller.assignmentfile = null);
                                                    },
                                                    icon: Icon(Icons.close),
                                                  ),
                                                ],
                                              ),
                                            )
                                          : GestureDetector(
                                              onTap: () {
                                                controller.getFromFile();
                                              },
                                              child: DottedBorder(
                                                strokeWidth: 1.5,
                                                dashPattern: const [6, 4],
                                                borderType: BorderType.RRect,
                                                radius: const Radius.circular(4),
                                                child: Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                                                  decoration: BoxDecoration(color: const Color(0xFFEAEAF1), borderRadius: BorderRadius.circular(4)),
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                    children: [
                                                      const SizedBox(width: 15),
                                                      Image.asset("assets/icons/upload.png", width: Get.width / 18),
                                                      const SizedBox(width: 15),
                                                      const Expanded(child: Text("Upload File", style: TextStyle(fontWeight: FontWeight.w600), textAlign: TextAlign.center)),
                                                      const SizedBox(width: 15),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                      const SizedBox(height: 8),
                                      Container(alignment: Alignment.centerRight, child: Text("Maximum Size 5 Mb", style: TextStyle(color: Color(0xFF7B7B7B), fontWeight: FontWeight.w600, fontSize: Get.width / 30)))
                                    ],
                                  ),
                                const SizedBox(height: 100),
                              ],
                            ),
                          ),
                        ),
                        if (widget.from.toString() != "result")
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              color: Colors.white.withOpacity(0.2),
                              child: common_button2(
                                  onPressed: () {
                                    if (controller.assignmentfile != null) {
                                      controller.uploadAssignment(widget.assignment_id.toString());
                                      // controller.showSubmitSuccess(context);
                                    } else {
                                      toast_warning("Select a file to submit assignment ");
                                    }
                                  },
                                  borderRadius: BorderRadius.circular(10),
                                  bg: primaryColor,
                                  child: controller.isAssignmentUpload
                                      ? Center(child: SizedBox(width: 25, height: 25, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2)))
                                      : Text("Submit Now", style: TextStyle(fontWeight: FontWeight.w600, fontFamily: font_regular, color: Colors.white))),
                            ),
                          )
                      ],
                    ),
            )
          ],
        ),
      );
    });
  }
}
