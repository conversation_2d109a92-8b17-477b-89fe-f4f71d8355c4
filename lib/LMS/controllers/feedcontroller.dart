import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../api/main_apis.dart';


class Feedcontroller extends GetxController{


  List feedsData = [];
  // List feeddata = [
  //   {
  //     "feed": [
  //       {
  //         "id": "5",
  //         "title": "Explore the Latest in Education",
  //         "content": "<p>Discover Insights, Tips, and Trends to Boost Your Learning Journey</p>",
  //         "feed_category_id": "5",
  //         "course_id": "0",
  //         "image": "assets/temp_images/feed1.png",
  //         "is_liked": 1,
  //         "likes": 51
  //       },
  //       {
  //         "id": "5",
  //         "title": "Explore the Latest in Education",
  //         "content": "<p>Discover Insights, Tips, and Trends to Boost Your Learning Journey</p>",
  //         "feed_category_id": "5",
  //         "course_id": "0",
  //         "image": "assets/temp_images/feed2.png",
  //         "is_liked": 0,
  //         "likes": 45
  //       },
  //     ],
  //     "stories": [
  //       {
  //         "id": "1",
  //         "title": "Story 1",
  //         "course_id": "0",
  //         "date": "0000-00-00",
  //         "image": "https://project.trogon.info/british_academy/file/?item=dXBsb2Fkcy9mZWVkLzIwMjQwNy8xNzIxOTg2MDYwXzJiNzk1YmVkZmQ3ZmE2NWE3ODgzLnBuZw%3D%3D&expiry=1731651943",
  //         "status": "1"
  //       },
  //       {
  //         "id": "2",
  //         "title": "Story 2",
  //         "course_id": "0",
  //         "date": "2024-07-26",
  //         "image": "https://project.trogon.info/british_academy/file/?item=dXBsb2Fkcy9zdG9yaWVzLzIwMjQwNy8xNzIxOTg2MDk4X2RkY2M0YWM2MjJlNDc3YjI3NWMzLnBuZw%3D%3D&expiry=1731651943",
  //         "status": "1"
  //       },
  //       {
  //         "id": "3",
  //         "title": "Story 3",
  //         "course_id": "0",
  //         "date": "2024-07-26",
  //         "image": "https://project.trogon.info/british_academy/file/?item=dXBsb2Fkcy9zdG9yaWVzLzIwMjQwNy8xNzIxOTg2MTM0XzA3YTZlMzhiOTMzZTAxM2UyOThmLnBuZw%3D%3D&expiry=1731651943",
  //         "status": "1"
  //       }
  //     ]
  //   }
  // ];
  bool isFeedLoading = true;


  get_feed(isInitial)async{
    if(isInitial) {
      isFeedLoading = true;
    }
    var data = await ApiBaseHandler.feeds();
      feedsData = [data['data']];
      isFeedLoading = false;
      update();
  }

  String result = '';

  like_feed(feed_id)async{
    print("called-------------");

    // updateLoading = true;

    // var data = await ApiBasehandler.like_feed(feed_id);
    //
    // result = data[0]['status'].toString();
    // if(data['status'].toString() == "1"){
    //   // GetStorage().write('course_id', cid.toString());
    //   // toast_success('Course Updated Successfully');
    //   // Get.offAll(()=>dashboard());
    // }


    // updateLoading = false;
    update();

  }



}