
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../components/constants.dart';

class FileInfoCard extends StatefulWidget {
  String title,label_1,label_2,label_3,icon;
  Color color,blink_color,blink_text_color;
  FileInfoCard({required this.blink_text_color,required this.blink_color,required this.title,required this.label_1,required this.label_2,required this.label_3,required this.color,required this.icon});


  @override
  State<FileInfoCard> createState() => _FileInfoCardState();
}

class _FileInfoCardState extends State<FileInfoCard>  with SingleTickerProviderStateMixin {
  late final AnimationController _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 500),
      animationBehavior: AnimationBehavior.preserve,
      lowerBound: 0.6,
       upperBound: 1
  );


  @override
  void initState() {
    _animationController.repeat();
    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return FadeTransition(
      opacity: _animationController,
      child: Container(
        padding: EdgeInsets.all(defaultPadding),
        decoration: BoxDecoration(
          color: widget.blink_color,
          borderRadius: const BorderRadius.all(Radius.circular(10)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [

            Container(
              child: Row(
                children: [

                  Container(
                    padding: EdgeInsets.all(defaultPadding * 0.60),
                    height: 45,
                    width: 45,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                    ),
                    child: Image.asset(
                      widget.icon,
                      color: widget.blink_color == Colors.yellow.withValues(alpha: 0.8) ? Colors.black : Colors.white,
                    ),
                  ),
                  SizedBox(width: 10,),
                  Container(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        if(widget.label_1!="")Container(
                          // color: Colors.yellow,
                          width: size.width/4.95,
                          child: Text(widget.label_1.toString(), style: Theme.of(context).textTheme.bodySmall!.copyWith(color: widget.blink_color == Colors.yellow.withValues(alpha: 0.8) ? Colors.black : Colors.white,overflow: TextOverflow.ellipsis)),
                        ),
                        if(widget.label_2!="")SizedBox(height: 5,),
                        if(widget.label_2!="")Container(
                          // color: Colors.yellow,
                          width: size.width/4.95,
                          child: Text(widget.label_2.toString(), style: Theme.of(context).textTheme.bodySmall!.copyWith(color: widget.blink_text_color,fontFamily: '',overflow: TextOverflow.ellipsis),maxLines: 2,),
                        ),
                        if(widget.label_3!="")SizedBox(height: 5,),
                        if(widget.label_3!="")Container(
                          // color: Colors.yellow,
                          width: size.width/4.95,
                          child: Text(widget.label_3.toString(), style: Theme.of(context).textTheme.bodySmall!.copyWith(color: widget.blink_text_color,fontFamily: '',overflow: TextOverflow.ellipsis),maxLines: 2,),
                        ),
                        // SizedBox(height: 0,),
                      ],
                    ),
                  ),

                ],
              ),
            ),

            SizedBox(height: 10,),

            Container(
              // color: Colors.yellow,
              child: Text(
                widget.title.toString(),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: widget.blink_text_color),
              ),
            ),



          ],
        ),
      ),
    );
  }
}


class FileInfoCard2 extends StatefulWidget {
  String title,label_1,label_2,label_3,icon;
  Color color,blink_color,blink_text_color;
  FileInfoCard2({required this.blink_text_color,required this.blink_color,required this.title,required this.label_1,required this.label_2,required this.label_3,required this.color,required this.icon});



  @override
  State<FileInfoCard2> createState() => _FileInfoCard2State();
}

class _FileInfoCard2State extends State<FileInfoCard2>  with SingleTickerProviderStateMixin {


  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Container(
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: widget.blink_color,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [

          Container(
            child: Row(
              children: [

                Container(
                  padding: EdgeInsets.all(defaultPadding * 0.60),
                  height: 45,
                  width: 45,
                  decoration: BoxDecoration(
                    color: widget.color.withValues(alpha: 0.1),
                    borderRadius: const BorderRadius.all(Radius.circular(10)),
                  ),
                  child: Image.asset(
                    widget.icon,
                    color: widget.color,
                  ),
                ),
                SizedBox(width: 10,),
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      if(widget.label_1!="")Container(
                        // color: Colors.yellow,
                        width: widget.label_1 == "Last Payment" ? size.width/1.5 : size.width/4.95,
                        child: Text(widget.label_1.toString(), style: Theme.of(context).textTheme.bodySmall!.copyWith(color: widget.blink_text_color,overflow: TextOverflow.ellipsis),maxLines: 2,),
                      ),
                      if(widget.label_2!="")SizedBox(height: 5,),
                      if(widget.label_2!="")Container(
                        // color: Colors.yellow,
                        width: widget.label_1 == "Last Payment" ? size.width/1.5 : size.width/4.95,
                        child: Text(widget.label_2.toString(), style: Theme.of(context).textTheme.bodySmall!.copyWith(color: widget.blink_text_color,fontFamily: '',overflow: TextOverflow.ellipsis),maxLines: 2,),
                      ),

                      if(widget.label_3!="")SizedBox(height: 5,),
                      if(widget.label_3!="")Container(
                        // color: Colors.yellow,
                        width: widget.label_1 == "Last Payment" ? size.width/1.5 : size.width/4.95,
                        child: Text(widget.label_3.toString(), style: Theme.of(context).textTheme.bodySmall!.copyWith(color: widget.blink_text_color,fontFamily: '',overflow: TextOverflow.ellipsis),maxLines: 2,),
                      ),

                      // SizedBox(height: 0,),
                    ],
                  ),
                ),

              ],
            ),
          ),

          SizedBox(height: 10,),

          Container(
            // color: Colors.yellow,
            child: Text(
              widget.title.toString(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: widget.blink_text_color),
            ),
          ),



        ],
      ),
    );
  }
}

