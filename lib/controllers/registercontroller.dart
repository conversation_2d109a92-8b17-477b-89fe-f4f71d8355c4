import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../LMS/api/auth_apis.dart';
import '../components/constants.dart';
import '../components/utils.dart';
import '../views/auth/login.dart';
import '../views/auth/otp.dart';

class Registercontroller extends GetxController {
  TextEditingController nameclr = TextEditingController();
  TextEditingController phoneclr = TextEditingController();

  bool isLoading = false, is_email = false;

  var countryFlag = "🇮🇳";
  String? countrycode_number = "91", countrycode_text = "IN", sel_gender = "male";
  get_register() async {
    isLoading = true;
    update();

    if (nameclr.text.isEmpty) {
      toast_warning('Name field is empty');
      isLoading = false;
      return;
    } else if (phoneclr.text.isEmpty) {
      toast_warning('Phone field is empty');
      isLoading = false;
      return;
    } else {
      var data = await AuthApis.register(nameclr.text.toString(), phoneclr.text.toString(), '+${countrycode_number.toString()}');
      log(data.toString());
      if (data['status'].toString() == "1" || data['status'].toString() == "true") {
        Get.to(() => otp(phone: phoneclr.text.toString(), code: "+${countrycode_number.toString()}", from: 'register'));
        toast_success(data['message'].toString());
      } else {
        toast_error(data['message'].toString());
        Get.offAll(() => Login());
      }
    }
    isLoading = false;
    update();
  }
}
