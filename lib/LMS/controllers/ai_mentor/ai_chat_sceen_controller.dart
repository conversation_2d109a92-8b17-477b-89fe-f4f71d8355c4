import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:get/get.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;

import '../../api/ai_chat_apis.dart';

class AiChatScreenController extends GetxController {

  bool isPageLoading = false;
  ScrollController chatScrollController = ScrollController();
  TextEditingController msgTextController = TextEditingController();
  List chat = [];
  final stt.SpeechToText _speech = stt.SpeechToText();
  final FlutterTts flutterTts = FlutterTts();
  String responseText = "";
  String spokenText = "";

  bool animate = false;
  bool isListening = false;

  /// Start listening to user speech
  Future<void> startListening() async {
    bool available = await _speech.initialize();
    if (available) {
      isListening = true;
      animate = true;
      update();
      _speech.listen(onResult: (result) {
        spokenText = result.recognizedWords;
        update();
      });

      log(isListening.toString());
      log("isListening.toString()");
    }
  }

  /// Stop listening and generate AI response
  Future<void> stopListening() async {
    _speech.stop();
    isListening = false;
    animate = false;
    sendMessage("you", spokenText, "text", "");
    update();
  }

  Future<void> forceStop() async {
    _speech.stop();
    isListening = false;
    animate = false;
    update();
  }

  /// Generate AI response based on predefined chatbot logic
  // Future<void> generateResponse(String userMessage) async {
  //   String response = "Sorry, I don't understand.";
  //   chatbotResponses.forEach((key, value) {
  //     if (userMessage.contains(key)) {
  //       response = value;
  //       sendMessage("AI", value.toString(), "text", "");
  //       msgTextController.text.toString();
  //       update();
  //     }
  //   });
  //
  //   responseText = response;
  //   update();
  //   await speak(response);
  // }

  /// Convert AI response text to speech
  Future<void> speak(String text) async {
    try {
      await flutterTts.speak(text);
      update();
    } catch (e) {
      debugPrint("TTS Error: $e");
    }
  }


  bool isGenerating = false;

  sendMessage(from, message, type, audio) async {

    isGenerating = true;
    update();

    var data = await AiChatApis.ai_send_message(message.toString());
    chat = data['data']['chat_history'];

    isGenerating = false;
    spokenText = "";
    msgTextController.text = "";
    update();

    scrollToBottom();

  }


  bool isMessageLoading = true;
  void getMessages() async {

    isMessageLoading = true;

    var data = await AiChatApis.ai_chat_messages();
    chat = data['data'];


    isMessageLoading = false;
    update();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollToBottom();
    });
  }
  void scrollToBottom() {
    if (chatScrollController.hasClients) {
      chatScrollController.animateTo(
        chatScrollController.position.maxScrollExtent + 200, duration: Duration(microseconds: 500), curve: Curves.easeIn,
      );
    }
  }
}
