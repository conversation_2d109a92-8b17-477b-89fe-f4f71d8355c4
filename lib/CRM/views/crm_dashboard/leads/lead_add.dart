import 'package:country_list_pick/country_list_pick.dart';
import 'package:edutalim/CRM/api/apis.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';


// CRE Lead Add and Edit page ----------------------

class lead_add extends StatefulWidget {
  String action,member_id;
  List data = [];
  lead_add({required this.action, required this.data,required this.member_id});

  @override
  State<lead_add> createState() => _update_lead_statusState();
}

class _update_lead_statusState extends State<lead_add> {

  TextEditingController remark = TextEditingController();
  TextEditingController follow_date = TextEditingController();
  TextEditingController name = TextEditingController();
  TextEditingController email = TextEditingController();
  TextEditingController place = TextEditingController();
  TextEditingController age = TextEditingController();
  TextEditingController qualification = TextEditingController();
  TextEditingController address = TextEditingController();
  TextEditingController phone = TextEditingController();
  TextEditingController whatsapp_no = TextEditingController();
  TextEditingController careof = TextEditingController();


  String countrycode = "91",countrycode_text = "IN",sel_gender = "male",sel_country = "",sel_leadstatus = '',sel_lsource = '',sel_date = '',sel_telecaller = '',
      sel_courses = '',sel_interest_status = '', sel_branches = '';
  bool addbtn_press = false;

  get_add_edit_lead()async{

    setState(() {
      addbtn_press = true;
    });

    var data = await CRMApiBaseHandler.fet_add_edit_lead(
        name.text.toString(),
        countrycode.toString(),
        phone.text.toString(),
        whatsapp_no.text.toString(),
        email.text.toString(),
        sel_telecaller.toString(),
        sel_interest_status.toString(),
        sel_leadstatus.toString(),
        sel_lsource.toString(),
        follow_date.text ?? '',
        remark.text.toString(),
        sel_cat.toString(),
        sel_subcat.toString(),
        sel_course.toString(),
        widget.data.isEmpty ? '' : widget.data[0]['id'].toString(),
        widget.action.toString(),
    );


    setState(() {
      addbtn_press = false;
    });

    Get.back();

    if(data[0]['status'].toString() == "true")
      {
        toast_success(data[0]['message'].toString());
        Get.back();
      }else{
      toast_error(data[0]['message'].toString());
    }

    print('-----------called--');
    print(data.toString());



  }

  List telecaller = [];
  bool istelecallerLoading = true;


  @override
  void initState() {
    super.initState();
    get_leaad_form('');
    if(widget.action.toString() == "edit" || widget.action.toString() == "update")
      {
        setState(() {
          name.text = widget.data[0]['title'] ?? '';
          phone.text = widget.data[0]['phone'] ?? '';
          countrycode = widget.data[0]['code'] ?? '';
          countrycode_text = widget.data[0]['code_text'] ?? '';
          whatsapp_no.text = widget.data[0]['whatsapp'] ?? '';
          email.text = widget.data[0]['email'] ?? '';
          sel_gender = widget.data[0]['gender'] ?? '';
          age.text = widget.data[0]['age'] ?? '';
          place.text = widget.data[0]['place'] ?? '';
          address.text = widget.data[0]['address'] ?? '';
          qualification.text = widget.data[0]['qualification'] ?? '';
          sel_country = widget.data[0]['country_id'] ?? '';
          follow_date.text = widget.data[0]['followup_date'] == 'null' || widget.data[0]['followup_date'] == null ? '' :  widget.data[0]['followup_date'];
          sel_leadstatus = widget.data[0]['lead_status_id'] ?? '';
          sel_lsource = widget.data[0]['lead_source_id'] ?? '';
          remark.text = widget.data[0]['remarks'] ?? '';
          sel_branches = widget.data[0]['branch_id'] ?? '';
          sel_courses = widget.data[0]['course_id'] ?? '';
          sel_interest_status = widget.data[0]['interest_status'] ?? '';
          sel_telecaller = widget.data[0]['telecaller_id'] ?? '';
        });
      }

    if(widget.action.toString() == "add")
      {
        if(GetStorage().read('user_role').toString() != "1")
          {
            sel_telecaller = GetStorage().read('user_id').toString();
          }
      }

  }

  Map formdata = {};
  bool formload = true;
  String? sel_cat,sel_subcat,sel_course;
  List sel_subcats = [],sel_subcats_ids = [];
  bool fromsubcatloading = false;
  get_leaad_form(category_id)async{

    if(category_id != "")
      {
        fromsubcatloading = true;
      }

    var data = await CRMApiBaseHandler.fet_leaad_form(category_id);
    print('------------------------------YY ----------');
    print(data.toString());

    setState(() {
      formdata = data['data'];
      formload = false;
      fromsubcatloading = false;
    });

    if(data['data']['subcategories'].isNotEmpty)
      {
        sel_subcats.add(data['data']['subcategories']);
      }

  }

  bool validateEmail(String email) {
    final RegExp emailRegExp = RegExp(r"[^@]+@[^@]+\.com");
    return emailRegExp.hasMatch(email);
  }

  String _errorMessage = "";


  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,

        appBar: AppBar(
          leading: IconButton(
            onPressed: (){
              Get.back();
            },
            icon: Container(
              padding: EdgeInsets.only(left: 20),
              child: Icon(Icons.arrow_back_ios,color: Colors.black54,),
            ),
          ),
          title: Text(widget.action == "update" ? "Update Followup" : widget.action == "edit" ? "Edit Lead" : "Add Lead",style: TextStyle(color: Colors.black,fontFamily: "poppins_regular",fontSize: 17),),
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          centerTitle: true,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: Color(0xffffffff),
              statusBarIconBrightness: Brightness.dark
          ),
        ),

        body:

        formload ?
        loader()
        :
        GestureDetector(
          onTap: (){
            FocusScope.of(context).unfocus();
          },
          child: Stack(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                decoration: BoxDecoration(
                  // image: DecorationImage(
                  //     fit: BoxFit.cover,
                  //     opacity: 0.05,
                  //     image: AssetImage('assets/logo/logo.png')
                  // ),
                ),
                child: SingleChildScrollView(
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [


                        SizedBox(height: 30,),


                        if(widget.action.toString() != "update")
                          Column(
                          children: [


                            // name
                            Container(
                              alignment: Alignment.centerLeft,
                              width: MediaQuery.of(context).size.width/1.2,
                              padding: EdgeInsets.only(bottom: 10),
                              child: Wrap(
                                children: [
                                  Text("Name",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                  Text(" *",style: TextStyle(color: Colors.red,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),

                                ],
                              ),
                            ),
                            Container(
                              // color: Colors.yellow,
                              width: MediaQuery.of(context).size.width/1.2,
                              // height: 40,
                              child: TextField(
                                onTap: (){
                                },
                                readOnly: false,
                                style: TextStyle(
                                    fontSize: 15.0,
                                    height: 1.5,
                                    color: Colors.black
                                ),
                                controller: name,
                                textCapitalization: TextCapitalization.words,
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  filled: true,
                                  hintStyle: TextStyle(color: const Color(0xFFA1A3AB),fontFamily: "poppins_regular"),
                                  hintText: "Name",
                                  fillColor: Colors.white70,
                                  contentPadding: const EdgeInsets.symmetric(vertical: 10.0,horizontal: 20),
                                ),
                              ),
                            ),
                            SizedBox(height: MediaQuery.of(context).size.height/30,),



                            // phone
                            Container(
                              alignment: Alignment.centerLeft,
                              width: MediaQuery.of(context).size.width/1.2,
                              padding: EdgeInsets.only(bottom: 10),
                              child: Wrap(
                                children: [
                                  Text("Phone",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                  Text(" *",style: TextStyle(color: Colors.red,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                ],
                              ),
                            ),
                            Container(
                              // padding: EdgeInsets.all(4),
                                width: MediaQuery.of(context).size.width/1.2,
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey,width: 0.5),
                                  borderRadius: BorderRadius.circular(10),
                                  // color: Color(0xffF6F7F9)
                                ),
                                // alignment: Alignment.centerRight,
                                child: Row(
                                  children: [
                                    Container(
                                      child: CountryListPick(
                                        initialSelection: '+'+countrycode.toString(),
                                        onChanged: (pickerc_code){
                                          countrycode = pickerc_code!.code.toString();
                                          print(pickerc_code);
                                        },
                                        theme: CountryTheme(
                                          isShowFlag: true,
                                          isShowTitle: false,
                                          isShowCode: true,
                                          isDownIcon: true,
                                          showEnglishName: true,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      // color: Colors.yellow,
                                      margin: EdgeInsets.only(bottom: 1),
                                      width: MediaQuery.of(context).size.width/2.1,
                                      child: TextField(
                                        keyboardType: TextInputType.number,
                                        controller: phone,
                                        maxLength: 10,
                                        style: TextStyle(color: Colors.black),
                                        decoration: InputDecoration(
                                          counterText: '',
                                            focusedBorder: UnderlineInputBorder(
                                              borderSide: BorderSide(color:  Colors.transparent),
                                            ),
                                            enabledBorder: UnderlineInputBorder(
                                              borderSide: BorderSide(color: Colors.transparent),
                                            ),
                                            border: OutlineInputBorder(
                                              borderRadius: BorderRadius.circular(10.0),
                                            ),
                                            // filled: true,
                                            hintStyle: TextStyle(color: const Color(0xFFA1A3AB),fontFamily: "poppins_regular",fontSize: MediaQuery.of(context).size.width/27),
                                            hintText: "Phone number",
                                            fillColor: Color(0xffF6F7F9)
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                            ),
                            SizedBox(height: MediaQuery.of(context).size.height/30,),


                            // whatsapp_no
                            Container(
                              alignment: Alignment.centerLeft,
                              width: MediaQuery.of(context).size.width/1.2,
                              padding: EdgeInsets.only(bottom: 10),
                              child: Text("Alternate Number",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                            ),
                            Container(
                              // color: Colors.yellow,
                              width: MediaQuery.of(context).size.width/1.2,
                              // height: 40,
                              child: TextField(
                                onTap: (){
                                },
                                keyboardType: TextInputType.number,
                                readOnly: false,
                                style: TextStyle(
                                    fontSize: 15.0,
                                    height: 1.5,
                                    color: Colors.black
                                ),
                                controller: whatsapp_no,
                                maxLength: 10,
                                decoration: InputDecoration(
                                  counterText: '',
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  filled: true,
                                  hintStyle: TextStyle(color: const Color(0xFFA1A3AB),fontFamily: "poppins_regular"),
                                  hintText: "WhatsApp",
                                  fillColor: Colors.white70,
                                  contentPadding: const EdgeInsets.symmetric(vertical: 10.0,horizontal: 20),
                                ),
                              ),
                            ),
                            SizedBox(height: MediaQuery.of(context).size.height/30,),


                            // email
                            Container(
                              alignment: Alignment.centerLeft,
                              width: MediaQuery.of(context).size.width/1.2,
                              padding: EdgeInsets.only(bottom: 10),
                              child: Text("Email",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                            ),
                            Container(
                              // color: Colors.yellow,
                              width: MediaQuery.of(context).size.width/1.2,
                              // height: 40,
                              child: TextField(
                                onTap: (){
                                },

                                onChanged: (text) {
                                  setState(() {
                                    _errorMessage = validateEmail(text) ? "" : "Invalid email format";
                                  });
                                },
                                readOnly: false,
                                style: TextStyle(
                                    fontSize: 15.0,
                                    height: 1.5,
                                    color: Colors.black
                                ),
                                controller: email,
                                keyboardType: TextInputType.emailAddress,
                                decoration: InputDecoration(
                                  errorText: email.text.isEmpty ? null : _errorMessage.isEmpty ? null : _errorMessage,
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  filled: true,
                                  hintStyle: TextStyle(color: const Color(0xFFA1A3AB),fontFamily: "poppins_regular"),
                                  hintText: "Email",
                                  fillColor: Colors.white70,
                                  contentPadding: const EdgeInsets.symmetric(vertical: 10.0,horizontal: 20),
                                ),
                              ),
                            ),
                            SizedBox(height: MediaQuery.of(context).size.height/30,),








                            Column(
                              children: [
                                Container(
                                  alignment: Alignment.centerLeft,
                                  width: MediaQuery.of(context).size.width/1.2,
                                  padding: EdgeInsets.only(bottom: 10),
                                  child: Text("Lead Source",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                ),
                                Container(
                                  width: MediaQuery.of(context).size.width / 1.2,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(
                                        color: Colors.black12,
                                        width: 1,
                                      )
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: ButtonTheme(
                                      alignedDropdown: true,
                                      child: DropdownButton<String>(
                                        value: sel_lsource.toString() == 'null' || sel_lsource.toString() == '' ? null : sel_lsource,
                                        iconSize: 30,
                                        // icon: (null),
                                        style: TextStyle(
                                          color: Colors.black54,
                                          fontSize: 14,
                                        ),
                                        hint: Text('--select lead source--'),
                                        onChanged: (newValue) async {
                                          setState(() {
                                            sel_lsource = newValue!;
                                          });
                                        },
                                        items: formdata['lead_source'].map<DropdownMenuItem<String>>((item) {
                                          return  DropdownMenuItem(
                                            child:  Container(
                                              width: MediaQuery.of(context).size.width / 1.6,
                                              child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                            ),
                                            value: item['id'].toString(),
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: MediaQuery.of(context).size.height/30,),
                              ],
                            ),



                          ],
                        ),


                        // ------------------------------------------------------------

                        // lead status
                        Container(
                          alignment: Alignment.centerLeft,
                          width: MediaQuery.of(context).size.width/1.2,
                          padding: EdgeInsets.only(bottom: 10),
                          child: Text("Lead Status",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width / 1.2,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.0),
                              border: Border.all(
                                color: Colors.black12,
                                width: 1,
                              )
                          ),
                          child: DropdownButtonHideUnderline(
                            child: ButtonTheme(
                              alignedDropdown: true,
                              child: DropdownButton<String>(
                                value: sel_leadstatus.toString() == 'null' || sel_leadstatus.toString() == '' ? null : sel_leadstatus.toString(),
                                iconSize: 30,
                                // icon: (null),
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontSize: 14,
                                ),
                                hint: Text('--select status--'),
                                onChanged: (newValue) async {
                                  setState(() {
                                    sel_leadstatus = newValue!;
                                  });
                                },
                                items: formdata['lead_status'].map<DropdownMenuItem<String>>((item) {
                                  return  DropdownMenuItem(
                                    child:  Container(
                                      width: MediaQuery.of(context).size.width / 1.6,
                                      child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                    ),
                                    value: item['id'].toString(),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height/30,),


                        if(widget.action.toString() != "update")
                        Column(
                          children: [



                            if(GetStorage().read('user_role').toString() == "1")
                              Column(
                                children: [
                                  Container(
                                    alignment: Alignment.centerLeft,
                                    width: MediaQuery.of(context).size.width/1.2,
                                    padding: EdgeInsets.only(bottom: 10),
                                    child: Row(
                                      children: [
                                        // Text("Academic Counselor",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                        Text("Telecaller",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                        Text(" *",style: TextStyle(color: Colors.red,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    width: MediaQuery.of(context).size.width / 1.2,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10.0),
                                        border: Border.all(
                                          color: Colors.black12,
                                          width: 1,
                                        )
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: ButtonTheme(
                                        alignedDropdown: true,
                                        child: DropdownButton<String>(
                                          value: sel_telecaller.toString() == 'null' || sel_telecaller.toString() == '' ? null : sel_telecaller.toString(),
                                          iconSize: 30,
                                          // icon: (null),
                                          style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 14,
                                          ),
                                          hint: Text('--select telecaller --'),
                                          onChanged: (newValue) async {
                                            setState(() {
                                              sel_telecaller = newValue!;
                                            });
                                          },
                                          items: formdata['telecallers'].map<DropdownMenuItem<String>>((item) {
                                            return  DropdownMenuItem(
                                              child:  Container(
                                                width: MediaQuery.of(context).size.width / 1.6,
                                                child: Text(item['name'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                              ),
                                              value: item['id'].toString(),
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: MediaQuery.of(context).size.height/30,),
                                ],
                              ),


                          ],
                        ),






                        // followup
                        Container(
                          alignment: Alignment.centerLeft,
                          width: MediaQuery.of(context).size.width/1.2,
                          padding: EdgeInsets.only(bottom: 10),
                          child: Text("Next Follow Up Date",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                        ),
                        Container(
                          // color: Colors.yellow,
                          width: MediaQuery.of(context).size.width/1.2,
                          // height: 40,
                          child: TextField(
                            onTap: (){
                              print('helooooo.....');
                              _showDatePicker(context);
                            },
                            readOnly: true,
                            style: TextStyle(
                                fontSize: 15.0,
                                height: 1.5,
                                color: Colors.black
                            ),
                            controller: follow_date,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              filled: true,
                              hintStyle: TextStyle(color: const Color(0xFFA1A3AB),fontFamily: "poppins_regular"),
                              hintText: "Next Follow Up",
                              fillColor: Colors.white70,
                              contentPadding: const EdgeInsets.symmetric(vertical: 10.0,horizontal: 20),
                            ),
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height/30,),


                        if(widget.action.toString() == "update")
                        // address
                        Column(
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              width: MediaQuery.of(context).size.width/1.2,
                              padding: EdgeInsets.only(bottom: 10),
                              child: Text("Remark"
                                  "",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                            ),
                            Container(
                              width: MediaQuery.of(context).size.width/1.2,
                              // height: 40,
                              child: TextField(
                                keyboardType: TextInputType.multiline,
                                minLines: 5,//Normal textInputField will be displayed
                                maxLines: 5,// when user presses enter it will adapt to it
                                style: TextStyle(
                                    fontSize: 15.0,
                                    height: 1.5,
                                    color: Colors.black
                                ),
                                controller: remark,
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black26,width: 0.5),
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  filled: true,
                                  hintStyle: TextStyle(color: const Color(0xFFA1A3AB),fontFamily: "poppins_regular"),
                                  hintText: "Remark",
                                  fillColor: Colors.white70,
                                  contentPadding: const EdgeInsets.symmetric(vertical: 10.0,horizontal: 20),
                                ),
                              ),
                            ),
                            SizedBox(height: MediaQuery.of(context).size.height/30,),
                          ],
                        ),





                        if(widget.action.toString() != "update")
                        Column(
                          children: [
                            Divider(),
                            SizedBox(height: 10,),

                            // category
                            Column(
                              children: [
                                Container(
                                  alignment: Alignment.centerLeft,
                                  width: MediaQuery.of(context).size.width/1.2,
                                  padding: EdgeInsets.only(bottom: 10),
                                  child: Row(
                                    children: [
                                      // Text("Academic Counselor",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                      Text("Category",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                      Text(" *",style: TextStyle(color: Colors.red,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                    ],
                                  ),
                                ),
                                Container(
                                  width: MediaQuery.of(context).size.width / 1.2,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(
                                        color: Colors.black12,
                                        width: 1,
                                      )
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: ButtonTheme(
                                      alignedDropdown: true,
                                      child: DropdownButton<String>(
                                        value: sel_cat.toString() == 'null' || sel_cat.toString() == '' ? null : sel_cat.toString(),
                                        iconSize: 30,
                                        // icon: (null),
                                        style: TextStyle(
                                          color: Colors.black54,
                                          fontSize: 14,
                                        ),
                                        hint: Text('--select category --'),
                                        onChanged: (newValue) async {
                                          setState(() {
                                            sel_cat = newValue!;
                                            sel_subcats = [];
                                            sel_subcats_ids = [];
                                          });
                                          get_leaad_form(newValue.toString());
                                        },
                                        items: formdata['categories'].map<DropdownMenuItem<String>>((item) {
                                          return  DropdownMenuItem(
                                            child:  Container(
                                              width: MediaQuery.of(context).size.width / 1.6,
                                              child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                            ),
                                            value: item['id'].toString(),
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            SizedBox(height: 10,),


                            if(sel_subcats.isNotEmpty)
                            // subcats
                              Container(
                                child: ListView.builder(
                                  physics: ScrollPhysics(),
                                  itemCount: sel_subcats.length,
                                  shrinkWrap: true,
                                  itemBuilder: (context, index) {
                                    return Container(
                                      child: Column(
                                        children: [
                                          Container(
                                            alignment: Alignment.centerLeft,
                                            width: MediaQuery.of(context).size.width/1.2,
                                            padding: EdgeInsets.only(bottom: 10),
                                            child: Row(
                                              children: [
                                                // Text("Academic Counselor",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                                Text("Subcategory",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                                // Text(" *",style: TextStyle(color: Colors.red,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                              ],
                                            ),
                                          ),
                                          Container(
                                            width: MediaQuery.of(context).size.width / 1.2,
                                            decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(10.0),
                                                border: Border.all(
                                                  color: Colors.black12,
                                                  width: 1,
                                                )
                                            ),
                                            child: DropdownButtonHideUnderline(
                                              child: ButtonTheme(
                                                alignedDropdown: true,
                                                child: DropdownButton<String>(
                                                  value: sel_subcats_ids.isEmpty ? null : sel_subcats_ids[index].toString(),
                                                  iconSize: 30,
                                                  // icon: (null),
                                                  style: TextStyle(
                                                    color: Colors.black54,
                                                    fontSize: 14,
                                                  ),
                                                  hint: Text('--select subcategory --'),
                                                  onChanged: (newValue) async {

                                                    // if(index == sel_subcats_ids.length-1)

                                                    setState(() {
                                                      sel_subcats_ids.add(newValue.toString());
                                                    });


                                                  },
                                                  items: formdata['subcategories'].map<DropdownMenuItem<String>>((item) {
                                                    return  DropdownMenuItem(
                                                      child:  Container(
                                                        width: MediaQuery.of(context).size.width / 1.6,
                                                        child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                                      ),
                                                      value: item['id'].toString(),
                                                    );
                                                  }).toList(),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),



                            Column(
                              children: [
                                Container(
                                  alignment: Alignment.centerLeft,
                                  width: MediaQuery.of(context).size.width/1.2,
                                  padding: EdgeInsets.only(bottom: 10),
                                  child: Row(
                                    children: [
                                      // Text("Academic Counselor",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                      Text("Course",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                      Text(" *",style: TextStyle(color: Colors.red,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: 12),),
                                    ],
                                  ),
                                ),
                                Container(
                                  width: MediaQuery.of(context).size.width / 1.2,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10.0),
                                      border: Border.all(
                                        color: Colors.black12,
                                        width: 1,
                                      )
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: ButtonTheme(
                                      alignedDropdown: true,
                                      child: DropdownButton<String>(
                                        value: sel_course.toString() == 'null' || sel_course.toString() == '' ? null : sel_course.toString(),
                                        iconSize: 30,
                                        // icon: (null),
                                        style: TextStyle(
                                          color: Colors.black54,
                                          fontSize: 14,
                                        ),
                                        hint: Text('--select category --'),
                                        onChanged: (newValue) async {
                                          setState(() {
                                            sel_course = newValue!;
                                          });
                                          get_leaad_form(newValue.toString());
                                        },
                                        items: formdata['courses'].map<DropdownMenuItem<String>>((item) {
                                          return  DropdownMenuItem(
                                            child:  Container(
                                              width: MediaQuery.of(context).size.width / 1.6,
                                              child: Text(item['title'].toString(),style: TextStyle(fontSize: MediaQuery.of(context).size.width/27),maxLines: 2,),
                                            ),
                                            value: item['id'].toString(),
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),


                            SizedBox(height: 10,),
                            Divider(),
                            SizedBox(height: 20,),
                          ],
                        ),





                        Container(
                          child: cust_elevatedbutton(
                            width: Get.width/1.2,
                            onPressed: () {
                              if(widget.action.toString() == "update") {
                                if (remark.text.isEmpty) {
                                  toast_info('Remarks is Empty');
                                }
                                else{
                                  get_add_edit_lead();
                                }
                              }
                              else{
                                if (name.text.isEmpty) {
                                  toast_info('Name is Empty');
                                }
                                else if (phone.text.isEmpty) {
                                  toast_info('Phone number is Empty');
                                }
                               else if (GetStorage().read('user_role').toString() == "1" && sel_telecaller == null) {
                                  toast_info('Select telecaller');
                                }
                                else{
                                  get_add_edit_lead();
                                }
                              }
                              // get_add_edit_lead();
                            },
                            borderRadius: BorderRadius.circular(10),
                            height: 50,
                            gradient: LinearGradient(
                              colors: [primaryColor,secondaryColor,],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            child: addbtn_press ?
                            Container(
                              height: 15,
                              width: 15,
                              child: CircularProgressIndicator(color: Colors.white,),
                            )
                                :
                            Text(
                              widget.action.toString() == "update" ? "Update Followup" : widget.action.toString() == "edit" ? "Update Lead" : "Add Lead",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: Colors.white,
                                  fontFamily: "poppins_regular"
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height/30,),

                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        )
    );
  }


  DateTime? _next_followup;
  void _showDatePicker(ctx) {
    follow_date.text = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
    setState(() {
      // sel_date = DateTime.now().toString();
      sel_date =DateFormat('dd-MM-yyyy').format(DateTime.now());
    });
    // showCupertinoModalPopup is a built-in function of the cupertino library
    showCupertinoModalPopup(
        context: ctx,
        builder: (_) => Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(30))
          ),
          height: 410,
          child: Column(
            children: [

              Container(
                width: Get.width,
                alignment: Alignment.center,
                padding: EdgeInsets.only(top: 20,bottom: 20),
                decoration: BoxDecoration(
                    color: Colors.lightBlueAccent,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(30))
                ),
                child: Text('Scrool to select',style: TextStyle(fontSize: 15,color: Colors.white,decoration: TextDecoration.none,fontFamily: ''),),
              ),

              SizedBox(
                height: 290,
                child: CupertinoDatePicker(
                    mode: CupertinoDatePickerMode.date,
                    initialDateTime: DateTime.now(),
                    onDateTimeChanged: (val) {
                      print("onchange ---sec");
                      print(sel_date.toString());
                      setState(() {
                        _next_followup = val;
                        follow_date.text = _next_followup!.day.toString()+"-"+_next_followup!.month.toString()+"-"+_next_followup!.year.toString();
                        sel_date =  _next_followup!.day.toString()+"-"+_next_followup!.month.toString()+"-"+_next_followup!.year.toString();

                      });
                    }),
              ),

              // Close the modal
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  CupertinoButton(
                    child: const Text('Cancel'),
                    onPressed: (){
                      setState(() {
                        follow_date.text = '';
                        sel_date = '';
                      });
                      Navigator.of(ctx).pop();
                    },
                  ),
                  CupertinoButton(
                    child: const Text('OK'),
                    onPressed: (){
                      Navigator.of(ctx).pop();
                      print(sel_date.toString());
                    },
                  ),
                ],
              ),

            ],
          ),
        ));
  }



}
