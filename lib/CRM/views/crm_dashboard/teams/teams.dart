import 'package:edutalim/CRM/api/apis.dart';
import 'package:edutalim/CRM/views/crm_dashboard/teams/team_members.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class teams extends StatefulWidget {

  @override
  State<teams> createState() => _leads_listState();
}

class _leads_listState extends State<teams> {

  List teamsdata = [];
  bool isLoading = true;

  get_teams()async{
    var data = await CRMApiBaseHandler.fet_teams();
    setState(() {
      teamsdata = data['data']['teams'];
      isLoading = false;
    });
  }


  @override
  void initState() {
    get_teams();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        title: Text("Teams",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.black),),
      ),
      body: isLoading?
      loader()
          :
      teamsdata.isEmpty ?
      Container(
        width: Get.width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset('assets/icons/no_data.png',width: Get.width/4,),
            SizedBox(height: 20,),
            Container(
              child: Text("No Teams  Found",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.black,fontSize: 16),),
            ),
          ],
        ),
      )
          :
      SingleChildScrollView(
        physics: ScrollPhysics(),
        child: Container(
          child: Column(
            children: [

              SizedBox(height: 5,),
              ListView.builder(
                physics: NeverScrollableScrollPhysics(),
                itemCount: teamsdata.length,
                shrinkWrap: true,
                itemBuilder: (context, index){
                  return GestureDetector(
                    onTap: (){
                      Get.to(team_members(team_id: teamsdata[index]['id'].toString()));
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        color: Colors.white,
                        border: Border.all(color: textblackColor.withValues(alpha: 0.1))
                      ),
                      margin: EdgeInsets.symmetric(horizontal: 15,vertical: 5),
                      padding: EdgeInsets.symmetric(horizontal: 20,vertical: 25),
                      child:  Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [

                          Row(
                            children: [
                              Container(
                                width: Get.width/8,
                                child: Image.asset('assets/icons/teams.png'),
                              ),
                              SizedBox(width: 15,),
                              Container(
                                // color: Colors.yellow,
                                width: Get.width/1.8,
                                child: Text(teamsdata[index]['title'].toString(), style: TextStyle(fontSize: Get.width/22, fontWeight: FontWeight.w600, color: commontextColor,overflow: TextOverflow.ellipsis),),
                              ),
                            ],
                          ),

                          Icon(Icons.keyboard_arrow_right_outlined,color: Colors.black54,),

                          // Center(
                          //     child: Theme(
                          //       data: ThemeData(),
                          //       child: CupertinoSwitch(
                          //         activeColor: Colors.green,
                          //         trackColor: Colors.red,
                          //         value: cres[index]['status'].toString() == "1" ? true : false,
                          //         onChanged: (v){
                          //           toggleteam_status(cres[index]['id'].toString());
                          //         },
                          //       ),
                          //     ),
                          // ),


                        ],
                      ),
                    ),
                  );
                },
              ),

            ],
          ),
        ),
      ),
    );
  }
}
