import 'package:edutalim/components/utils.dart';

import 'package:edutalim/components/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../login.dart';
import 'content.dart';
import 'content_image.dart';

class onboarding extends StatefulWidget {

  @override
  State<onboarding> createState() => _boardingState();
}

class _boardingState extends State<onboarding> {

  String? sel_language;

  PageController controller = PageController(viewportFraction: 1, keepPage: true);
  PageController controller2 = PageController(viewportFraction: 1, keepPage: true);
  int currentPage = 0;
  List<Map<String, String>> splashData = [
    {
      "title": "Learn beyond limits",
      "text": "Begin with the foundation — gain real-world knowledge and build confidence from day one.",
      "image": "assets/onboard/onb_1.png",
      "color": "0xff6481A8",
    },
    {
      "title": "Learn What Matters",
      "text": "Master in-demand skills in AI, fashion design, and more — built for tomorrow’s challenges.",
      "image": "assets/onboard/onb_2.png",
      "color": "0xff8982C4",
    },
    {
      "title": "Create Your Future",
      "text": "Turn passion into purpose with hands-on learning and mentorship that empowers you to lead.",
      "image": "assets/onboard/onb_3.png",
      "color": "0xff16B09C",
    },
  ];


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                primarylightColor.withValues(alpha: .4),
                primarylightColor.withValues(alpha: .7),
                primarylightColor,
                // primaryColor
              ],
              stops: [0,0.5,1],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child:  Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
        
              Container(
                decoration: BoxDecoration(
                  color: onboardBgColor.withValues(alpha: .7),
                  borderRadius: BorderRadius.vertical(bottom: Radius.circular(30)),
                ),
                child: Column(
                  children: [
                    AppBar(
                      backgroundColor: Colors.transparent,
                      surfaceTintColor: Colors.transparent,
                      elevation: 0,
                      actions: [
                        if(currentPage != 2)
                          GestureDetector(
                          onTap: (){
                            print("next ---page--------");
                            GetStorage().write('onboarding', 'false');
                            Get.offAll(() =>Login());
                          },
                          child: Container(
                            color: Colors.transparent,
                            padding: EdgeInsets.symmetric(horizontal: 25,vertical: 15),
                            child: Text("Skip",style: TextStyle(color: textblackColor,fontFamily: font_semibold),),
                          ),
                        ),
                      ],
                    ),
        
        
                    Container(
                      height: Get.height*.05,
                    ),
        
                    Container(
                      // color: Colors.yellow,
                      // width: Get.width/1.3,
                      height: Get.height/2.5,
                      alignment: Alignment.center,
                      // padding: EdgeInsets.symmetric(horizontal: 20),
                      child: PageView.builder(
                        // physics: NeverScrollableScrollPhysics(),
                        // pageSnapping: false,
                        physics: NeverScrollableScrollPhysics(),
                        controller: controller,
                        onPageChanged: (value) {
                          setState(() {
                            currentPage = value;
        
                          });
                        },
                        itemCount: splashData.length,
                        itemBuilder: (context, index) => content_image(
                          image: splashData[index]["image"],
                          text: splashData[index]['text'],
                          title: splashData[index]['title'],
                          color: splashData[index]['color'],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
        
        
              Container(
                padding: EdgeInsets.symmetric(vertical: 10,horizontal: 12),
                margin: EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  // color: textwhiteColor,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Column(
                  children: [
        
                    SizedBox(height: Get.height/50,),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        splashData.length,
                            (index) => buildDot(index: index),
                      ),
                    ),
        
                    SizedBox(height: Get.height/20,),
        
                    Container(
                      // color: Colors.yellow,
                      padding: EdgeInsets.symmetric(horizontal: Get.width/17),
                      height: Get.height/6,
                      alignment: Alignment.center,
                      // padding: EdgeInsets.symmetric(horizontal: 20),
                      child: PageView.builder(
                        // physics: NeverScrollableScrollPhysics(),
                        controller: controller2,
                        physics: NeverScrollableScrollPhysics(),
                        // pageSnapping: false,
                        onPageChanged: (value) {
        
        
        
                          setState(() {
                            currentPage = value;
                          });
        
                          print('---------L------');
                          print('---------L------'+value.toString());
        
        
                        },
                        itemCount: splashData.length,
                        itemBuilder: (context, index) => content(
                          image: splashData[index]["image"],
                          text: splashData[index]['text'],
                          title: splashData[index]['title'],
                          color: splashData[index]['color'],
                        ),
                      ),
                    ),
        
                    SizedBox(height: Get.height/20,),
        
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        children: [
        
                          if(currentPage > 0)
                          //   Expanded(
                          //   child: GestureDetector(
                          //     onTap: (){
                          //
                          //
                          //     },
                          //     child: Container(
                          //       decoration: BoxDecoration(
                          //           color: textwhiteColor,
                          //           borderRadius: BorderRadius.circular(1000),
                          //           border: Border.all(color: primaryColor)
                          //       ),
                          //       alignment: Alignment.center,
                          //       padding: EdgeInsets.all(15),
                          //       child: Text("Prev",style: TextStyle(color: primaryColor,fontFamily: font_semibold),),
                          //     ),
                          //   ),
                          // ),
        
                            Expanded(
                              child: common_button_gradient_secondary(onPressed: (){
        
                                if(currentPage > 0)
                                {
                                  // controller.jumpToPage(currentPage+1); // for regular jump
                                  controller.animateToPage(currentPage-1, curve: Curves.easeInOut, duration: Duration(milliseconds: 700),); // for animated jump. Requires a curve and a duration
                                  controller2.animateToPage(currentPage-1, curve: Curves.easeInOut, duration: Duration(milliseconds: 700),); // for animated jump. Requires a curve and a duration
                                }
                              },
                                borderRadius: BorderRadius.circular(10),
                                child: Container(
                                // decoration: BoxDecoration(
                                //     color: textwhiteColor,
                                //     borderRadius: BorderRadius.circular(1000),
                                //     border: Border.all(color: primaryColor)
                                // ),
                                alignment: Alignment.center,
                                padding: EdgeInsets.all(15),
                                child: Text("Prev",style: TextStyle(color: textwhiteColor,fontFamily: font_semibold),),
                              ),
                              ),
                            ),
                          // if(currentPage < 2)
                          //   Expanded(
                          //   child: GestureDetector(
                          //     onTap: (){
                          //
                          //       if(currentPage < 2)
                          //       {
                          //         // controller.jumpToPage(currentPage+1); // for regular jump
                          //         controller.animateToPage(currentPage+1, curve: Curves.easeInOut, duration: Duration(milliseconds: 700),); // for animated jump. Requires a curve and a duration
                          //         controller2.animateToPage(currentPage+1, curve: Curves.easeInOut, duration: Duration(milliseconds: 700),); // for animated jump. Requires a curve and a duration
                          //
                          //       }else{
                          //         print("next ---page--------");
                          //         GetStorage().write('onboarding', 'false');
                          //         // Get.offAll(() =>login());
                          //       }
                          //
                          //     },
                          //     child: Container(
                          //       margin: EdgeInsets.only(left: 10),
                          //       decoration: BoxDecoration(
                          //         color: primaryColor,
                          //         borderRadius: BorderRadius.circular(1000),
                          //         border: Border.all(color: primaryColor)
                          //       ),
                          //       alignment: Alignment.center,
                          //       padding: EdgeInsets.all(15),
                          //       child: Text(currentPage < 2 ? "Next" : "Continue",style: TextStyle(color: textwhiteColor,fontFamily: font_semibold),),
                          //     ),
                          //   ),
                          // ),
        
                          SizedBox(width: 10,),
                          Expanded(
                            child: common_button_gradient_secondary(
                              onPressed: (){
                                if(currentPage < 2)
                                      {
                                        // controller.jumpToPage(currentPage+1); // for regular jump
                                        controller.animateToPage(currentPage+1, curve: Curves.easeInOut, duration: Duration(milliseconds: 700),); // for animated jump. Requires a curve and a duration
                                        controller2.animateToPage(currentPage+1, curve: Curves.easeInOut, duration: Duration(milliseconds: 700),); // for animated jump. Requires a curve and a duration
        
                                      }else{
                                        print("next ---page--------");
                                        GetStorage().write('onboarding', 'false');
                                        Get.offAll(() =>Login());
                                      }
                              },
                              borderRadius: BorderRadius.circular(10),
                              child: Container(
                              // margin: EdgeInsets.only(left: 10),
                              decoration: BoxDecoration(
                                  // color: primaryColor,
                                  // borderRadius: BorderRadius.circular(1000),
                                  // border: Border.all(color: primaryColor)
                              ),
                              alignment: Alignment.center,
                              // padding: EdgeInsets.all(15),
                              child: Text(currentPage < 2 ? "Next" : "Continue",style: TextStyle(color: textwhiteColor,fontFamily: font_semibold),),
                            ),),
                          )
                        ],
                      ),
                    ),
        
                    SizedBox(height: Get.height/60,),
        
                  ],
                ),
              ),
        
              SizedBox(height: 30,),
        
            ],
          ),
        ),
      ),
    );
  }


  AnimatedContainer buildDot({int? index}) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 600),
      margin: EdgeInsets.only(right: 5),
      height: 14,
      width: 14,
      decoration: BoxDecoration(
        color: currentPage == index ? secondarylightColor : textwhiteColor,
        borderRadius: BorderRadius.circular(1000),
      ),
    );
  }

}
