// import 'dart:async';
// import 'dart:io';
// import 'package:get/get.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:razorpay_flutter/razorpay_flutter.dart';
//
//
//
// import 'package:edutalim/components/constants.dart';
// import 'package:edutalim/components/utils.dart';
// import '../../../controllers/homecontroller.dart';
// import '../../../controllers/planscontroller.dart';
//
//
// class razorpay extends StatefulWidget {
//
//   final String phone,email,desc,amount,razorp_key,package_id,order_id,logo,from,isRedeemed,redeem_amount;
//   razorpay({
//     required this.phone,
//     required this.email,
//     required this.desc,
//     required this.amount,
//     required this.razorp_key,
//     required this.package_id,
//     required this.order_id,
//     required this.logo,
//     required this.from,
//     required this.isRedeemed,
//     required this.redeem_amount,
//   });
//
//   @override
//   _MyAppState createState() => _MyAppState();
// }
//
// class _MyAppState extends State<razorpay> {
//
//   Plancontroller controller = Get.put(Plancontroller());
//   Homecontroller controller2 = Get.put(Homecontroller());
//
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<Plancontroller>
//       (builder: (controller){
//       return WillPopScope(
//         onWillPop: (){
//           return controller.onWillPop();
//         },
//         child: Scaffold(
//           appBar: AppBar(
//             automaticallyImplyLeading: false,
//             elevation: 0,
//             backgroundColor: Colors.white,
//             title: Text('Payment',style: TextStyle(color: Colors.black),),
//           ),
//           body: Center(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   SizedBox(height: MediaQuery.of(context).size.height/5,),
//                   controller.direction == "forward"?
//                   Container(
//                     child: Text("Processing...",style: TextStyle(color: Colors.black54,fontSize: MediaQuery.of(context).size.width/20),),
//                   )
//                       :Container(
//                     child: Column(
//                       children: [
//                         controller.status.toString() == "failed" ?
//                         Icon(Icons.cancel_outlined,color: Colors.red,size: MediaQuery.of(context).size.width/5,):
//                         Icon(Icons.check_circle_outline_outlined,color: Colors.green,size: MediaQuery.of(context).size.width/5),
//                         SizedBox(height: 10,),
//                         Text("Payment "+controller.status.toString(),style: TextStyle(color: controller.status.toString() == "failed" ? Colors.red : Colors.green,fontSize: MediaQuery.of(context).size.width/20,fontFamily: "poppins_regular",fontWeight: FontWeight.w600),),
//                         controller.status == "success" ? Container() : SizedBox(height: MediaQuery.of(context).size.width/10,),
//                         controller.status == "success" ? Container() :Container(
//                           decoration: BoxDecoration(
//                               border: Border.all(color: Color(0xfff6f6f7)),
//                               color: Color(0xfff6f6f7),
//                               borderRadius: BorderRadius.all(Radius.circular(3))
//                           ),
//                           padding: EdgeInsets.all(10),
//                           // child: Text(controller.status.toString() == "failed" ? Platform.isIOS ? controller.resp.toString().split('-')[1].toString() : controller.ress.toString().split('{')[2].toString().split(':')[2].toString().split('"')[1].toString() : "Payment Success",style: TextStyle(color: controller.status.toString() == "failed" ? Colors.red : Colors.green,fontSize: MediaQuery.of(context).size.width/30,fontFamily: "poppins_regular",fontWeight: FontWeight.w500),),
//                         ),
//                       ],
//                     ),
//                   ),
//                   SizedBox(height: MediaQuery.of(context).size.height/3.5,),
//                   controller.status.toString() == "failed" ?Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       GestureDetector(
//                         onTap: ()async{
//                           GetStorage().write('home_index', 0);
//                           // Navigator.of(context).pushAndRemoveUntil(MaterialPageRoute(builder: (context) => dashboard()), (Route<dynamic> route) => false);
//                           Get.back();
//                         },
//                         child: Container(
//                           decoration: BoxDecoration(
//                               border: Border.all(color: Colors.blueAccent),
//                               borderRadius: BorderRadius.all(Radius.circular(3),)
//                           ),
//                           alignment: Alignment.center,
//                           width: MediaQuery.of(context).size.width/2.5,
//                           padding: EdgeInsets.all(10),
//                           child: Text("Cancel",style: TextStyle(color: Colors.blueAccent),),
//                         ),
//                       ),
//                       SizedBox(width: 10,),
//                       GestureDetector(
//                         onTap: (){
//                           openCheckout();
//                         },
//                         child:   Container(
//                           decoration: BoxDecoration(
//                               border: Border.all(color: Colors.orange),
//                               borderRadius: BorderRadius.all(Radius.circular(3),),
//                               color: Colors.orange
//                           ),
//                           alignment: Alignment.center,
//                           width: MediaQuery.of(context).size.width/2.5,
//                           padding: EdgeInsets.all(10),
//                           child: Text("Retry",style: TextStyle(color: Colors.white),),
//                         ),
//                       ),
//                     ],
//                   )
//                       :
//                   Container(
//                     child: Text("Please wait, you will be redirected automatically...",style: TextStyle(fontSize: MediaQuery.of(context).size.width/30,color: Colors.black54),),
//                   ),
//                 ],
//               )
//           ),
//         ),
//       );
//     });
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     openCheckout();
//   }
//
//   @override
//   void dispose(){
//     super.dispose();
//   }
//
//   void openCheckout() async {
//
//     Razorpay razorpay = Razorpay();
//     var options = {
//       'key': widget.razorp_key.toString(),
//       'amount': widget.amount.toString(),
//       'name': appName,
//       'description': widget.desc.toString(),
//       'retry': {'enabled': true, 'max_count': 1},
//       'send_sms_hash': true,
//       'prefill': {
//         'contact': widget.phone.toString(),
//         'email': widget.email.toString(),
//         'method': {
//           "netbanking": true,
//           "card": true,
//           "upi": true,
//           "wallet": true,
//           "emi": true,
//           "paylater": false,
//         },
//       },
//       'image': widget.logo.toString(),
//       'theme': {'color': "#$razorpay_color"},
//       'external': {
//         'wallets': ['paytm', 'freecharge', 'mobikwik'],
//       },
//       'currency': 'INR',
//       'order_id': widget.order_id.toString(),
//     };
//     razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, handlePaymentErrorResponse);
//     razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, handlePaymentSuccessResponse);
//     razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, handleExternalWalletSelected);
//     razorpay.open(options);
//
//
//   }
//
//
//   // ---------------------- handle START -------------------------------------
//   void handlePaymentErrorResponse(PaymentFailureResponse response){
//     /*
//     * PaymentFailureResponse contains three values:
//     * 1. Error Code
//     * 2. Error Description
//     * 3. Metadata
//     * */
//     // showAlertDialog(context, "Payment Failed", "Code: ${response.code}\nDescription: ${response.message}\nMetadata:${response.error.toString()}");
//
//
//     toast_error('Payment failed');
//     setState(() {
//       controller.status = "failed";
//       controller.resp = response.code.toString() + " - " + response.message!;
//       controller.ress = [controller.resp];
//       controller.direction = "backward";
//     });
//
//   }
//
//   void handlePaymentSuccessResponse(PaymentSuccessResponse response){
//     /*
//     * Payment Success Response contains three values:
//     * 1. Order ID
//     * 2. Payment ID
//     * 3. Signature
//     * */
//     // showAlertDialog(context, "Payment Successful", "Payment ID: ${response.paymentId}");
//
//
//     toast_success('Payment Success');
//     setState(() {
//       controller.status = "success";
//       controller.resp = response.paymentId.toString();
//       controller.ress = [controller.resp];
//       controller.direction = "backward";
//     });
//
//     if(widget.from.toString() == 'course_plan') {
//       controller.savepaymentinfo(response.paymentId.toString(),response.orderId.toString(),response.signature.toString(),widget.package_id.toString(),widget.isRedeemed.toString(),widget.redeem_amount.toString());
//     }
//     else {
//       // controller2.savepaymentinfo_p_fees(response.paymentId.toString(),response.orderId.toString(),response.signature.toString(),widget.package_id.toString());
//     }
//
//     print("handling success--------");
//     print(controller.resp);
//
//
//   }
//
//   void handleExternalWalletSelected(ExternalWalletResponse response){
//     toast_error(" EXTERNAL_WALLET : "+ response.walletName!);
//     setState(() {
//       controller.direction = "backward";
//     });
//   }
//
//   void showAlertDialog(BuildContext context, String title, String message){
//     // set up the buttons
//     Widget continueButton = ElevatedButton(
//       child: const Text("Continue"),
//       onPressed:  () {},
//     );
//     // set up the AlertDialog
//     AlertDialog alert = AlertDialog(
//       title: Text(title),
//       content: Text(message),
//       actions: [
//         continueButton,
//       ],
//     );
//     // show the dialog
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return alert;
//       },
//     );
//   }
// // ---------------------- handle END -------------------------------------
//
// }
