import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../LMS/api/auth_apis.dart';
import '../components/constants.dart';
import '../components/utils.dart';
import '../views/auth/otp.dart';
import '../views/auth/register.dart';

class Logincontroller extends GetxController {
  TextEditingController phoneclr = TextEditingController();
  bool isLoading = false;

  var countryFlag = "🇮🇳";
  String? countrycode_number = "91", countrycode_text = "IN";

  get_login() async {
    isLoading = true;
    update();

    if (phoneclr.text.isNotEmpty) {
      var data = await AuthApis.login(phoneclr.text.toString(), '+${countrycode_number.toString()}');
      log(data.toString());
      if (data['status'].toString() == "true" || data['status'].toString() == '1') {
        toast_success(data['message'].toString());
        Get.to(() => otp(from: 'login', phone: phoneclr.text.toString(), code: '+${countrycode_number.toString()}'));
      } else {
        Get.to(() => register());
        toast_success(data['message'].toString());
      }
    } else {
      toast_warning('Please Enter your phone number');
    }

    isLoading = false;
    update();
  }
}
