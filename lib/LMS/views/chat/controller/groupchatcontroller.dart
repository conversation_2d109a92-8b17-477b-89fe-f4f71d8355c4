import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:edutalim/LMS/views/chat/apis/speekichat_apis.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;

class GroupchatController extends GetxController {
  var chatId = '';
  var commentId = '';
  // bool isChatListLoading = false;
  TextEditingController searchController = TextEditingController();
  TextEditingController commentController = TextEditingController();

  bool isChatListloading = true;
  // String? recaudioPath;
  var chatList;
  bool isRecording = false;

  chatListData() async {
    isChatListloading = true;
    update();
    var data = await SpeekichatApihandler.chatList();
    chatList = data['data'];
    isChatListloading = false;
    update();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (chatScrollController.hasClients) {
        chatScrollController.jumpTo(
          chatScrollController.position.maxScrollExtent,
        );
      }
    });
  }

  bool isMessageListloading = true;
  bool commonLoader = true;

  var messageList = [];

  messageListData(chat_id, {bool scrollToBottom = true}) async {
    isMessageListloading = true;
    commonLoader = true;
    update();

    var data = await SpeekichatApihandler.messageList(chat_id);
    commonLoader = false;
    isMessageListloading = false;
    update();
    if (data != null && data['status'] == 1) {
      if (data['data'] != null && data['data']['chat_list'] != null) {
        messageList = data['data']['chat_list'];
        if (scrollToBottom) _scrollToBottom();
      } else {
        messageList = []; // Handle case where chat_list is missing or empty
      }
    } else {
      messageList = []; // Handle failed API response
    }

    isMessageListloading = false;
    update();
  }

  var commentData;
  var messageCommentLoading = false;
  messageComments(
    chat_id,
  ) async {
    messageCommentLoading = true;
    commonLoader = true;
    update();
    var data = await SpeekichatApihandler.getComments(chat_id);

    messageCommentLoading = false;
    commonLoader = false;
    update();
    if (data != null && data['status'] == 1) {
      commentData = data['data'];
      _scrollToBottom();
    } else {}
  }

  bool isMessageLoading = false;
  //  var isSendingMessage = false;
  // var messageData;

  sendMessage({
    required String chatType,
    required String chatTypeId,
    required String messageType,
    required String message,
    required String isTaggedMessage,
    required String taggedMsgId,
    dynamic filePath,
  }) async {
    isMessageLoading = true;
    commonLoader = true;
    update();
    var data = await SpeekichatApihandler.sendMessage(
        chatType: chatType,
        chatTypeId: chatTypeId,
        messageType: messageType,
        message: message,
        isTaggedMessage: isTaggedMessage,
        taggedMsgId: taggedMsgId,
        filePath: filePath);
    commonLoader = false;
    update();
    // if (msgTextController.text != '') {

    // } else {
    //   isText = false;
    // }
    if (data['status'] == 1) {
      msgTextController.text = '';
      imageFile = null;
      isText = false;

      isMessageListloading = false;
      commonLoader = false;
      update();
      // messageListData(chatId);
      fetchMessages(chatId.toString());
      if (isTaggedMessage == "1") {
        messageComments(commentId);
      }

      // messageComments(chatId);
    }

    // chatList = data['data'];
    isMessageLoading = false;
    commonLoader = false;
    update();
  }

  Map<String, bool> deleteLoadingMap = {};
  deleteMessage(messageId) async {
    try {
      commonLoader = true;
      deleteLoadingMap[messageId] = true;
      update();
      var data = await SpeekichatApihandler.deleteMessage(messageId);
      commonLoader = false;
      update();
      // deleteLoadingMap[messageId] = false;
      // update();
      if (data['status'] == 1) {
        isMessageListloading = false;
        update();

        // deleteLoadingMap[messageId] = true;
        // update();

        //working area
        fetchMessages(chatId.toString());
        messageListData(chatId, scrollToBottom: false);
        deleteLoadingMap[messageId] = false;
        update();
        isMessageListloading = false;

        update();
      } else {}
    } catch (e) {
      isMessageListloading = false;
      update();
      log('Delete message error : $e');
    }
  }

  editMessage(message_id, message) async {
    try {
      commonLoader = true;
      update();
      var data = await SpeekichatApihandler.editMessage(message_id, message);
      isMessageListloading = false;
      commonLoader = false;
      update();
      if (data['status'] == 1) {
        print('Edit success');
        isMessageListloading = false;
        update();
        messageListData(chatId, scrollToBottom: true);
        isMessageListloading = false;
        update();
      }
    } catch (e) {}
  }

  bool isChatPageLoading = false;
  ScrollController chatScrollController = ScrollController();
  TextEditingController msgTextController = TextEditingController();
  bool isText = false;
  bool showAttachmentSection = false;
  File? imageFile;
  List chat = [
    {
      "id": 1,
      "message": 'Hi',
      "send": "John Doe",
      "time": "09:56 AM",
      "type": "text"
    }
  ];

  void scrollToBottom() {
    chatScrollController.animateTo(
      chatScrollController.position.maxScrollExtent, // Scroll to the end
      duration: Duration(milliseconds: 300), // Scroll duration
      curve: Curves.easeOut, // Animation curve
    );
  }

  List<dynamic> messageListNew = [];
  int from = 0;
  int count = 10;
  bool isLoadingMore = false;
  bool hasMoreMessages = true;

  Future<void> fetchMessages(String chatId, {bool loadMore = false}) async {
    if (loadMore && isLoadingMore)
      return; // Prevent multiple requests at the same time

    if (loadMore) {
      isLoadingMore = true; // Set loading flag to true when loading more
      update();
    } else {
      // If it's the initial fetch, reset pagination variables
      from = 0;
      messageListNew.clear();
      hasMoreMessages = true;
    }

    try {
      final response = await http.get(
        Uri.parse(
          'https://speeki.trogon.info/api/message/get_chat_list_new?chat_id=$chatId&from=$from&to=${from + count}&count=$count&auth_token=${GetStorage().read('auth_token').toString()}',
        ),
      );
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 1) {
          final newMessages =
              List<Map<String, dynamic>>.from(data['data']['chat_list']);

          if (newMessages.isNotEmpty) {
            messageListNew.addAll(newMessages);
            from += count;
            hasMoreMessages = true;
          } else {
            hasMoreMessages = false;
          }
          isLoadingMore = false;
          update();
        } else {
          print('Error in response data: ${data['message']}');
        }
      } else {
        print(
            'Failed to load messages with status code ${response.statusCode}');
      }
    } catch (e) {
      print('Error: $e');
      isLoadingMore = false;
    }
  }
}
