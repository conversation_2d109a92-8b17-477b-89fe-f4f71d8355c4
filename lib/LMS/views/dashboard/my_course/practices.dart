import 'package:edutalim/components/constants.dart';
import 'package:edutalim/LMS/controllers/mycoursecontroller.dart';
import 'package:edutalim/LMS/views/tools/web_view_pg.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/utils.dart';
import 'leaderboard.dart';

class PracticeScreen extends StatefulWidget {
  String course_id;

  PracticeScreen({required this.course_id});

  @override
  State<PracticeScreen> createState() => _PracticeScreenState();
}

class _PracticeScreenState extends State<PracticeScreen> {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());

  @override
  void initState() {
    controller.getPractices(widget.course_id);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: GetBuilder<Mycoursecontroller>(builder: (controller) {
        return Scaffold(
          backgroundColor: textwhiteColor,
          appBar: PreferredSize(
            preferredSize: Size.fromHeight(120),
            child: Column(
              children: [
                AppBar(
                  backgroundColor: textwhiteColor,
                  surfaceTintColor: textwhiteColor,
                  elevation: 5,
                  centerTitle: true,
                  leading: GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      // decoration: BoxDecoration(
                      //     color: primaryColor,
                      //     borderRadius: BorderRadius.circular(100)
                      // ),
                      margin: EdgeInsets.all(10),
                      child: Icon(
                        Icons.arrow_back,
                        color: textblackColor,
                      ),
                    ),
                  ),
                  title: Container(
                    child: Text(
                      "Practices",
                      style: TextStyle(color: textblackColor, fontWeight: FontWeight.w700, fontFamily: font_regular, fontSize: Get.width / 24),
                    ),
                  ),
                ),
                SizedBox(height: 10),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 4)],
                  ),
                  child: TabBar(
                    indicatorColor: Color(0xFFd2a83d), // gold
                    labelColor: Colors.black,
                    unselectedLabelColor: Colors.black45,
                    tabs: [
                      Tab(text: 'Current'),
                      Tab(text: 'Completed'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          body: controller.isPracticeLoading
              ? loader()
              : TabBarView(
                  children: [
                    buildPracticeList(controller.practicePage['not_attempted'], "Current"),
                    buildPracticeList(controller.practicePage['attempted'], "Completed"),
                  ],
                ),
        );
      }),
    );
  }

  Widget buildPracticeList(List practices, type) {
    if (practices.isEmpty) {
      return Container(
        padding: EdgeInsets.all(80),
        alignment: Alignment.topCenter,
        child: Text(
          "No $type Practice's Found......",
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.black),
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: practices.length,
      itemBuilder: (context, index) {
        final practice = practices[index];
        return PracticeCard(practice,type.toString());
      },
    );
  }
}

class PracticeCard extends StatelessWidget {
  var data;
  String type;
  PracticeCard(this.data,this.type);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: textwhiteColor,
      surfaceTintColor: textwhiteColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12), side: BorderSide(color: Color(0xFFE0E0E0))),
      elevation: 4,
      margin: EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(data['title'].toString(), style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Color(0xFF2F4F4F))),
            SizedBox(height: 8),
            Text(
              data['short_description'].toString(),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 12),


            type.toString() == "Current" ?
            Row(
              children: [

                Expanded(
                  child: Container(
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Get.to(() => webview_page(url: data['exam_url'].toString()));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFFD4A437), // golden yellow
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                    child: Text("Attempt", style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontFamily: font_semibold, fontSize: Get.width / 32)),
                  ),
                ),

              ],
            ) :
            Row(
              children: [
                Expanded(
                  child: Container(),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Get.to(() => webview_page(url: data['exam_url'].toString()));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFFD4A437), // golden yellow
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                    child: Text("View Result", style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontFamily: font_semibold, fontSize: Get.width / 32)),
                  ),
                ),


              ],
            ),
          ],
        ),
      ),
    );
  }
}
