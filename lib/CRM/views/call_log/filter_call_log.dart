// import 'package:call_log/call_log.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
// import 'package:edutalim/components/utils.dart';
//
// class FilteredCallLogPage extends StatefulWidget {
//   final String number; // the number to filter call logs
//
//   FilteredCallLogPage({required this.number});
//
//   @override
//   _FilteredCallLogPageState createState() => _FilteredCallLogPageState();
// }
//
// class _FilteredCallLogPageState extends State<FilteredCallLogPage> {
//   List<CallLogEntry> filteredLogs = [];
//
//   @override
//   void initState() {
//     super.initState();
//     getFilteredCallLogs();
//   }
//
//   Future<void> getFilteredCallLogs() async {
//     Iterable<CallLogEntry> entries = await CallLog.query(
//       number: widget.number, // filter by number
//     );
//     setState(() {
//       filteredLogs = entries.toList();
//     });
//   }
//
//   String formatTimestamp(int timestamp) {
//     DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
//     String formattedDate = DateFormat('MMM dd, yyyy').format(dateTime);
//     String formattedTime = DateFormat('hh:mm a').format(dateTime);
//     return '$formattedDate - $formattedTime';
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Call Details', style: TextStyle(color: Colors.black)),
//         backgroundColor: Colors.white,
//         elevation: 0,
//         iconTheme: IconThemeData(color: Colors.black),
//         centerTitle: true,
//       ),
//       body: filteredLogs.isEmpty
//           ? Center(child: Text('No call logs found for ${widget.number}'))
//           : ListView.builder(
//               itemCount: filteredLogs.length,
//               itemBuilder: (context, index) {
//                 CallLogEntry log = filteredLogs[index];
//                 return Container(
//                   decoration: BoxDecoration(
//                       color: Colors.white,
//                       borderRadius: BorderRadius.circular(12)),
//                   margin: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
//                   padding: EdgeInsets.symmetric(vertical: 12, horizontal: 15),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Row(children: [
//                         Icon(Icons.account_circle,
//                             color: Colors.black54, size: Get.width / 10),
//                         SizedBox(width: 13),
//                         Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Text(log.name ?? 'Unknown',
//                                 style: TextStyle(
//                                     color: log.callType
//                                                 .toString()
//                                                 .split('.')[1]
//                                                 .toString() ==
//                                             "missed"
//                                         ? Colors.red
//                                         : Colors.black,
//                                     fontWeight: FontWeight.w600,
//                                     fontSize: Get.width / 24)),
//                             Text(log.number.toString()),
//                             Row(
//                               children: [
//                                 Icon(
//                                   log.callType.toString().split('.')[1] ==
//                                           "missed"
//                                       ? Icons.call_missed
//                                       : log.callType.toString().split('.')[1] ==
//                                               "outgoing"
//                                           ? Icons.call_made
//                                           : Icons.call_received,
//                                   color:
//                                       log.callType.toString().split('.')[1] ==
//                                               "missed"
//                                           ? Colors.red
//                                           : Colors.black54,
//                                   size: 15,
//                                 ),
//                                 SizedBox(width: 5),
//                                 Text(
//                                   log.callType.toString().split('.')[1],
//                                   style: TextStyle(
//                                       color: log.callType
//                                                   .toString()
//                                                   .split('.')[1] ==
//                                               "missed"
//                                           ? Colors.red
//                                           : Colors.black),
//                                 ),
//                                 SizedBox(width: 5),
//                                 Text(" - ${formatDuration(log.duration!)}"),
//                               ],
//                             ),
//                             Text(
//                               formatTimestamp(
//                                   int.parse(log.timestamp.toString())),
//                               style: TextStyle(fontSize: Get.width / 33),
//                             ),
//                           ],
//                         ),
//                       ]),
//                       IconButton(
//                         onPressed: () {
//                           launchURL('tel:${log.number}');
//                         },
//                         icon: Icon(Icons.call, color: Colors.green),
//                       ),
//                     ],
//                   ),
//                 );
//               },
//             ),
//     );
//   }
// }
