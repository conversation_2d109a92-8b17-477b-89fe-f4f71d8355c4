import 'package:cached_network_image/cached_network_image.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:edutalim/LMS/controllers/mycoursecontroller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class change_course extends StatefulWidget {
  const change_course({super.key});

  @override
  State<change_course> createState() => _change_courseState();
}

class _change_courseState extends State<change_course> {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());

  @override
  void initState() {
    controller.enrolled_courses();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Mycoursecontroller>(builder: (controller) {
      return Scaffold(
        backgroundColor: bgColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          title: Container(
            padding: EdgeInsets.symmetric(horizontal: 15),
            child: Text(
              "Change Course",
              style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 22, color: textblackColor),
            ),
          ),
          centerTitle: true,
          automaticallyImplyLeading: false,
          leadingWidth: 45,
          leading: GestureDetector(
            onTap: () {
              Get.back();
            },
            child: Container(
              margin: EdgeInsets.only(left: 10),
              // width: 40,
              //
              // padding: EdgeInsets.all(18),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.transparent,
                border: Border.all(color: textblackColor.withValues(alpha: 0.1)),
              ),
              child: Icon(CupertinoIcons.left_chevron),
            ),
          ),
        ),
        body: Container(
          margin: EdgeInsets.only(top: 5),
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: controller.isEnrolledListLoading
                    ? loader()
                    : controller.enrolledCourseList.isEmpty
                        ? Container(
                            child: Center(
                              child: Text("No Enrolled Course Found...."),
                            ),
                          )
                        : ListView.builder(
                            shrinkWrap: true,
                            itemCount: controller.enrolledCourseList.length,
                            physics: BouncingScrollPhysics(),
                            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 30),
                            itemBuilder: (_, index) {

                              Map data = controller.enrolledCourseList[index];

                              return GestureDetector(
                                onTap: (){

                                  controller.switch_course(data['course_id'].toString());

                                },
                                child: Container(
                                  padding: EdgeInsets.all(10),
                                  margin: EdgeInsets.only(bottom: 15),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: secondaryColor,width: 0.5),
                                    color: secondaryColor.withValues(alpha: 0.05),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Row(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: CachedNetworkImage(
                                          imageUrl: data['thumbnail'].toString(),
                                          width: Get.width / 4,
                                          errorWidget: (_, ___, __) {
                                            return Image.asset("assets/images/placeholder_rect.webp");
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: 10,),
                                      Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(data['title'].toString(),style: TextStyle(fontWeight: FontWeight.w600,fontSize: Get.width/26),maxLines: 2,),
                                          const SizedBox(height: 2,),
                                          Text(data['short_description'].toString(),style: TextStyle(fontSize: Get.width/34),maxLines: 1,overflow: TextOverflow.ellipsis,),
                                        ],
                                      ))
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
              )
            ],
          ),
        ),
      );
    });
  }
}
