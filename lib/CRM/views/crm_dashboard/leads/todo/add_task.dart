import 'package:edutalim/CRM/controller/todocontroller.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

class AddTaskPage extends StatefulWidget {
  @override
  _AddTaskPageState createState() => _AddTaskPageState();
}

class _AddTaskPageState extends State<AddTaskPage> {
  Todocontroller controller = Get.put(Todocontroller());

  @override
  void initState() {
    controller.call_telecallers();
    super.initState();
  }

  void _pickDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.selectedDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      controller.selectedDate = picked;
      controller.update();
    }
  }

  InputDecoration _inputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      labelStyle: TextStyle(fontWeight: FontWeight.bold),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.blue, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Todocontroller>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: textwhiteColor,
          appBar: AppBar(
            title: Text('Add Todo'),
            centerTitle: true,
            backgroundColor: textwhiteColor,
          ),
          body: controller.isaddtodoloading
              ? loader()
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      TextField(
                        controller: controller.titleController,
                        decoration: _inputDecoration('Title'),
                      ),
                      SizedBox(height: 16),
                      TextField(
                        controller: controller.descriptionController,
                        maxLines: 3,
                        decoration: _inputDecoration('Description'),
                      ),
                      SizedBox(height: 16),
                      if (GetStorage().read('user_role').toString() == "1")
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: controller.isteLoading
                              ? loader()
                              : DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    hint: const Text("Select Telecaller"),
                                    value: controller.selectedTelecallerId,
                                    isExpanded: true,
                                    items: controller.telecallers
                                        .map<DropdownMenuItem<String>>(
                                            (telecaller) {
                                      return DropdownMenuItem<String>(
                                        value: telecaller['id'].toString(),
                                        child: Text(telecaller['name']),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      controller.selectedTelecallerId = value!;
                                      controller.update();
                                    },
                                  ),
                                ),
                        ),
                      SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              controller.selectedDate == null
                                  ? 'No date selected'
                                  : 'Due Date: ${DateFormat('yyyy-MM-dd').format(controller.selectedDate!)}',
                              style: TextStyle(fontSize: 16),
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: _pickDate,
                            icon: Icon(Icons.calendar_today,
                                color: textwhiteColor),
                            label: Text('Pick Date',
                                style: TextStyle(color: textwhiteColor)),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 50),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: controller.add_todo,
                          child: controller.isaddtodoloading
                              ? CircularProgressIndicator(color: textwhiteColor)
                              : Text(
                                  'Submit Task',
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      color: textwhiteColor),
                                ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: primaryColor,
                            padding: EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
        );
      },
    );
  }
}
