
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileCard extends StatelessWidget {
  double? height;
  double? width;
  final String index;
  final String name;
  final String experience;
  final String imageUrl;
  final Color backgroundColor;
  final Color circleColor;

  ProfileCard({
    Key? key,
    this.height,
    this.width,
    required this.index,
    required this.name,
    required this.experience,
    required this.imageUrl,
    required this.backgroundColor,
    required this.circleColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      // padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        // color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        image: DecorationImage(
            fit: BoxFit.fitWidth,
            alignment: Alignment.center,
            image: AssetImage(index.toString() == '0' ?'assets/bg/teacher_bg.png':'assets/bg/teacher_bg2.png')
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // SizedBox(height: 5),
          Row(
            children: [
              Container(
                width: 65,
                height: 65,
                // decoration: BoxDecoration(
                //   image: DecorationImage(image: AssetImage(imageUrl)),
                //   // shape: BoxShape.circle,
                // ),
                child: Image.network(
                    imageUrl,
                  errorBuilder: (a,b,c){
                    return Image.asset('assets/images/teacher_placeholder.png',);
                  },
                ),
              ),
              SizedBox(width: 10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: width!-90,
                    // color: Colors.yellowAccent,
                    child: Text(name,
                        style: TextStyle(
                            fontSize: Get.width*.035,
                            fontWeight: FontWeight.bold,
                            color: Colors.black)),
                  ),
                  SizedBox(height: 4),
                  Container(
                    width: width!-90,
                    child: Text(experience,
                        style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }
}