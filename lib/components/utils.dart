import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:story/story_page_view.dart';
import 'package:url_launcher/url_launcher.dart';

import 'constants.dart';




String formatDuration(int seconds) {
  Duration duration = Duration(seconds: seconds);

  if (duration.inSeconds < 60) {
    return '${duration.inSeconds} sec';
  } else if (duration.inMinutes < 60) {
    return '${duration.inMinutes} min';
  } else if (duration.inHours < 24) {
    return '${duration.inHours} hr';
  } else {
    return '${duration.inDays} day';
  }
}



class common_button_gradient_secondary extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;

  // final Color color1, color2;
  final BorderRadius? borderRadius;
  final double? width;
  final double height;

  const common_button_gradient_secondary({
    Key? key,
    required this.onPressed,
    required this.child,
    // required this.color1,
    // required this.color2,
    this.borderRadius,
    this.width,
    this.height = 60.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final borderRadius = this.borderRadius ?? BorderRadius.circular(0);
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [secondarylightColor, secondaryColor],
              ),
              borderRadius: borderRadius),
          width: width,
          height: height,
          alignment: Alignment.center,
          child: child,
        ),
        Positioned.fill(
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: borderRadius,
              onTap: onPressed,
            ),
          ),
        ),
      ],
    );
  }
}

class common_button_gradient_primary extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;

  // final Color color1, color2;
  final BorderRadius? borderRadius;
  final double? width;
  final double height;

  const common_button_gradient_primary({
    Key? key,
    required this.onPressed,
    required this.child,
    // required this.color1,
    // required this.color2,
    this.borderRadius,
    this.width,
    this.height = 60.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final borderRadius = this.borderRadius ?? BorderRadius.circular(0);
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            // color: bg,
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [primarylightColor, primaryColor],
            ),
            borderRadius: borderRadius,
          ),
          width: width,
          height: height,
          alignment: Alignment.center,
          // padding: EdgeInsets.all(18),
          child: child,
        ),
        Positioned.fill(
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: borderRadius,
              onTap: onPressed,
            ),
          ),
        ),
      ],
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final double length; // Height for vertical, Width for horizontal
  final Color color;
  final double dashWidth;
  final double dashGap;
  final Axis direction;

  DashedLinePainter({
    required this.length,
    this.color = Colors.black,
    this.dashWidth = 5.0,
    this.dashGap = 3.0,
    this.direction = Axis.horizontal, // Default to horizontal
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    double start = 0;

    if (direction == Axis.horizontal) {
      while (start < length) {
        canvas.drawLine(Offset(start, 0), Offset(start + dashWidth, 0), paint);
        start += dashWidth + dashGap; // Move to next dash position
      }
    } else {
      while (start < length) {
        canvas.drawLine(Offset(0, start), Offset(0, start + dashWidth), paint);
        start += dashWidth + dashGap; // Move to next dash position
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

show_confirm(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(30))),
        content: Wrap(
          children: [
            Container(
              width: Get.width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: Get.height / 7,
                    child: Lottie.asset('assets/lottie/warning2.json', repeat: false),
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Container(
                    child: Text(
                      "Head's Up!",
                      style: TextStyle(fontFamily: font_bold, fontSize: 23),
                    ),
                  ),
                  Container(
                    child: Text(
                      "Are you sure to exit?",
                      style: TextStyle(fontFamily: font_semibold, color: Colors.black54, fontSize: 13),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            child: Text(
              "NO",
              style: TextStyle(color: Colors.black, fontFamily: font_semibold),
            ),
            onPressed: () {
              Get.back();
            },
          ),
          TextButton(
            child: Text("YES", style: TextStyle(color: Colors.black, fontFamily: font_semibold)),
            onPressed: () {
              Get.back();
              Get.back();
            },
          ),
        ],
      );
    },
  );
}

Color getRandomColor2() {
  final Random random = Random();
  return Color.fromARGB(
    255, // Alpha (opacity) value - keep it fully opaque
    random.nextInt(256), // Red
    random.nextInt(256), // Green
    random.nextInt(256), // Blue
  );
}

class StoryPage extends StatefulWidget {
  List data;
  int indx;

  StoryPage({required this.data, required this.indx});

  @override
  _StoryPageState createState() => _StoryPageState();
}

class _StoryPageState extends State<StoryPage> {
  late ValueNotifier<IndicatorAnimationCommand> indicatorAnimationController;

  @override
  void initState() {
    super.initState();
    indicatorAnimationController = ValueNotifier<IndicatorAnimationCommand>(IndicatorAnimationCommand.resume);
  }

  @override
  void dispose() {
    indicatorAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: textblackColor,
      // appBar: AppBar(toolbarHeight: 0,backgroundColor: textblackColor,surfaceTintColor: textblackColor,),
      body: Container(
        padding: EdgeInsets.only(top: 20),
        child: StoryPageView(
          initialPage: widget.indx,
          showShadow: true,
          indicatorHeight: 3.0,
          itemBuilder: (context, pageIndex, storyIndex) {
            return Stack(
              children: [
                Positioned.fill(
                  child: Container(color: Colors.black),
                ),
                Positioned.fill(
                  child: Image.network(
                    widget.data[pageIndex]['image'].toString(),
                    fit: BoxFit.contain,
                  ),
                ),
                Container(
                    // color: Colors.yellow,
                    padding: EdgeInsets.only(top: 44, left: 8),
                    child: Wrap(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Container(
                            //   height: 32,
                            //   width: 32,
                            //   decoration: BoxDecoration(
                            //     image: DecorationImage(
                            //       image: NetworkImage(widget.data[pageIndex]['url'].toString()),
                            //       fit: BoxFit.cover,
                            //     ),
                            //     shape: BoxShape.circle,
                            //   ),
                            // ),

                            const SizedBox(
                              width: 10,
                            ),
                            Container(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    // color: Colors.yellow,
                                    width: size.width / 1.35,
                                    child: Text(
                                      widget.data[pageIndex]['title'].toString(),
                                      style: TextStyle(
                                        fontSize: size.width / 25,
                                        color: Colors.white,
                                        fontFamily: "poppins_bold",
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Container(
                                    child: Text(
                                      widget.data[pageIndex]['date'].toString(),
                                      style: TextStyle(
                                        fontSize: size.width / 35,
                                        color: Colors.grey,
                                        fontFamily: "poppins_regular",
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    )),
              ],
            );
          },
          gestureItemBuilder: (context, pageIndex, storyIndex) {
            return Stack(children: [
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: const EdgeInsets.only(top: 32),
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    color: Colors.white,
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ),
            ]);
          },
          indicatorAnimationController: indicatorAnimationController,
          initialStoryIndex: (pageIndex) {
            print("----------ggg---" + pageIndex.toString());
            // if (pageIndex == 0) {
            //   return widget.indx;
            // }
            return 0;
          },
          pageLength: widget.data.length,
          storyLength: (int pageIndex) {
            return 1;
          },
          onPageLimitReached: () {
            Navigator.pop(context);
          },
        ),
      ),
    );
  }
}

//-----------------connectivity---------------------

// List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
// final Connectivity _connectivity = Connectivity();
//
// Future<void> initConnectivity() async {
//   late List<ConnectivityResult> result;
//   // Platform messages may fail, so we use a try/catch PlatformException.
//   try {
//     result = await _connectivity.checkConnectivity();
//   } on PlatformException catch (e) {
//     // developer.log('Couldn\'t check connectivity status', error: e);
//     return;
//   }
//
//   // If the widget was removed from the tree while the asynchronous platform
//   // message was in flight, we want to discard the reply rather than calling
//   // setState to update our non-existent appearance.
//   // if (!mounted) {
//   //   return Future.value(null);
//   // }
//
//   return _updateConnectionStatus(result);
// }

// Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
//
//     _connectionStatus = result;
//   // ignore: avoid_print
//   print('Connectivity changed: $_connectionStatus');
//   print('-----------------------${_connectionStatus[0].toString() == 'ConnectivityResult.none'}');
//   // _connectionStatus[0] == ConnectivityResult.none?dashboard(): get_appversion();
//   // Timer(Duration(seconds: 2), () {
//     // _connectionStatus[0].toString() == 'ConnectivityResult.none' ?Get.offAll(()=> downloaded_files()): get_appversion();
//   // });
// }

//-----------------connectivity---------------------

// -----------------------------------
// ------------------screen rec---------------------------------------------
//
// void preventScreenshotOn() async => await ScreenProtector.preventScreenshotOn();
//
// void preventScreenshotOff() async =>
//     await ScreenProtector.preventScreenshotOff();
//
// void addListenerPreventScreenshot() async {
//   ScreenProtector.addListener(() {
//     // Screenshot
//     debugPrint('Screenshot:');
//     // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
//     //   content: Text('Screenshot!'),
//     // ));
//
//     Get.to(() => screen_rec_prev());
//   }, (isCaptured) {
//     // Screen Record
//     debugPrint('Screen Record:');
//     // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
//     //   content: Text('Screen Record----!'),
//     // ));
//
//     Get.to(() => screen_rec_prev());
//
//   });
// }
//
// void removeListenerPreventScreenshot() async {
//   ScreenProtector.removeListener();
// }
//
// void protectDataLeakageOn() async {
//   if (Platform.isIOS) {
//     await ScreenProtector.protectDataLeakageWithColor(Colors.white);
//   } else if (Platform.isAndroid) {
//     await ScreenProtector.protectDataLeakageOn();
//   }
// }
// ------------------screen rec---------------------------------------------

class EmptyListWidget extends StatelessWidget {
  String content;

  EmptyListWidget({required this.content});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        LottieBuilder.asset(
          "assets/lotties/empty_list.json",
          height: Get.width / 3.5,
          repeat: false,
        ),
        Text(
          content,
          style: TextStyle(
              color: textblackColor,
              // fontFamily: font_rethik_medium,
              fontSize: Get.width / 28),
        ),
      ],
    );
  }
}

class linear_loader extends StatelessWidget {
  final double? width;
  final double height;

  const linear_loader({super.key, required this.width, required this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(5),
        child: LinearProgressIndicator(
          backgroundColor: Colors.black.withValues(alpha: 0.1),
          color: Colors.black.withValues(alpha: 0.05),
        ),
      ),
    );
  }
}

Color getindexColor(opacity, index) {
  Color clr1 = index % 2 == 0
      ? Color(0xff007CC7).withValues(alpha: opacity)
      // : (index - 4) % 5 == 0 ? Colors.orange.withValues(alpha: opacity)
      // : (index - 3) % 4 == 0 ? Colors.green.withValues(alpha: opacity)
      // : (index - 2) % 3 == 0 ? Colors.purple.withValues(alpha: opacity)
      // : (index - 1) % 2 == 0 ? Colors.blue.withValues(alpha: opacity)
      : Color(0xff46A3D4).withValues(alpha: opacity);

  return clr1; // Set the alpha value to 255 (fully opaque)
}

class common_button extends StatelessWidget {
  String title;
  Color bg, title_clr;
  bool isLoading;
  var tap, width;

  common_button({required this.title, required this.bg, required this.title_clr, required this.tap, required this.width, required this.isLoading});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: bg,
            borderRadius: BorderRadius.circular(10),
          ),
          width: width,
          alignment: Alignment.center,
          padding: EdgeInsets.all(18),
          child: isLoading
              ? Container(
                  height: 25,
                  width: 25,
                  child: CircularProgressIndicator(
                    color: textwhiteColor,
                  ),
                )
              : Text(
                  "${title}",
                  style: TextStyle(color: title_clr, fontWeight: FontWeight.w600, fontSize: Get.width / 22),
                ),
        ),
        Positioned.fill(
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(1000),
              onTap: tap,
            ),
          ),
        ),
      ],
    );
  }
}

get_device_info() async {
  DeviceInfoPlugin deviceInfo = await DeviceInfoPlugin();
  var device_id;

  if (Platform.isAndroid) {
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    print('Running on ${androidInfo.model}');
    device_id = androidInfo.id.toString();
  } else {
    IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
    print('Running on ${iosInfo.utsname.machine}');
    device_id = iosInfo.identifierForVendor.toString();
  }
  return device_id.toString();
}

class app_bar extends StatelessWidget {
  var drw_controll;

  app_bar({required this.drw_controll});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      // backgroundColor: Color(0xff00FFFFFF),
      backgroundColor: Color(0xff00000000),
      elevation: 0,
      centerTitle: true,
      title: Image.asset(
        'assets/logo/log_text_only_white.png',
        width: Get.width / 3.5,
      ),
      leading: IconButton(
        onPressed: () {
          drw_controll.currentState!.openDrawer();
        },
        icon: Icon(
          Icons.menu,
          color: primaryColor,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            // Get.to(()=> notifications());
          },
          icon: Icon(
            Icons.notifications_active,
            color: primaryColor,
          ),
        ),
        SizedBox(
          width: 10,
        ),
      ],
    );
  }
}

// -----------------------------------------------------------------
Color getRandomColor() {
  final Random random = Random();

  // Use a slightly wider range to allow for more variation
  int red = random.nextInt(100);   // 0-99
  int green = random.nextInt(100);
  int blue = random.nextInt(100);

  return Color.fromARGB(255, red, green, blue); // Fully opaque dark color
}

// -----------------------------------------------------------------
String removehtml_tags(html) {
  RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);
  String out = html.replaceAll(exp, '');
  return out;
}

String greeting() {
  var hour = DateTime.now().hour;
  if (hour < 12) {
    return 'Morning';
  }
  if (hour < 17) {
    return 'Afternoon';
  }
  return 'Evening';
}

// ------------------------------------------
String vimeo_decoder(url) {
  var result = url;
  // https://player.vimeo.com/455855485

  var leng = result.toString().split('/').length;

  if (leng < 4 || leng > 5) {
    result = "invalid vimeo url";
  } else {
    if (leng == 4) {
      print("kooooooooooooooi 1----");
      result = result.toString().split('/')[3].toString();
    } else {
      print("kooooooooooooooi 2----" + result.toString().split('/').length.toString());

      print("leng---------------" + result.toString().split('/')[4].toString().split('.').length.toString());
      print("else here------oo----" + result.toString().split('/')[4].toString().split('.')[0].toString());

      if (result.toString().startsWith("https://player.vimeo.com/video/")) {
        result = result.toString().split('/')[4].toString();

        print("edooo kooi------" + result.toString());
      } else if (result.toString().split('/')[4].toString().split('?').length > 1) {
        result = result.toString().split('/')[4].toString().split('?')[0].toString();
      } else if (result.toString().split('/')[4].toString().split('.').length.toString() == "1") {
        result = result.toString().split('/')[3].toString();
      } else {
        result = result.toString().split('/')[4].toString().split('.')[0].toString();
      }
    }
  }

  print("vim id---------------------" + result.toString());

  return result;
}

String youtube_decode(url) {
  var result = url;

  var leng = result.toString().split('=').length;

  if (leng < 2 || leng > 2) {
    result = "invalid youtube url";
  } else {
    result = result.toString().split('=')[1].toString();
  }

  return result;
}
// ------------------------------------------

Future<void> launchURL(String urll) async {
  try {
    final Uri uri = Uri.parse(urll);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      toast_error('Cannot open the link: $urll');
    }
  } catch (e) {
    print(e.toString());
    toast_error('Error occurred while launching the link: $e');
  }
}


// ------------------------------toast start------------------------
toast_warning(msg) async {
  Fluttertoast.showToast(msg: msg.toString(), toastLength: Toast.LENGTH_LONG, gravity: ToastGravity.CENTER, timeInSecForIosWeb: 1, backgroundColor: Colors.orange, textColor: Colors.white, fontSize: 16.0);
}

toast_error(msg) async {
  Fluttertoast.showToast(msg: msg.toString(), toastLength: Toast.LENGTH_LONG, gravity: ToastGravity.CENTER, timeInSecForIosWeb: 1, backgroundColor: Colors.red, textColor: Colors.white, fontSize: 16.0);
}

toast_success(msg) async {
  Fluttertoast.showToast(msg: msg.toString(), toastLength: Toast.LENGTH_LONG, gravity: ToastGravity.CENTER, timeInSecForIosWeb: 1, backgroundColor: Colors.green, textColor: Colors.white, fontSize: 16.0);
}

toast_info(msg) async {
  Fluttertoast.showToast(msg: msg.toString(), toastLength: Toast.LENGTH_LONG, gravity: ToastGravity.CENTER, timeInSecForIosWeb: 1, backgroundColor: Colors.blue, textColor: Colors.white, fontSize: 16.0);
}
// ------------------------------toast start------------------------

class loader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: primaryColor,
      ),
    );
  }
}

class loaderimg extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: Center(
        child: Lottie.asset(
          'assets/lotties/loader_main4.json',
          width: MediaQuery.of(context).size.width / 2,
        ),
      ),
    );
  }
}

String convertTimeStampToHumanDate(int timeStamp) {
  var dateToTimeStamp = DateTime.fromMillisecondsSinceEpoch(timeStamp * 1000);
  return DateFormat('dd-MM-yyyy').format(dateToTimeStamp);
}

String convertTimeStampToHumanHour(int timeStamp) {
  var dateToTimeStamp = DateTime.fromMillisecondsSinceEpoch(timeStamp * 1000);
  return DateFormat('HH:mm').format(dateToTimeStamp);
}

String readTimestamp(int timestamp) {
  var now = DateTime.now();
  var format = DateFormat('h:mm  a');
  var date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  var diff = now.difference(date);
  var time = '';

  if (diff.inSeconds <= 0 || diff.inSeconds > 0 && diff.inMinutes == 0 || diff.inMinutes > 0 && diff.inHours == 0 || diff.inHours > 0 && diff.inDays == 0) {
    time = format.format(date);
  } else if (diff.inDays > 0 && diff.inDays < 7) {
    if (diff.inDays == 1) {
      time = '${diff.inDays} Day ago';
    } else {
      time = '${diff.inDays} Days ago';
    }
  } else {
    if (diff.inDays == 7) {
      time = '${(diff.inDays / 7).floor()} Week ago';
    } else {
      time = '${(diff.inDays / 7).floor()} Weeks ago';
    }
  }

  return time;
}
// -----------------------------------------------------------------------------------

class ShowUp extends StatefulWidget {
  final Widget child;
  final int delay;

  ShowUp({required this.child, required this.delay});

  @override
  _ShowUpState createState() => _ShowUpState();
}

class _ShowUpState extends State<ShowUp> with TickerProviderStateMixin {
  late AnimationController _animController;
  late Animation<Offset> _animOffset;

  @override
  void initState() {
    super.initState();

    _animController = AnimationController(vsync: this, duration: Duration(milliseconds: 500)); // animation duration
    final curve = CurvedAnimation(curve: Curves.decelerate, parent: _animController);
    _animOffset = Tween<Offset>(begin: Offset(0.0, -0.35), end: Offset.zero).animate(curve);

    Timer(Duration(milliseconds: widget.delay), () {
      _animController.forward();
    });
  }

  @override
  void dispose() {
    super.dispose();
    _animController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animController,
      child: SlideTransition(
        position: _animOffset,
        child: widget.child,
      ),
    );
  }
}

class cust_elevatedbutton extends StatelessWidget {
  final BorderRadiusGeometry? borderRadius;
  final double? width;
  final double height;
  final Gradient gradient;
  final VoidCallback? onPressed;
  final Widget child;

  const cust_elevatedbutton({
    Key? key,
    required this.onPressed,
    required this.child,
    this.borderRadius,
    this.width,
    this.height = 60.0,
    this.gradient = const LinearGradient(colors: [Colors.cyan, Colors.indigo]),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final borderRadius = this.borderRadius ?? BorderRadius.circular(0);
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: borderRadius,
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          // primary: Colors.transparent,
          // shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: borderRadius),
        ),
        child: child,
      ),
    );
  }
}

class common_button2 extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final Color bg;
  final BorderRadius? borderRadius;
  final double? width;
  final double height;

  const common_button2({
    Key? key,
    required this.onPressed,
    required this.child,
    required this.bg,
    this.borderRadius,
    this.width,
    this.height = 60.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final borderRadius = this.borderRadius ?? BorderRadius.circular(0);
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: bg,
            borderRadius: borderRadius,
          ),
          width: width,
          height: height,
          alignment: Alignment.center,
          // padding: EdgeInsets.all(18),
          child: child,
        ),
        Positioned.fill(
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: borderRadius,
              onTap: onPressed,
            ),
          ),
        ),
      ],
    );
  }
}

class common_button_gradient extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final Color color1, color2;
  final BorderRadius? borderRadius;
  final double? width;
  final double height;

  const common_button_gradient({
    Key? key,
    required this.onPressed,
    required this.child,
    required this.color1,
    required this.color2,
    this.borderRadius,
    this.width,
    this.height = 60.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final borderRadius = this.borderRadius ?? BorderRadius.circular(0);
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            // color: bg,
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [color1, color2],
            ),
            borderRadius: borderRadius,
          ),
          width: width,
          height: height,
          alignment: Alignment.center,
          // padding: EdgeInsets.all(18),
          child: child,
        ),
        Positioned.fill(
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: borderRadius,
              onTap: onPressed,
            ),
          ),
        ),
      ],
    );
  }
}
