// import 'dart:async';
//
// import 'package:prepscale/components/constants.dart';
// import 'package:flutter/material.dart';
// import 'package:just_audio/just_audio.dart';
//
// class SimpleAudioPlayer extends StatefulWidget {
//   final String audioUrl, senderName;
//
//   const SimpleAudioPlayer({
//     Key? key,
//     required this.audioUrl,
//     required this.senderName,
//   }) : super(key: key);
//
//   @override
//   _SimpleAudioPlayerState createState() => _SimpleAudioPlayerState();
// }
//
// class _SimpleAudioPlayerState extends State<SimpleAudioPlayer> {
//   late AudioPlayer _audioPlayer;
//   bool _isPlaying = false;
//   Duration _currentPosition = Duration.zero;
//   Duration _totalDuration = Duration.zero;
//   late StreamSubscription<Duration> _positionSubscription;
//
//   @override
//   void initState() {
//     super.initState();
//     _audioPlayer = AudioPlayer();
//     _initializePlayer();
//   }
//
//   Future<void> _initializePlayer() async {
//     await _audioPlayer.setUrl(widget.audioUrl);
//     _audioPlayer.playerStateStream.listen((playerState) {
//       if (playerState.processingState == ProcessingState.completed) {
//         setState(() {
//           _isPlaying = false;
//           _currentPosition = Duration.zero;
//         });
//       }
//     });
//
//     // Listen for position updates
//     _positionSubscription = _audioPlayer.positionStream.listen((position) {
//       setState(() {
//         _currentPosition = position;
//         _totalDuration = _audioPlayer.duration ?? Duration.zero;
//       });
//     });
//   }
//
//   Future<void> _togglePlayPause() async {
//     if (_isPlaying) {
//       await _audioPlayer.pause();
//     } else {
//       await _audioPlayer.play();
//     }
//     setState(() {
//       _isPlaying = !_isPlaying;
//     });
//   }
//
//   String _formatDuration(Duration duration) {
//     String twoDigits(int n) => n.toString().padLeft(2, "0");
//     return "${twoDigits(duration.inMinutes)}:${twoDigits(duration.inSeconds.remainder(60))}";
//   }
//
//   @override
//   void dispose() {
//     _positionSubscription.cancel();
//     _audioPlayer.dispose();
//
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Row(
//           children: [
//             IconButton(
//               icon: Icon(
//                 _isPlaying ? Icons.pause : Icons.play_arrow,
//                 color: widget.senderName != "You" ? primaryColor : Colors.white,
//               ),
//               onPressed: _togglePlayPause,
//             ),
//             Expanded(
//                 child: Slider(
//               min: 0.0,
//               max: _totalDuration.inMilliseconds.toDouble(),
//               value: (_currentPosition.inMilliseconds.toDouble() >
//                       _totalDuration.inMilliseconds.toDouble())
//                   ? _totalDuration.inMilliseconds.toDouble()
//                   : _currentPosition.inMilliseconds.toDouble(),
//               onChanged: (value) async {
//                 final newPosition = Duration(milliseconds: value.toInt());
//                 await _audioPlayer.seek(newPosition);
//                 setState(() {
//                   _currentPosition = newPosition;
//                 });
//               },
//             )),
//             SizedBox(width: 8),
//             Text(
//               '${_formatDuration(_currentPosition)} / ${_formatDuration(_totalDuration)}',
//               style: TextStyle(
//                 fontSize: 12,
//                 color: widget.senderName == "You" ? Colors.white : Colors.black,
//               ),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
// }
