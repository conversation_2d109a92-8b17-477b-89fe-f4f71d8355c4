import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/notificationcontroller.dart';


class notifications extends StatefulWidget {

  @override
  State<notifications> createState() => _sessionsState();
}

class _sessionsState extends State<notifications> {

  Notificationcontroller controller = Get.put(Notificationcontroller());

  @override
  void initState() {
    // controller.get_notification();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Notificationcontroller>(
        builder: (controller){
      return Scaffold(
        backgroundColor: textwhiteColor,
          body: Container(
            height: Get.height,
            width: Get.width,
            // decoration: BoxDecoration(gradient: bgGradient),
            child:
            // controller.isloading ? Center(child: CircularProgressIndicator(color: primaryColor,),) :
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

                  const SizedBox(height: 5,),

                  AppBar(
                    backgroundColor: Colors.transparent,
                    surfaceTintColor: Colors.transparent,
                    automaticallyImplyLeading: false,
                    leadingWidth: 45,
                    systemOverlayStyle: SystemUiOverlayStyle(statusBarBrightness: Brightness.light),
                    leading: GestureDetector(
                      onTap: () { Get.back(); },
                      child: Container(
                        margin: EdgeInsets.only(left: 10),
                        // width: 40,
                        //
                        // padding: EdgeInsets.all(18),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.transparent,
                          border: Border.all(color: textblackColor.withValues(alpha: 0.1)),
                        ),
                        child: Icon(CupertinoIcons.left_chevron),
                      ),
                    ),
                    title: Text("Notifications",style: TextStyle(fontSize: Get.width/23,fontFamily: font_semibold),),

                    centerTitle: true,
                  ),

                  const SizedBox(height: 20,),

                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text("Today",style: TextStyle(fontSize: Get.width/23,fontFamily: font_semibold),),
                  ),
                  // controller.notifications.isNotEmpty
                  //     ?
                  ListView.builder(
                      shrinkWrap: true,
                      itemCount: 1,
                      physics: ScrollPhysics(),
                      padding: EdgeInsets.all(0),
                      itemBuilder: (_,index){

                        Map data = controller.notifications[index];

                        return Container(
                            margin: EdgeInsets.only(bottom: 10,right: 15,left: 15),
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: textwhiteColor,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(color: textblackColor.withValues(alpha: 0.1)),
                              // boxShadow: [BoxShadow(
                              //   color: Colors.black.withValues(alpha: 0.05),
                              //   blurRadius: 10,
                              //   spreadRadius: 1
                              // ),],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Image.asset('assets/icons/bell_noti.png',width: Get.width/9,),
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: Get.width/1.5,
                                          // color: Colors.yellow,
                                          child: Text(data['title'].toString(),style: TextStyle(color: primaryColor,fontWeight: FontWeight.w600,fontSize: Get.width/26),),
                                        ),
                                        SizedBox(height: 12,),

                                        Container(
                                          width: Get.width/1.5,
                                          // color: Colors.yellow,
                                          child: Text(data['description'].toString(),style: TextStyle(color: Colors.black,fontWeight: FontWeight.w400,fontSize: Get.width/28),),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),

                                SizedBox(height: 25,),

                                Container(
                                  // color: Colors.yellow,
                                  alignment: Alignment.centerRight,
                                  child: Text(data['date'].toString(),style: TextStyle(color: Colors.black54,fontWeight: FontWeight.w400,fontSize: Get.width/28),),
                                ),

                              ],
                            )
                        );
                      }),
                  //     :
                  // Container(
                  //   height: Get.width,
                  //   width: Get.width,
                  //   alignment: Alignment.center,
                  //   child: Text(" No Notification Found Yet...."),
                  // ),
                  const SizedBox(height: 20,),

                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text("Yesterday",style: TextStyle(fontSize: Get.width/23,fontFamily: font_semibold),),
                  ),
                  // controller.notifications.isNotEmpty
                  //     ?
                  ListView.builder(
                      shrinkWrap: true,
                      itemCount: 2,
                      physics: ScrollPhysics(),
                      padding: EdgeInsets.all(0),
                      itemBuilder: (_,index){

                        Map data = controller.notifications[index];

                        return Container(
                            margin: EdgeInsets.only(bottom: 10,right: 12,left: 12),
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: textblackColor.withOpacity(0.05),
                              borderRadius: BorderRadius.circular(10),
                              // border: Border.all(color: textblackColor.withValues(alpha: 0.1)),
                              // boxShadow: [BoxShadow(
                              //   color: Colors.black.withValues(alpha: 0.05),
                              //   blurRadius: 10,
                              //   spreadRadius: 1
                              // ),],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Image.asset('assets/icons/bell_noti.png',width: Get.width/9,),
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: Get.width/1.5,
                                          // color: Colors.yellow,
                                          child: Text(data['title'].toString(),style: TextStyle(color: primaryColor,fontWeight: FontWeight.w600,fontSize: Get.width/26),),
                                        ),
                                        SizedBox(height: 12,),

                                        Container(
                                          width: Get.width/1.5,
                                          // color: Colors.yellow,
                                          child: Text(data['description'].toString(),style: TextStyle(color: Colors.black,fontWeight: FontWeight.w400,fontSize: Get.width/28),),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),

                                SizedBox(height: 25,),

                                Container(
                                  // color: Colors.yellow,
                                  alignment: Alignment.centerRight,
                                  child: Text(data['date'].toString(),style: TextStyle(color: Colors.black54,fontWeight: FontWeight.w400,fontSize: Get.width/28),),
                                ),

                              ],
                            )
                        );
                      }),
                  //     :
                  // Container(
                  //   height: Get.width,
                  //   width: Get.width,
                  //   alignment: Alignment.center,
                  //   child: Text(" No Notification Found Yet...."),
                  // ),



                  const SizedBox(height: 30,),



                ],
              ),
            ),


          )
      );
    });
  }
}
