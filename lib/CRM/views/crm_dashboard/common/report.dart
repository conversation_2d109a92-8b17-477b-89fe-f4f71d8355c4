import 'dart:io';

import 'package:edutalim/CRM/controller/crmreportcontroller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lottie/lottie.dart';

import '../../../../components/constants.dart';
import '../../../../components/utils.dart';

class report extends StatefulWidget {
  @override
  State<report> createState() => _reportState();
}

class _reportState extends State<report> {
  CRMReportController controller = Get.put(CRMReportController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    controller.get_report();
    // filter_bottom_sheet();
    // bottomtempcall();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CRMReportController>(builder: (context) {
      return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Color(0xff00ffffff),
          surfaceTintColor: Colors.transparent,
          iconTheme: IconThemeData(
            color: Colors.black,
          ),
          title: Text(
            "Report",
            style: TextStyle(color: Colors.black),
          ),
          centerTitle: true,
          automaticallyImplyLeading: true,
          elevation: 0,
        ),
        body: Container(
          height: Get.height,
          color: Colors.black.withValues(alpha: 0.1),
          child: Column(
            children: [
              Expanded(
                  child: controller.isLoading
                      ? loader()
                      : SingleChildScrollView(
                          physics: ScrollPhysics(),
                          child: Column(
                            children: [
                              // ------------------ leads count --------------------------
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                                margin: EdgeInsets.only(top: 4),
                                color: Colors.white,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      child: Text(
                                        "Leads Count by status",
                                        style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Container(
                                      child: GridView.builder(
                                        physics: NeverScrollableScrollPhysics(),
                                        padding: EdgeInsets.all(0),
                                        shrinkWrap: true,
                                        itemCount: controller.reportdata[0]['lead_overview'].length,
                                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                          childAspectRatio: 2 / 1.5,
                                          crossAxisCount: 3,
                                          mainAxisSpacing: 10,
                                          crossAxisSpacing: 10,
                                        ),
                                        itemBuilder: (context, index) {
                                          var clr = getRandomColor();
                                          return GestureDetector(
                                            onTap: () {
                                              // Get.to(() => leads_list(
                                              //       member_id: GetStorage().read('user_id').toString(),
                                              //       from_id: reportdata[0]['lead_overview'][index]['id'].toString(),
                                              //       countryId: '',
                                              //       sourceId: '',
                                              //       from: 'Leads',
                                              //     ));
                                            },
                                            child: Container(
                                              decoration: BoxDecoration(color: Colors.white, border: Border.all(color: clr.withValues(alpha: 1), width: 0.5), borderRadius: BorderRadius.circular(10)),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    color: Colors.transparent,
                                                    child: Text(
                                                      controller.reportdata[0]['lead_overview'][index]['title'].toString(),
                                                      style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w400, fontSize: Get.width / 32),
                                                      textAlign: TextAlign.center,
                                                    ),
                                                  ),
                                                  Container(
                                                    color: Colors.transparent,
                                                    child: Text(
                                                      // formatnumber(int.parse(
                                                      controller.reportdata[0]['lead_overview'][index]['count'].toString()
                                                      // ))
                                                      ,
                                                      style: TextStyle(color: clr, fontWeight: FontWeight.w700, fontSize: Get.width / 20),
                                                      textAlign: TextAlign.center,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              ////---------------------------account total------------------------------------------
                              //  // ------------------ profit --------------------------
                              //   if(GetStorage().read('current_role').toString() != "academic_counsellor")
                              //
                              //   Container(
                              //     padding: EdgeInsets.symmetric(horizontal: 20,vertical: 15),
                              //     margin: EdgeInsets.only(top: 4),
                              //     color: Colors.white,
                              //     child: Column(
                              //       crossAxisAlignment: CrossAxisAlignment.start,
                              //       children: [
                              //
                              //         Container(
                              //           child: Text("Accounts",style: TextStyle(color: Colors.black,fontWeight: FontWeight.w500),),
                              //         ),
                              //         SizedBox(height: 15,),
                              //         Container(
                              //           child: GridView(
                              //             physics: NeverScrollableScrollPhysics(),
                              //             padding: EdgeInsets.all(0),
                              //             shrinkWrap: true,
                              //             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                              //               childAspectRatio: 2/0.7,
                              //               crossAxisCount: 2,
                              //               mainAxisSpacing: 10,
                              //               crossAxisSpacing: 10,
                              //             ),
                              //
                              //             children: [
                              //
                              //               Container(
                              //                 decoration: BoxDecoration(
                              //                     color: Colors.green,
                              //                     border: Border.all(color: Colors.green,
                              //                         width: 0.5),
                              //                     borderRadius: BorderRadius.circular(10)
                              //                 ),
                              //                 child: Column(
                              //                   crossAxisAlignment: CrossAxisAlignment.center,
                              //                   mainAxisAlignment: MainAxisAlignment.center,
                              //                   children: [
                              //
                              //                     Container(
                              //                       color: Colors.transparent,
                              //                       child: Text('Total Revenue',style: TextStyle(color: Colors.white, fontWeight: FontWeight.w400,fontSize: Get.width/32),textAlign: TextAlign.center,),
                              //                     ),
                              //
                              //                     Container(
                              //                       color: Colors.transparent,
                              //                       child: Text("₹"+formatnumber(int.parse(reportdata[0]['accounts']['total_revenue'].toString())),style: TextStyle(color: Colors.white, fontWeight: FontWeight.w700,fontSize: Get.width/20,fontFamily: ''),textAlign: TextAlign.center,),
                              //                     ),
                              //
                              //                   ],
                              //                 ),
                              //               ),
                              //
                              //               Container(
                              //                 decoration: BoxDecoration(
                              //                     color: Colors.red,
                              //                     border: Border.all(color: Colors.red,
                              //                         width: 0.5),
                              //                     borderRadius: BorderRadius.circular(10)
                              //                 ),
                              //                 child: Column(
                              //                   crossAxisAlignment: CrossAxisAlignment.center,
                              //                   mainAxisAlignment: MainAxisAlignment.center,
                              //                   children: [
                              //
                              //                     Container(
                              //                       color: Colors.transparent,
                              //                       child: Text('Pending amount',style: TextStyle(color: Colors.white, fontWeight: FontWeight.w400,fontSize: Get.width/32),textAlign: TextAlign.center,),
                              //                     ),
                              //
                              //                     Container(
                              //                       color: Colors.transparent,
                              //                       child: Text("₹"+formatnumber(int.parse(reportdata[0]['accounts']['pending_amount'].toString())),style: TextStyle(color: Colors.white, fontWeight: FontWeight.w700,fontSize: Get.width/20,fontFamily: ''),textAlign: TextAlign.center,),
                              //                     ),
                              //
                              //                   ],
                              //                 ),
                              //               ),
                              //
                              //             ],
                              //
                              //
                              //           ),
                              //         ),
                              //
                              //       ],
                              //     ),
                              //   ),
                              ////---------------------------account total------------------------------------------

                              // ------------------ top leads sourc--------------------------
                              if (GetStorage().read('current_role').toString() != "academic_counsellor" && controller.reportdata[0]['lead_sources'].isNotEmpty)
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                                  margin: EdgeInsets.only(top: 4),
                                  color: Colors.white,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        child: Text(
                                          "Top 5 Lead sources",
                                          style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 5,
                                      ),
                                      Divider(),
                                      Container(
                                        child: ListView.builder(
                                          physics: NeverScrollableScrollPhysics(),
                                          padding: EdgeInsets.all(0),
                                          shrinkWrap: true,
                                          itemCount: controller.reportdata[0]['lead_sources'].length,
                                          itemBuilder: (context, index) {
                                            var clr = getRandomColor();
                                            return GestureDetector(
                                              onTap: () {
                                                // Get.to(() => leads_list(
                                                //       member_id: GetStorage().read('user_id').toString(),
                                                //       from_id: '',
                                                //       sourceId: reportdata[0]['lead_sources'][index]['id'].toString(),
                                                //       countryId: '',
                                                //       from: 'Leads',
                                                //     ));
                                              },
                                              child: Container(
                                                // decoration: BoxDecoration(
                                                color: Colors.black.withValues(alpha: 0.02),
                                                //     border: Border.all(color: clr.withValues(alpha: 1),
                                                //         width: 0.5),
                                                //     borderRadius: BorderRadius.circular(10)
                                                // ),
                                                margin: EdgeInsets.only(bottom: 10),
                                                padding: EdgeInsets.all(3),
                                                child: Row(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Icon(
                                                          Icons.circle,
                                                          size: 10,
                                                          color: Colors.black12,
                                                        ),
                                                        SizedBox(
                                                          width: 15,
                                                        ),
                                                        Container(
                                                          color: Colors.transparent,
                                                          child: Text(
                                                            controller.reportdata[0]['lead_sources'][index]['title'].toString(),
                                                            style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500, fontSize: Get.width / 30),
                                                            textAlign: TextAlign.center,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    Container(
                                                      color: Colors.transparent,
                                                      child: Text(
                                                        controller.reportdata[0]['lead_sources'][index]['count'].toString(),
                                                        style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500, fontSize: Get.width / 30),
                                                        textAlign: TextAlign.center,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                              // ------------------ leads makers --------------------------
                              if (GetStorage().read('current_role').toString() != "academic_counsellor" && controller.reportdata[0]['lead_makers'].isNotEmpty)
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                                  margin: EdgeInsets.only(top: 4),
                                  color: Colors.white,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        child: Text(
                                          "Top 5 Lead makers",
                                          style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 15,
                                      ),
                                      Container(
                                        child: ListView.builder(
                                          physics: NeverScrollableScrollPhysics(),
                                          padding: EdgeInsets.all(0),
                                          shrinkWrap: true,
                                          itemCount: controller.reportdata[0]['lead_makers'].length,
                                          itemBuilder: (context, index) {
                                            var clr = getRandomColor();
                                            return GestureDetector(
                                              onTap: () {
                                                // Get.to(() => leads_list(
                                                //       member_id: controller.reportdata[0]['lead_makers'][index]['id'].toString(),
                                                //       from_id: '',
                                                //       countryId: '',
                                                //       sourceId: '',
                                                //       from: 'Leads',
                                                //     ));
                                              },
                                              child: Container(
                                                decoration: BoxDecoration(color: Colors.white, border: Border.all(color: clr.withValues(alpha: 1), width: 0.5), borderRadius: BorderRadius.circular(10)),
                                                margin: EdgeInsets.only(bottom: 10),
                                                padding: EdgeInsets.all(10),
                                                child: Row(
                                                  crossAxisAlignment: CrossAxisAlignment.center,
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Container(
                                                          width: Get.width / 10,
                                                          height: Get.width / 10,
                                                          child: ClipRRect(
                                                              borderRadius: BorderRadius.circular(1000),
                                                              child: Image.network(
                                                                controller.reportdata[0]['lead_makers'][index]['user_profile'].toString(),
                                                                errorBuilder: (a, b, c) {
                                                                  return Image.asset(
                                                                    'assets/images/avater_placeholder.jpg',
                                                                  );
                                                                },
                                                              )),
                                                        ),
                                                        SizedBox(
                                                          width: 10,
                                                        ),
                                                        Column(
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          children: [
                                                            Container(
                                                              // color: Colors.yellow,
                                                              width: Get.width / 1.7,
                                                              child: Text(
                                                                controller.reportdata[0]['lead_makers'][index]['name'].toString(),
                                                                style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500, fontSize: Get.width / 26),
                                                                textAlign: TextAlign.left,
                                                              ),
                                                            ),
                                                            Container(
                                                              width: Get.width / 1.7,
                                                              color: Colors.transparent,
                                                              child: Text(
                                                                controller.reportdata[0]['lead_makers'][index]['phone'].toString(),
                                                                style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w500, fontSize: Get.width / 30),
                                                                textAlign: TextAlign.left,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                    Container(
                                                      color: Colors.transparent,
                                                      child: Text(
                                                        controller.reportdata[0]['lead_makers'][index]['count'].toString(),
                                                        style: TextStyle(color: clr, fontWeight: FontWeight.w700, fontSize: Get.width / 20),
                                                        textAlign: TextAlign.center,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                              // ------------------ leads makers --------------------------
                              // if(Platform.isAndroid)
                              //   Container(
                              //   padding: EdgeInsets.symmetric(horizontal: 20,vertical: 15),
                              //   margin: EdgeInsets.only(top: 4),
                              //   color: Colors.white,
                              //   child: Column(
                              //     crossAxisAlignment: CrossAxisAlignment.start,
                              //     children: [
                              //
                              //       Container(
                              //         child: Text("Call Overview",style: TextStyle(color: Colors.black,fontWeight: FontWeight.w500),),
                              //       ),
                              //       SizedBox(height: 15,),
                              //
                              //       Container(
                              //         child: call_log_overview(),
                              //       ),
                              //
                              //     ],
                              //   ),
                              // ),

                              // ------------------ top emigration country--------------------------
                              if (GetStorage().read('current_role').toString() != "academic_counsellor" && controller.reportdata[0]['emigration_countries'].isNotEmpty)
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                                  margin: EdgeInsets.only(top: 4),
                                  color: Colors.white,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        child: Text(
                                          "Top 5 Emigration countries",
                                          style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 5,
                                      ),
                                      Divider(),
                                      Container(
                                        child: ListView.builder(
                                          physics: NeverScrollableScrollPhysics(),
                                          padding: EdgeInsets.all(0),
                                          shrinkWrap: true,
                                          itemCount: controller.reportdata[0]['emigration_countries'].length,
                                          itemBuilder: (context, index) {
                                            var clr = getRandomColor();
                                            return GestureDetector(
                                              onTap: () {
                                                // Get.to(() => leads_list(
                                                //       member_id: GetStorage().read('user_id').toString(),
                                                //       from_id: '',
                                                //       countryId: controller.reportdata[0]['emigration_countries'][index]['id'].toString(),
                                                //       sourceId: '',
                                                //       from: 'Leads',
                                                //     ));
                                              },
                                              child: Container(
                                                // decoration: BoxDecoration(
                                                color: Colors.black.withValues(alpha: 0.02),
                                                //     border: Border.all(color: clr.withValues(alpha: 1),
                                                //         width: 0.5),
                                                //     borderRadius: BorderRadius.circular(10)
                                                // ),
                                                margin: EdgeInsets.only(bottom: 10),
                                                padding: EdgeInsets.all(3),
                                                child: Row(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Icon(
                                                          Icons.circle,
                                                          size: 10,
                                                          color: Colors.black12,
                                                        ),
                                                        SizedBox(
                                                          width: 15,
                                                        ),
                                                        Container(
                                                          color: Colors.transparent,
                                                          child: Text(
                                                            controller.reportdata[0]['emigration_countries'][index]['title'].toString(),
                                                            style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500, fontSize: Get.width / 30),
                                                            textAlign: TextAlign.center,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    Container(
                                                      color: Colors.transparent,
                                                      child: Text(
                                                        controller.reportdata[0]['emigration_countries'][index]['count'].toString(),
                                                        style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500, fontSize: Get.width / 30),
                                                        textAlign: TextAlign.center,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        )),
            ],
          ),
        ),
      );
    });
  }

  // filter bottom sheet sec
  void filter_bottom_sheet() {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      useSafeArea: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      builder: (builder) {
        return GetBuilder<CRMReportController>(
          builder: (controller) {
            return Padding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Stack(
                children: [
                  Container(
                    constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height / 1.1),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 30,
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Image.asset(
                                'assets/icons/filter.png',
                                width: Get.width / 18,
                                color: Colors.black,
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              Container(
                                  // color: Colors.yellow,
                                  child: Text(
                                "Filter",
                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 23, fontWeight: FontWeight.w600, color: Colors.black),
                              )),
                            ],
                          ),
                        ),
                        Divider(),
                        Expanded(
                          child: SingleChildScrollView(
                            physics: ScrollPhysics(),
                            child: Wrap(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // -----------------------------sort  sec--------------------------------------------------------
                                      if (controller.sel_section.toString() == "all" || controller.sel_section.toString() == "date")
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 10),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                  // padding: EdgeInsets.symmetric(horizontal: 15),
                                                  // color: Colors.yellow,
                                                  child: Text(
                                                "Date",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left,
                                              )),
                                              SizedBox(
                                                height: 20,
                                              ),
                                              Container(
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Text(
                                                        "From date :",
                                                        style: TextStyle(color: Colors.black54, fontSize: Get.width / 34),
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 12,
                                                    ),
                                                    Expanded(
                                                      child: Text(
                                                        "To date :",
                                                        style: TextStyle(color: Colors.black54, fontSize: Get.width / 34),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(
                                                height: 8,
                                              ),
                                              Container(
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Container(
                                                        // height: 40,
                                                        child: TextField(
                                                          onChanged: (val) {
                                                            // fetleadsby_type();
                                                          },
                                                          onTap: () {
                                                            print('helooooo.....');
                                                            _DatePicker(context, 'from');
                                                          },
                                                          readOnly: true,
                                                          style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                                                          controller: controller.follow_from_date,
                                                          decoration: InputDecoration(
                                                            focusedBorder: OutlineInputBorder(
                                                              borderSide: BorderSide(color: Colors.blue, width: 1),
                                                              borderRadius: BorderRadius.circular(10.0),
                                                            ),
                                                            enabledBorder: OutlineInputBorder(
                                                              borderSide: BorderSide(color: Colors.blue, width: 1),
                                                              borderRadius: BorderRadius.circular(10.0),
                                                            ),
                                                            filled: true,
                                                            hintStyle: TextStyle(color: Colors.black, fontFamily: "poppins_regular"),
                                                            hintText: controller.from_date.toString(),
                                                            fillColor: Colors.blueAccent.withValues(alpha: 0.2),
                                                            contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                                                            suffixIcon: controller.from_date.toString() == "All"
                                                                ? Container(
                                                                    width: 0,
                                                                    height: 0,
                                                                  )
                                                                : IconButton(
                                                                    onPressed: () {
                                                                      print("rttested------");

                                                                        controller.follow_from_date.text = "All";
                                                                        controller.from_date = "All";
                                                                      controller.update();
                                                                      // fetleadsby_type();
                                                                    },
                                                                    icon: Icon(
                                                                      Icons.close,
                                                                      color: Colors.black,
                                                                    ),
                                                                  ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 12,
                                                    ),
                                                    Expanded(
                                                      child: Container(
                                                        // height: 40,
                                                        child: TextField(
                                                          onChanged: (val) {
                                                            // fetleadsby_type();
                                                          },
                                                          onTap: () {
                                                            print('helooooo.....');
                                                            _DatePicker(context, 'to');
                                                          },
                                                          readOnly: true,
                                                          style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                                                          controller: controller.follow_to_date,
                                                          decoration: InputDecoration(
                                                            focusedBorder: OutlineInputBorder(
                                                              borderSide: BorderSide(color: Colors.blue, width: 1),
                                                              borderRadius: BorderRadius.circular(10.0),
                                                            ),
                                                            enabledBorder: OutlineInputBorder(
                                                              borderSide: BorderSide(color: Colors.blue, width: 1),
                                                              borderRadius: BorderRadius.circular(10.0),
                                                            ),
                                                            filled: true,
                                                            hintStyle: TextStyle(color: Colors.black, fontFamily: "poppins_regular"),
                                                            hintText: controller.to_date.toString(),
                                                            fillColor: Colors.blueAccent.withValues(alpha: 0.2),
                                                            contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                                                            suffixIcon: controller.to_date.toString() == "All"
                                                                ? Container(
                                                                    width: 0,
                                                                    height: 0,
                                                                  )
                                                                : IconButton(
                                                                    onPressed: () {
                                                                      print("rttested------");

                                                                        controller.follow_to_date.text = "All";
                                                                        controller.to_date = "All";
                                                                      controller.update();
                                                                      // fetleadsby_type();
                                                                    },
                                                                    icon: Icon(
                                                                      Icons.close,
                                                                      color: Colors.black,
                                                                    ),
                                                                  ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(
                                                height: 20,
                                              ),
                                            ],
                                          ),
                                        ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      if (controller.sel_section.toString() == "all" || controller.sel_section.toString() == "status")
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 10),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                  // padding: EdgeInsets.symmetric(horizontal: 15),
                                                  // color: Colors.yellow,
                                                  child: Text(
                                                "Status",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left,
                                              )),
                                              SizedBox(
                                                height: 20,
                                              ),
                                              Container(
                                                width: MediaQuery.of(context).size.width,
                                                decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                                child: DropdownButtonHideUnderline(
                                                  child: ButtonTheme(
                                                    alignedDropdown: true,
                                                    child: DropdownButton<String>(
                                                      value: controller.sel_status,
                                                      iconSize: 30,
                                                      // icon: (null),
                                                      style: TextStyle(
                                                        color: Colors.black54,
                                                        fontSize: 14,
                                                      ),
                                                      hint: Text('- select a status -'),
                                                      onChanged: (newValue) async {
                                                        setState(() {
                                                          controller.sel_status = newValue;
                                                        });
                                                      },
                                                      items: controller.reportdata[0]['statuses'].map<DropdownMenuItem<String>>((item) {
                                                        return DropdownMenuItem<String>(
                                                          child: Container(
                                                            width: MediaQuery.of(context).size.width / 1.6,
                                                            child: Text(
                                                              item['title'].toString(),
                                                              style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27),
                                                              maxLines: 2,
                                                            ),
                                                          ),
                                                          value: item['id'].toString(),
                                                        );
                                                      }).toList(),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                height: 20,
                                              ),
                                            ],
                                          ),
                                        ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      if (controller.sel_section.toString() == "all" || controller.sel_section.toString() == "team")
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 10),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                  // padding: EdgeInsets.symmetric(horizontal: 15),
                                                  // color: Colors.yellow,
                                                  child: Text(
                                                "Team",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left,
                                              )),
                                              SizedBox(
                                                height: 20,
                                              ),
                                              Container(
                                                width: MediaQuery.of(context).size.width,
                                                decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                                child: DropdownButtonHideUnderline(
                                                  child: ButtonTheme(
                                                    alignedDropdown: true,
                                                    child: DropdownButton<String>(
                                                      value: controller.sel_team,
                                                      iconSize: 30,
                                                      // icon: (null),
                                                      style: TextStyle(
                                                        color: Colors.black54,
                                                        fontSize: 14,
                                                      ),
                                                      hint: Text('- select a team -'),
                                                      onChanged: (newValue) async {
                                                        setState(() {
                                                          controller.sel_team = newValue;
                                                        });
                                                      },
                                                      items: controller.reportdata[0]['teams'].map<DropdownMenuItem<String>>((item) {
                                                        return DropdownMenuItem<String>(
                                                          child: Container(
                                                            width: MediaQuery.of(context).size.width / 1.6,
                                                            child: Text(
                                                              item['title'].toString(),
                                                              style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27),
                                                              maxLines: 2,
                                                            ),
                                                          ),
                                                          value: item['id'].toString(),
                                                        );
                                                      }).toList(),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                height: 20,
                                              ),
                                            ],
                                          ),
                                        ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      if (controller.sel_section.toString() == "all" || controller.sel_section.toString() == "sourse")
                                        Container(
                                          padding: EdgeInsets.symmetric(horizontal: 10),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                  // padding: EdgeInsets.symmetric(horizontal: 15),
                                                  // color: Colors.yellow,
                                                  child: Text(
                                                "Source",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left,
                                              )),
                                              SizedBox(
                                                height: 20,
                                              ),
                                              Container(
                                                width: MediaQuery.of(context).size.width,
                                                decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                                child: DropdownButtonHideUnderline(
                                                  child: ButtonTheme(
                                                    alignedDropdown: true,
                                                    child: DropdownButton<String>(
                                                      value: controller.sel_sourse,
                                                      iconSize: 30,
                                                      // icon: (null),
                                                      style: TextStyle(
                                                        color: Colors.black54,
                                                        fontSize: 14,
                                                      ),
                                                      hint: Text('- select a source -'),
                                                      onChanged: (newValue) async {
                                                        setState(() {
                                                          controller.sel_sourse = newValue;
                                                        });
                                                      },
                                                      items: controller.reportdata[0]['sources'].map<DropdownMenuItem<String>>((item) {
                                                        return DropdownMenuItem<String>(
                                                          child: Container(
                                                            width: MediaQuery.of(context).size.width / 1.6,
                                                            child: Text(
                                                              item['title'].toString(),
                                                              style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27),
                                                              maxLines: 2,
                                                            ),
                                                          ),
                                                          value: item['id'].toString(),
                                                        );
                                                      }).toList(),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                height: 20,
                                              ),
                                            ],
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    Get.back();
                                    controller.get_report();
                                  },
                                  child: Container(
                                    width: Get.width / 4,
                                    decoration: BoxDecoration(
                                      color: primaryColor,
                                      border: Border.all(color: primaryColor, width: 1),
                                      borderRadius: BorderRadius.circular(1000),
                                    ),
                                    padding: EdgeInsets.symmetric(vertical: 15),
                                    child: Text(
                                      "Apply",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(color: Colors.white, fontFamily: "poppins_regular", fontWeight: FontWeight.w600),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                      right: 0,
                      left: 0,
                      top: 0,
                      child: GestureDetector(
                        onTap: () {
                          // Get.back();
                        },
                        child: Container(color: Colors.transparent, child: Image.asset('assets/icons/drag_handle.png')),
                      )),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _DatePicker(ctx, type) {
    if (type.toString() == "from") {
      controller.follow_from_date.text = DateTime.now().day.toString() + "-" + DateTime.now().month.toString() + "-" + DateTime.now().year.toString();
      setState(() {
        controller.from_date = DateTime.now().day.toString() + "-" + DateTime.now().month.toString() + "-" + DateTime.now().year.toString();
      });
    } else {
      controller.follow_to_date.text = DateTime.now().day.toString() + "-" + DateTime.now().month.toString() + "-" + DateTime.now().year.toString();
      setState(() {
        controller.to_date = DateTime.now().day.toString() + "-" + DateTime.now().month.toString() + "-" + DateTime.now().year.toString();
      });
    }

    // showCupertinoModalPopup is a built-in function of the cupertino library
    showCupertinoModalPopup(
        context: ctx,
        builder: (_) => GetBuilder<CRMReportController>(builder: (controller) {
              return Container(
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                height: 410,
                child: Column(
                  children: [
                    Container(
                      width: Get.width,
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(top: 20, bottom: 20),
                      decoration: BoxDecoration(color: Colors.lightBlueAccent, borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
                      child: Text(
                        'Scrool to select',
                        style: TextStyle(fontSize: 15, color: Colors.white, decoration: TextDecoration.none, fontFamily: ''),
                      ),
                    ),

                    SizedBox(
                      height: 290,
                      child: CupertinoDatePicker(
                          mode: CupertinoDatePickerMode.date,
                          initialDateTime: DateTime.now(),
                          onDateTimeChanged: (val) {
                            if (type.toString() == "from") {
                              controller.fromDate = val;
                              controller.follow_from_date.text = controller.fromDate!.day.toString() + "-" + controller.fromDate!.month.toString() + "-" + controller.fromDate!.year.toString();
                              controller.from_date = controller.fromDate!.day.toString() + "-" + controller.fromDate!.month.toString() + "-" + controller.fromDate!.year.toString();
                            } else {
                              controller.toDate = val;
                              controller.follow_to_date.text = controller.toDate!.day.toString() + "-" + controller.toDate!.month.toString() + "-" + controller.toDate!.year.toString();
                              controller.to_date = controller.toDate!.day.toString() + "-" + controller.toDate!.month.toString() + "-" + controller.toDate!.year.toString();
                            }
                            controller.update();
                          }),
                    ),

                    // Close the modal
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CupertinoButton(
                          child: const Text('Cancel'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                        CupertinoButton(
                          child: const Text('OK'),
                          onPressed: () {
                            Navigator.of(ctx).pop();
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }));
  }
}
