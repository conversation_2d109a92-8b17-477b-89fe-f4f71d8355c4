import 'dart:developer';

import 'package:dotted_line/dotted_line.dart';
import 'package:edutalim/LMS/controllers/planscontroller.dart';
import 'package:edutalim/LMS/views/dashboard/home/<USER>/teacher_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/homecontroller.dart';

class course_details extends StatefulWidget {
  String course_id;

  course_details({required this.course_id});

  @override
  State<course_details> createState() => _course_detailsState();
}

class _course_detailsState extends State<course_details> {
  Homecontroller controller = Get.put(Homecontroller());


  @override
  void dispose() {
    controller.coursedetails  = [];
    controller.iscoursedetailsLoad = false;
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    controller.getCourseDetails(widget.course_id);

    final now = DateTime.now();
    final formatted = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
    print(formatted); // Example: 2025-04-29 11:40:32
  }


  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    int previewLength = 150;

    return Builder(builder: (context) {
      return GetBuilder<Homecontroller>(builder: (controller) {
        return Scaffold(
          backgroundColor: textwhiteColor,
          body: controller.iscoursedetailsLoad
              ? loader()
              : Container(
                  width: Get.width,
                  height: Get.height,
                  color: textwhiteColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppBar(
                                toolbarHeight: 0,
                                backgroundColor: appbarwhiteColor,
                                surfaceTintColor: appbarwhiteColor,
                              ),
                              GestureDetector(
                                onTap: () {
                                  // Get.to(video_page());
                                },
                                child: Stack(
                                  clipBehavior: Clip.none, // Allows content to overflow
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                      child: Stack(
                                        children: [
                                          Container(
                                            padding: EdgeInsets.symmetric(horizontal: 0),
                                            child: ClipRRect(
                                              borderRadius: BorderRadius.circular(20),
                                              child: Image.network(
                                                controller.coursedetails[0]['thumbnail'].toString(),
                                                fit: BoxFit.cover,
                                                width: double.infinity,
                                                errorBuilder: (a, b, c) {
                                                  return Image.asset(
                                                    'assets/images/placeholder_rect.webp',
                                                    fit: BoxFit.cover,
                                                  );
                                                },
                                                height: Get.height / 4.1, // Adjust height as needed
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            bottom: 0,
                                            left: 0,
                                            right: 0,
                                            child: Container(
                                              height: 150,
                                              decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(20),
                                                  gradient: LinearGradient(
                                                      colors: [Colors.black, Colors.black.withOpacity(0.3), Colors.transparent],
                                                      begin: AlignmentDirectional.bottomCenter,
                                                      end: AlignmentDirectional.topCenter,
                                                      stops: [0, 0.5, 1])),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    Positioned(
                                      top: 20,
                                      left: 20,
                                      child: GestureDetector(
                                        onTap: () {
                                          Get.back();
                                        },
                                        child: Image.asset(
                                          'assets/icons/arrow_back.png',
                                          width: Get.width / 12,
                                          color: textwhiteColor,
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      bottom: 40,
                                      left: 20,
                                      right: 20,
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            // "Forex Trading Education",
                                            controller.coursedetails[0]["title"].toString(),
                                            style: TextStyle(fontSize: Get.width / 20, fontWeight: FontWeight.bold, color: Colors.white, fontFamily: font_bold),
                                          ),
                                          SizedBox(height: 27),
                                          Row(
                                            children: [
                                              // Generate star icons dynamically
                                              ...List.generate(5, (index) {
                                                double rating = double.tryParse(controller.coursedetails[0]["meta"]["reviews_rating"]?.toString() ?? '0') ?? 0;
                                                if (index < rating.floor()) {
                                                  return Icon(Icons.star_rounded, color: Colors.amber, size: 16);
                                                } else if (index < rating) {
                                                  return Icon(Icons.star_half_rounded, color: Colors.amber, size: 16);
                                                } else {
                                                  return Icon(Icons.star_border_rounded, color: Colors.amber, size: 16);
                                                }
                                              }),
                                              SizedBox(width: 10),
                                              Text(
                                                "${controller.coursedetails[0]["meta"]["reviews_rating"]?.toString() ?? '0'} ",
                                                style: TextStyle(color: Colors.white, fontSize: 14),
                                              ),
                                              SizedBox(width: 2),
                                              Expanded(
                                                child: Text(
                                                  "(${controller.coursedetails[0]["meta"]["reviews_count"]?.toString() ?? '0'} Reviews)",
                                                  style: TextStyle(color: Colors.white, fontSize: 14),
                                                ),
                                              ),
                                              SizedBox(width: 2),
                                              Text("|", style: TextStyle(color: Colors.white, fontSize: 14)),
                                              SizedBox(width: 2),
                                              Expanded(
                                                child: Text(
                                                  "${controller.coursedetails[0]["meta"]["total_enrollments"]?.toString() ?? '0'} Students",
                                                  style: TextStyle(color: Colors.white, fontSize: 14),
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(
                                            height: 10,
                                          )
                                        ],
                                      ),
                                    ),

                                    // Overlapping Rounded Container
                                    Positioned(
                                      bottom: -30, // Moves half of the container outside
                                      left: 0, right: 0,
                                      child: Container(
                                        // width: Get.width/,
                                        margin: EdgeInsets.symmetric(horizontal: Get.width / 11),
                                        decoration: BoxDecoration(
                                          color: textwhiteColor,
                                          borderRadius: BorderRadius.circular(1000), // Rounded border
                                          boxShadow: [
                                            BoxShadow(
                                              color: textblackColor.withOpacity(0.1),
                                              blurRadius: 10,
                                              spreadRadius: 1,
                                              offset: Offset(0, 2),
                                            )
                                          ],
                                        ),
                                        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                                        alignment: Alignment.center,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Image.asset('assets/icons/calender.png', width: 20, height: 20),
                                            SizedBox(
                                              width: 5,
                                            ),
                                            Expanded(
                                              child: Text(
                                                "Duration : ${controller.coursedetails[0]["meta"]["course_duration"]?.toString() ?? '---'} ",
                                                maxLines: 1,
                                                style: TextStyle(fontSize: Get.width * .034, fontWeight: FontWeight.bold),
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                List features = controller.coursedetails[0]['features'];
                                                String featureText = features.map((f) => "- $f").join('\n');
                                                Share.share("📘 Course Title: ${controller.coursedetails[0]['title']}\n\n"
                                                    "📝 Description:\n${removehtml_tags(controller.coursedetails[0]['description'])}\n\n"
                                                    "⏱ Duration: ${controller.coursedetails[0]['duration']} months\n"
                                                    "✨ Features:\n$featureText\n\n"
                                                    "💰 Price: ₹${controller.coursedetails[0]['price']}\n\n"
                                                    "📱 Download the App:\n"
                                                    "🔗 Android: ${androidLink}\n"
                                                    "🔗 iOS: ${iosLink}\n"
                                                    // "🌐 Web: ${baseurl}\n\n"
                                                    "$appName");
                                              },
                                              child: Image.asset('assets/icons/share.png', width: 80, height: 60),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 60,
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 15),
                                child: Container(
                                  child: Text(
                                    "Description",
                                    style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 24, fontFamily: font_bold),
                                  ),
                                ),
                              ),
                              Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        height: 5,
                                      ),
                                      // Description Text
                                      // Text("Forex Trading Education equips learners with the knowledge and skills to navigate the dynamic world of foreign exchange markets. From understanding currency pairs and market analysis to mastering advanced ",style: TextStyle(fontSize: 12),),
                                      Html(
                                        data: isExpanded
                                            ? controller.coursedetails[0]['description'] // Full description when expanded
                                            : controller.coursedetails[0]['description'].length > previewLength
                                                ? controller.coursedetails[0]['description'].substring(0, previewLength) + "..."
                                                : controller.coursedetails[0]['description'], // Show limited content
                                        shrinkWrap: true,
                                      ),

                                      // "View More" Button (Only if content is long)
                                      // if (controller.coursedetails[0]['description'].length > previewLength)
                                      //   Align(
                                      //     alignment: Alignment.centerLeft,
                                      //     child: Padding(
                                      //       padding: const EdgeInsets.symmetric(horizontal: 10),
                                      //       child: GestureDetector(
                                      //         onTap: () {
                                      //           isExpanded = !isExpanded;
                                      //           (context as Element).markNeedsBuild(); // Rebuild widget
                                      //         },
                                      //         child: Text(
                                      //           isExpanded ? "View Less" : "View More",
                                      //           style: TextStyle(
                                      //             color: primaryColor,
                                      //             fontWeight: FontWeight.bold,
                                      //           ),
                                      //         ),
                                      //       ),
                                      //     ),
                                      //   ),
                                      //
                                    ],
                                  )),
                              SizedBox(
                                height: 20,
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      child: Text(
                                        "What You will Get",
                                        style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 24),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 8),
                                child: Container(
                                  height: 120,
                                  child: ListView.builder(
                                    physics: ScrollPhysics(),
                                    padding: EdgeInsets.all(0),
                                    shrinkWrap: true,
                                    scrollDirection: Axis.horizontal,
                                    // itemCount: controller.items.length,
                                    // itemCount: 2,
                                    itemCount: controller.coursedetails[0]["includes"].length,
                                    itemBuilder: (BuildContext context, int index) {
                                      var data = controller.coursedetails[0]["includes"][index];
                                      return GestureDetector(
                                        onTap: () {},
                                        child: Container(
                                          width: Get.width / 3.8,
                                          margin: EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                                          padding: EdgeInsets.all(10),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.circular(10),
                                            border: Border.all(
                                              color: Theme.of(context).primaryColor.withOpacity(1),
                                              width: 0.8,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: textblackColor.withOpacity(0.1),
                                                blurRadius: 10,
                                                spreadRadius: 1,
                                              ),
                                            ],
                                          ),
                                          // child: Column(
                                          //   mainAxisAlignment: MainAxisAlignment.center,
                                          //   children: [
                                          //     Image.asset(
                                          //         controller.items[index]["icon"]!,
                                          //         height: Get.height*.03),
                                          //     SizedBox(height: 8),
                                          //     Text(controller.items[index]["count"]!, style: TextStyle(fontSize: Get.width*.03, fontWeight: FontWeight.bold)),
                                          //     Text(controller.items[index]["title"]!, style: TextStyle(fontSize: Get.width*.026, color: Colors.grey[600])),
                                          //   ],
                                          // ),
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              // Image.asset('assets/images/placeholder_square.png',
                                              // height: Get.height*.03,),
                                              Image.network(
                                                data["icon"].toString(),
                                                height: Get.height * .038,
                                                errorBuilder: (a, b, c) {
                                                  return Image.asset(
                                                    'assets/images/placeholder_square.png',
                                                    height: Get.height * .03,
                                                  );
                                                },
                                              ),
                                              SizedBox(height: 8),
                                              Text(data["value"].toString(), style: TextStyle(fontSize: Get.width * .03, fontWeight: FontWeight.bold)),
                                              SizedBox(height: 2),
                                              Text(data["label"].toString(), style: TextStyle(fontSize: Get.width * .026, color: Colors.grey[600])),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              if (controller.coursedetails[0]["subjects"].isNotEmpty)
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                      child: Container(
                                        child: Text(
                                          "Subjects",
                                          style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 26),
                                        ),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        // Get.to(video_page());
                                      },
                                      child: Container(
                                        height: 70, // Small height
                                        padding: EdgeInsets.symmetric(vertical: 10),
                                        child: ListView.builder(
                                          scrollDirection: Axis.horizontal,
                                          // itemCount: 2,
                                          itemCount: controller.coursedetails[0]["subjects"].length,
                                          itemBuilder: (context, index) {
                                            if (controller.coursedetails.isEmpty || controller.coursedetails[0]["subjects"].isEmpty) {
                                              return Center(
                                                child: Text(
                                                  'No subjects available',
                                                  style: TextStyle(fontSize: 14, color: Colors.grey),
                                                ),
                                              );
                                            }
                                            var subject = controller.coursedetails[0]["subjects"][index];
                                            return Container(
                                              margin: EdgeInsets.symmetric(horizontal: 15), // Spacing between items
                                              padding: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                                              decoration: BoxDecoration(
                                                color: Color(0xffFFF5E8), // Background color
                                                borderRadius: BorderRadius.circular(15), // Rounded corners
                                                // Border with opacity
                                              ),
                                              child: Row(
                                                children: [
                                                  Image.network(
                                                    subject['thumbnail'].toString(),
                                                    width: 40,
                                                    height: 40,
                                                    fit: BoxFit.cover,
                                                    errorBuilder: (a, b, c) {
                                                      return ClipRRect(
                                                        borderRadius: BorderRadius.circular(12),
                                                        child: Image.asset(
                                                          'assets/images/placeholder_square.png',
                                                          height: Get.height * .03,
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                  SizedBox(width: 10),
                                                  Center(
                                                    child: Text(
                                                      // "Forex Market Analysis",
                                                      subject['title'],
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.black87,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              const SizedBox(
                                height: 20,
                              ),
                              GestureDetector(
                                onTap: () {
                                  // Get.to(video_page());
                                },
                                child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 15),
                                    child: Container(
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                          // color: Color(0xFFEFF3FC), // Background color
                                          borderRadius: BorderRadius.circular(10), // Optional rounded corners
                                          border: Border.all(color: textblackColor, width: .6)),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: List.generate(
                                            // 3,
                                            controller.coursedetails[0]['features'].length, (index) {
                                          var features_list = controller.coursedetails[0]['features'][index];
                                          return Padding(
                                            padding: const EdgeInsets.only(bottom: 10.0), // Space between items
                                            child: Row(
                                              // crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Image.asset(
                                                  'assets/icons/tick.png',
                                                  width: 15,
                                                  height: 15,
                                                ),
                                                SizedBox(width: 15), // Space between icon and text
                                                Text(features_list)

                                                // Expanded(
                                                //   // child: Text(
                                                //   //   controller.coursedetails[0]['course']['features'][index].toString(),
                                                //   //   style: TextStyle(
                                                //   //     fontSize: Get.width*.035,
                                                //   //     fontWeight: FontWeight.w600,
                                                //   //     color: Colors.black87,
                                                //   //   ),
                                                //   // ),
                                                //   child: Html(
                                                //     data:
                                                //     controller.coursedetails[0]['course']['features'][index].toString(),
                                                //
                                                //   ),
                                                // ),
                                              ],
                                            ),
                                          );
                                        }),
                                      ),
                                    )),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Container(
                                decoration: BoxDecoration(
                                    // gradient: LinearGradient(
                                    //   colors: [Color(0xffEDE7FF), textwhiteColor, textwhiteColor],
                                    //   stops: [0, 0.5, 1],
                                    //   begin: Alignment.topCenter,
                                    //   end: Alignment.bottomCenter,
                                    // ),
                                    ),
                                // padding: EdgeInsets.symmetric(vertical: 10),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Container(
                                            child: Text(
                                              "Teacher",
                                              style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 26, fontFamily: font_bold),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // SizedBox(
                                    //   height: 5,
                                    // ),
                                    Container(
                                      height: Get.height * 0.1,
                                      child: ListView.builder(
                                        physics: BouncingScrollPhysics(),
                                        // Improved scrolling
                                        // padding: EdgeInsets.zero,
                                        padding: EdgeInsets.only(left: 12),
                                        shrinkWrap: true,
                                        scrollDirection: Axis.horizontal,
                                        // itemCount: 2,
                                        itemCount: controller.coursedetails[0]["teachers"]?.length ?? 0,
                                        itemBuilder: (BuildContext context, int index) {
                                          var instructor = controller.coursedetails[0]["teachers"][index];

                                          return GestureDetector(
                                            onTap: () {
                                              // Handle tap action
                                            },
                                            child: ProfileCard(
                                              // height: 90,
                                              width: Get.width * .6,
                                              name: instructor['name'].toString(),
                                              experience: instructor['label'].toString(),
                                              imageUrl: instructor['thumbnail'].toString(),
                                              backgroundColor: Colors.transparent,
                                              circleColor: Colors.transparent,
                                              index: index.toString(),
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              Divider(
                                thickness: 0.5,
                              ),
                              SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ),
                      ),
                      // const SizedBox(height: 5),
                      // Divider(
                      //   thickness: 0.2,
                      // ),
                    ],
                  ),
                ),
          bottomNavigationBar: controller.iscoursedetailsLoad
              ? loader()
              : Container(
                  height: Get.height / 6.5,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10),
                        bottomRight: Radius.circular(10),
                      ),
                      color: Colors.white,
                      boxShadow: [BoxShadow(color: textblackColor.withOpacity(0.06), blurRadius: 5, spreadRadius: 1)]),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        color: textwhiteColor,
                        // padding: EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [

                            if(GetStorage().read('pay_version').toString() == "false")
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "₹ ${controller.coursedetails[0]['offer_price']}",
                                  style: TextStyle(
                                    fontSize: Get.width * .045,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  "₹  ${controller.coursedetails[0]['price'].toString()}",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: Get.width * .045,
                                    color: textblackColor.withValues(alpha: .5),
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor: textblackColor.withValues(alpha: .5),
                                  ),
                                ),
                                // Container(
                                //   padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                //   decoration: BoxDecoration(
                                //     color: Color(0xff4AC18A).withOpacity(0.1),
                                //     borderRadius: BorderRadius.circular(20),
                                //   ),
                                //   child: Text(
                                //     // "39% OFF",
                                //     "${controller.coursedetails[0]['course']['discount_percentage']}% OFF",
                                //     style: TextStyle(
                                //       fontSize: Get.width*.035,
                                //       fontWeight: FontWeight.bold,
                                //       color: Color(0xff4AC18A),
                                //     ),
                                //   ),
                                // ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      if(GetStorage().read('pay_version').toString() == "false")
                      SizedBox(height: Get.height * 0.02),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                if (controller.coursedetails[0]['is_enrolled'].toString() == "1") {
                                  controller.enrolll_course(controller.coursedetails[0]['id'].toString());
                                } else {
                                  controller.enrolll_course(controller.coursedetails[0]['id'].toString());
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  side: BorderSide(
                                    color: secondaryColor,
                                  ),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                padding: EdgeInsets.symmetric(vertical: 15),
                              ),
                              child: controller.isEnrolling
                                  ? Container(
                                      height: 23,
                                      width: 23,
                                      child: CircularProgressIndicator(
                                        color: primaryColor,
                                      ),
                                    )
                                  : Text(
                                      controller.coursedetails[0]['is_enrolled'].toString() == "1" ? "View Course" : "Enroll Now",
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: primaryColor,
                                      ),
                                    ),
                            ),
                          ),
                          SizedBox(width: 10),
                          if (GetStorage().read('pay_version').toString() == "false")
                            Expanded(
                              child: common_button_gradient_secondary(
                                onPressed: () {
                                  bottomsheet(context);
                                },
                                borderRadius: BorderRadius.circular(8),
                                width: Get.width * .9,
                                height: 55,
                                child:
                                    // controller.isLoading ?
                                    // Container(
                                    //   height: 25,
                                    //   width: 25,
                                    //   child: CircularProgressIndicator(color: textwhiteColor,),
                                    // )
                                    //     :
                                    Text(
                                  "Buy Now",
                                  style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontFamily: font_semibold, fontSize: Get.width / 25),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
        );
      });
    });
  }

  Widget featurePoint(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          // Image.asset('assets/icons/tick2.png', width: 20, height: 20),
          Icon(
            Icons.done,
            color: primaryColor,
            size: Get.width / 20,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Container(
              // color: Colors.yellow,
              child: Text(
                text,
                style: TextStyle(
                  fontSize: Get.width / 28,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  bottomsheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allows full-screen height usage
      backgroundColor: textwhiteColor,
      builder: (BuildContext context) {
        // Plancontroller plancontroller = Get.put(Plancontroller());

        return StatefulBuilder(
          builder: (context, setState) {
            return GetBuilder<Homecontroller>(builder: (controller) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                child: SingleChildScrollView(
                  child:

                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Close Button

                      // Title
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Enrollment",
                              style: TextStyle(
                                fontSize: Get.width / 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            Align(
                              alignment: Alignment.topRight,
                              child: GestureDetector(
                                onTap: () {
                                  Get.back();
                                },
                                child: Container(padding: EdgeInsets.all(5), color: Colors.transparent, child: Icon(Icons.close, size: 24)),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12),
                        child: Text(
                          "For more details about enrollment, please contact our Admission Desk. We're here to assist you.",
                          style: TextStyle(
                            fontSize: Get.width / 30,
                            fontWeight: FontWeight.w400,
                            color: Colors.black,
                          ),
                        ),
                      ),

                      SizedBox(height: 30),

                      Container(
                        child: Row(
                          children: [

                            Expanded(
                              child: GestureDetector(
                                onTap: (){
                                  launchURL('tel:'+controller.coursedetails[0]['phone'].toString());
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: textwhiteColor,
                                    border: Border.all(color: primaryColor,width: 0.5),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  padding: EdgeInsets.symmetric(vertical: 10),
                                  alignment: Alignment.center,
                                  child: Text("Call"),
                                ),
                              ),
                            ),
                            SizedBox(width: 10,),
                            Expanded(
                              child: GestureDetector(
                                onTap: (){
                                  // launchURL("https://api.whatsapp.com/send/?phone=${controller.coursedetails[0]['whatsapp'].toString()}&text=${Uri.encodeComponent('Hello!, I need help with the enrollment process.\n\n Course : ${controller.coursedetails[0]['title']}')}&type=phone_number&app_absent=0");
                                  // launchURL("https://wa.me/919946801100");
                                  launchURL('https://wa.me/${controller.coursedetails[0]['whatsapp']}?text=${Uri.encodeComponent('Hello!, I need help with the enrollment process.\n\n Course : ${controller.coursedetails[0]['title']}')}');
                                },
                                child: Container(
                                    decoration: BoxDecoration(
                                      color: CupertinoColors.systemGreen,
                                      border: Border.all(color: CupertinoColors.systemGreen,width: 0.5),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    padding: EdgeInsets.symmetric(vertical: 10),
                                    alignment: Alignment.center,
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Image.asset('assets/icons/whatsapp_2.png',width: Get.width/22,),
                                        SizedBox(width: 5,),
                                        Text("WhatsApp",style: TextStyle(color: textwhiteColor),),
                                      ],
                                    )
                                ),
                              ),
                            ),

                          ],
                        ),
                      ),

                      SizedBox(height: 30),


                    ],
                  ),


                  // Column(
                  //   crossAxisAlignment: CrossAxisAlignment.start,
                  //   children: [
                  //     // Close Button
                  //
                  //     // Title
                  //     Padding(
                  //       padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10),
                  //       child: Row(
                  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //         children: [
                  //           Text(
                  //             "Installments",
                  //             style: TextStyle(
                  //               fontSize: Get.width / 22,
                  //               fontWeight: FontWeight.bold,
                  //               color: Colors.black,
                  //             ),
                  //           ),
                  //           Align(
                  //             alignment: Alignment.topRight,
                  //             child: GestureDetector(
                  //               onTap: () {
                  //                 Get.back();
                  //               },
                  //               child: Container(padding: EdgeInsets.all(5), color: Colors.transparent, child: Icon(Icons.close, size: 24)),
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //     Container(
                  //       padding: EdgeInsets.symmetric(horizontal: 12),
                  //       child: Text(
                  //         "Choose a payment plan that suits you – pay in a single installment or split into two easy payments.",
                  //         style: TextStyle(
                  //           fontSize: Get.width / 30,
                  //           fontWeight: FontWeight.w400,
                  //           color: Colors.black,
                  //         ),
                  //       ),
                  //     ),
                  //
                  //     SizedBox(height: 30),
                  //
                  //     // Package List
                  //     // Container(
                  //     //   padding: EdgeInsets.symmetric(horizontal: 10),
                  //     //   child: ListView.builder(
                  //     //     shrinkWrap: true, // Allows the ListView to fit inside SingleChildScrollView
                  //     //     physics: NeverScrollableScrollPhysics(), // Prevents inner scrolling
                  //     //     itemCount: controller.coursedetails[0]['packages'].length,
                  //     //     itemBuilder: (context, index) {
                  //     //       bool isSelected = controller.slectedpackage == controller.coursedetails[0]['packages'][index]['id'].toString();
                  //     //       return GestureDetector(
                  //     //         onTap: () {
                  //     //           setState(() {
                  //     //             controller.slectedpackage = controller.coursedetails[0]['packages'][index]['id'].toString();
                  //     //             controller.selectedplandata = controller.coursedetails[0]['packages'][index];
                  //     //             controller.update();
                  //     //           });
                  //     //         },
                  //     //         child: Stack(
                  //     //           children: [
                  //     //             Container(
                  //     //               margin: EdgeInsets.symmetric(vertical: 8),
                  //     //               padding: EdgeInsets.all(12),
                  //     //               decoration: BoxDecoration(
                  //     //                 borderRadius: BorderRadius.circular(10),
                  //     //                 border: Border.all(
                  //     //                   color: isSelected ? primaryColor : textblackColor.withValues(alpha: 0.5),
                  //     //                   width: isSelected ? 1.5 : 0.5,
                  //     //                 ),
                  //     //                 color: Colors.white,
                  //     //               ),
                  //     //               child: Column(
                  //     //                 crossAxisAlignment: CrossAxisAlignment.start,
                  //     //                 children: [
                  //     //                   Row(
                  //     //                     crossAxisAlignment: CrossAxisAlignment.center,
                  //     //                     children: [
                  //     //                       if (isSelected) Icon(Icons.check_circle, color: primaryColor),
                  //     //                       SizedBox(width: isSelected ? 8 : 0),
                  //     //                       Column(
                  //     //                         crossAxisAlignment: CrossAxisAlignment.start,
                  //     //                         children: [
                  //     //                           Text(
                  //     //                             controller.coursedetails[0]['packages'][index]['title'],
                  //     //                             style: TextStyle(
                  //     //                               fontSize: 18,
                  //     //                               fontWeight: FontWeight.bold,
                  //     //                             ),
                  //     //                           ),
                  //     //                           SizedBox(height: 8),
                  //     //                           Row(
                  //     //                             children: [
                  //     //                               Text(
                  //     //                                 // "₹${controller.coursedetails[0]['packages'][index]['discount_price']}",
                  //     //                                 "₹${controller.coursedetails[0]['packages'][index]['payable_amount']}",
                  //     //                                 style: TextStyle(
                  //     //                                   fontSize: 18,
                  //     //                                   fontWeight: FontWeight.bold,
                  //     //                                   color: textblackColor,
                  //     //                                 ),
                  //     //                               ),
                  //     //                               SizedBox(width: 10),
                  //     //                               Text(
                  //     //                                 "₹${controller.coursedetails[0]['packages'][index]['actual_amount']}",
                  //     //                                 style: TextStyle(
                  //     //                                   fontSize: 16,
                  //     //                                   decoration: TextDecoration.lineThrough,
                  //     //                                   color: Colors.grey,
                  //     //                                 ),
                  //     //                               ),
                  //     //                             ],
                  //     //                           ),
                  //     //                         ],
                  //     //                       ),
                  //     //                     ],
                  //     //                   ),
                  //     //                 ],
                  //     //               ),
                  //     //             ),
                  //     //
                  //     //             // if(int.parse(controller.coursedetails[0]['packages'][index]['discount_percentage'].toString()) > 0)
                  //     //             //   Positioned(
                  //     //             //     top: 8,
                  //     //             //     right: 0.2,
                  //     //             //     child: Container(
                  //     //             //       padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  //     //             //       decoration: BoxDecoration(
                  //     //             //         color:  primaryColor,
                  //     //             //         borderRadius: BorderRadius.only(bottomLeft: Radius.circular(5), topRight: Radius.circular(10)),
                  //     //             //       ),
                  //     //             //       child: Text("Save ${controller.coursedetails[0]['packages'][index]['discount_percentage']}%",
                  //     //             //         style: TextStyle(
                  //     //             //           color:  textwhiteColor,
                  //     //             //           fontWeight: FontWeight.bold,
                  //     //             //         ),
                  //     //             //       ),
                  //     //             //     ),
                  //     //             //   ),
                  //     //           ],
                  //     //         ),
                  //     //       );
                  //     //     },
                  //     //   ),
                  //     // ),
                  //     controller.coursedetails[0]['installments'].isEmpty
                  //         ? Container(
                  //             padding: EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                  //             child: Center(
                  //               child: Text("No Installments Available",
                  //                   style: TextStyle(
                  //                     fontSize: Get.width / 22,
                  //                     fontWeight: FontWeight.bold,
                  //                     color: Colors.black,
                  //                   )),
                  //             ))
                  //         : Container(
                  //             padding: EdgeInsets.symmetric(horizontal: 10),
                  //             child: ListView.builder(
                  //               shrinkWrap: true, // Allows the ListView to fit inside SingleChildScrollView
                  //               physics: NeverScrollableScrollPhysics(), // Prevents inner scrolling
                  //               // itemCount: controller.packages.length,
                  //               itemCount: controller.coursedetails[0]['installments'].length,
                  //               itemBuilder: (context, index) {
                  //                 // bool isSelected = controller.slectedpackage == controller.packages[index]['id'].toString();
                  //                 // var data = controller.packages[index];
                  //                 bool isSelected = controller.slectedpackage == controller.coursedetails[0]['installments'][index]['id'].toString();
                  //                 var data = controller.coursedetails[0]['installments'][index];
                  //                 return GestureDetector(
                  //                   onTap: () {
                  //                     // controller.slectedpackage = controller.packages[index]['id'].toString();
                  //                     // controller.selectedplandata = controller.packages[index];
                  //                     controller.slectedpackage = data['id'].toString();
                  //                     controller.selectedplandata = data;
                  //                     log(controller.selectedplandata.toString());
                  //                     controller.update();
                  //                   },
                  //                   child: Stack(
                  //                     children: [
                  //                       AnimatedContainer(
                  //                         duration: Duration(milliseconds: 300),
                  //                         margin: const EdgeInsets.only(bottom: 16),
                  //                         decoration: BoxDecoration(
                  //                           color: Colors.white,
                  //                           border: Border.all(
                  //                             width: 2,
                  //                             color: isSelected ? secondaryColor : Colors.grey.shade300,
                  //                           ),
                  //                           borderRadius: BorderRadius.circular(12),
                  //                         ),
                  //                         child: Column(
                  //                           children: [
                  //                             // if (data['isBestValue'])
                  //                             //   SizedBox(
                  //                             //     height: 20,
                  //                             //   ),
                  //                             Padding(
                  //                               padding: const EdgeInsets.all(12.0),
                  //                               child: Row(
                  //                                 children: [
                  //                                   if (isSelected)
                  //                                     Container(
                  //                                         decoration: BoxDecoration(
                  //                                           gradient: LinearGradient(
                  //                                             begin: Alignment.centerLeft,
                  //                                             end: Alignment.centerRight,
                  //                                             colors: [secondarylightColor, secondaryColor],
                  //                                           ),
                  //                                           shape: BoxShape.circle,
                  //                                         ),
                  //                                         padding: EdgeInsets.all(5),
                  //                                         margin: EdgeInsets.only(right: 10),
                  //                                         child: Icon(
                  //                                           Icons.done,
                  //                                           color: Colors.white,
                  //                                           size: Get.width / 35,
                  //                                         )),
                  //                                   Expanded(child: Text(data['title'].toString(), style: const TextStyle(fontWeight: FontWeight.bold))),
                  //                                 ],
                  //                               ),
                  //                             ),
                  //                             Padding(
                  //                               padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8),
                  //                               child: Column(
                  //                                 crossAxisAlignment: CrossAxisAlignment.start,
                  //                                 children: [
                  //                                   // ...data['features'].map((f) => Row(
                  //                                   //       children: [
                  //                                   //         Icon(Icons.done, color: primaryColor, size: Get.width / 20),
                  //                                   //         Expanded(child: Text("$f")),
                  //                                   //       ],
                  //                                   //     )),
                  //                                   const SizedBox(height: 8),
                  //
                  //                                   // data['is_installment'].toString() == "1" ? Container(
                  //                                   // data['has_installment'].toString() == "1" ? Container(
                  //                                   //
                  //                                   // ) :
                  //                                   // Row(
                  //                                   //   mainAxisAlignment: MainAxisAlignment.end,
                  //                                   //   children: [
                  //                                   //     Text(
                  //                                   //       data['price'].toString(),
                  //                                   //       style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  //                                   //     ),
                  //                                   //     SizedBox(
                  //                                   //       width: 14,
                  //                                   //     ),
                  //                                   //     if (data['oldPrice'] != null)
                  //                                   //       Text(
                  //                                   //         data['oldPrice'].toString(),
                  //                                   //         style: const TextStyle(
                  //                                   //           decoration: TextDecoration.lineThrough,
                  //                                   //           color: Colors.grey,
                  //                                   //         ),
                  //                                   //       ),
                  //                                   //   ],
                  //                                   // ),
                  //                                   Row(
                  //                                     mainAxisAlignment: MainAxisAlignment.end,
                  //                                     children: [
                  //                                       Text(
                  //                                         '₹ ${data['amount'].toString()}',
                  //                                         style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  //                                       ),
                  //                                       SizedBox(
                  //                                         width: 14,
                  //                                       ),
                  //                                       if (data['oldPrice'] != null)
                  //                                         Text(
                  //                                           data['oldPrice'].toString(),
                  //                                           style: const TextStyle(
                  //                                             decoration: TextDecoration.lineThrough,
                  //                                             color: Colors.grey,
                  //                                           ),
                  //                                         ),
                  //                                     ],
                  //                                   ),
                  //
                  //                                   if (isSelected &&
                  //                                       // data['is_installment'].toString() == "1")
                  //                                       data['has_installment'].toString() == "1")
                  //                                     Column(
                  //                                       children: [
                  //                                         Padding(
                  //                                           padding: const EdgeInsets.symmetric(vertical: 10),
                  //                                           child: DottedLine(
                  //                                             direction: Axis.horizontal,
                  //                                             alignment: WrapAlignment.center,
                  //                                             lineLength: double.infinity,
                  //                                             lineThickness: 1.0,
                  //                                             dashLength: 4.0,
                  //                                             dashColor: secondarylightColor.withOpacity(0.7),
                  //                                             dashGapLength: 3.0,
                  //                                             dashGapColor: Colors.transparent,
                  //                                           ),
                  //                                         ),
                  //                                         const SizedBox(height: 5),
                  //                                         // ...data['installments'].map((f) => Expanded(child: Text("$f"))),
                  //                                         ListView.builder(
                  //                                           // padding: EdgeInsets.all(16),
                  //                                           itemCount: data['installments'].length,
                  //                                           shrinkWrap: true,
                  //                                           itemBuilder: (_, index2) {
                  //                                             var emiData = data['installments'][index2];
                  //
                  //                                             return GestureDetector(
                  //                                               onTap: () {
                  //                                                 controller.selectedEmi = index2.toString();
                  //                                                 controller.selectedEmiData = emiData;
                  //                                                 controller.update();
                  //                                               },
                  //                                               child: Container(
                  //                                                 margin: EdgeInsets.only(bottom: data['installments'].length - 1 == index2 ? 0 : 5),
                  //                                                 child: Card(
                  //                                                   color: textwhiteColor,
                  //                                                   surfaceTintColor: textwhiteColor,
                  //                                                   child: Padding(
                  //                                                     padding: const EdgeInsets.all(10),
                  //                                                     child: Row(
                  //                                                       children: [
                  //                                                         Expanded(
                  //                                                             child: Container(
                  //                                                           // color: Colors.yellow,
                  //                                                           child: Column(
                  //                                                             crossAxisAlignment: CrossAxisAlignment.start,
                  //                                                             children: [
                  //                                                               // Text(
                  //                                                               //   emiData['title'].toString(),
                  //                                                               //   style: TextStyle(fontFamily: font_regular, fontSize: Get.width / 30,fontWeight: FontWeight.w500)
                  //                                                               // ),
                  //                                                               Text(
                  //                                                                   // '₹ ${emiData['final_price'].toString()}',
                  //                                                                   '₹ ${emiData['installment_amount'].toString()}',
                  //                                                                   style: TextStyle(fontFamily: font_bold, fontSize: Get.width / 27, fontWeight: FontWeight.w600)),
                  //                                                             ],
                  //                                                           ),
                  //                                                         )),
                  //                                                         const SizedBox(width: 10),
                  //                                                         Image.asset(
                  //                                                           "assets/icons/check_2.png",
                  //                                                           width: Get.width / 18,
                  //                                                           color: controller.selectedEmi == index2.toString() ? primaryColor : Colors.grey,
                  //                                                         ),
                  //                                                       ],
                  //                                                     ),
                  //                                                   ),
                  //                                                 ),
                  //                                               ),
                  //                                             );
                  //                                           },
                  //                                         ),
                  //                                         const SizedBox(height: 8),
                  //                                       ],
                  //                                     )
                  //                                 ],
                  //                               ),
                  //                             ),
                  //                           ],
                  //                         ),
                  //                       ),
                  //                       // if (data['isBestValue'])
                  //                       //   Positioned(
                  //                       //     top: 0,
                  //                       //     right: 0.2,
                  //                       //     child: Container(
                  //                       //       padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  //                       //       decoration: BoxDecoration(
                  //                       //         gradient: LinearGradient(
                  //                       //           begin: Alignment.centerLeft,
                  //                       //           end: Alignment.centerRight,
                  //                       //           colors: [primarylightColor.withValues(alpha: .9), primaryColor],
                  //                       //         ),
                  //                       //         borderRadius: BorderRadius.only(bottomLeft: Radius.circular(5), topRight: Radius.circular(10)),
                  //                       //       ),
                  //                       //       child: Text(
                  //                       //         'Best Value  •  Save 61%',
                  //                       //         style: TextStyle(
                  //                       //           color: textwhiteColor,
                  //                       //           fontWeight: FontWeight.bold,
                  //                       //         ),
                  //                       //       ),
                  //                       //     ),
                  //                       //   ),
                  //                     ],
                  //                   ),
                  //                 );
                  //               },
                  //             ),
                  //           ),
                  //
                  //     // Continue Button
                  //     SizedBox(
                  //       height: Get.height / 7,
                  //     ),
                  //     Padding(
                  //       padding: const EdgeInsets.only(top: 10.0),
                  //       child: Center(
                  //         child: common_button_gradient_secondary(
                  //           borderRadius: BorderRadius.circular(10),
                  //           width: Get.width * .9,
                  //           onPressed: controller.slectedpackage != '-1'
                  //               ? () {
                  //                   if (controller.selectedplandata['has_installment'].toString() == '1') {
                  //                     if (controller.selectedEmi == '-1') {
                  //                       toast_info('Select any installment');
                  //                     } else {
                  //                       plancontroller.get_create_order("", "", "", controller.selectedEmiData['installment_amount'], "", "", "", "0", "");
                  //                     }
                  //                   } else {
                  //                     plancontroller.get_create_order("", "", "", controller.selectedplandata['amount'], "", "", "", "0", "");
                  //                   }
                  //                   // plancontroller.get_create_order("", "", "", controller.selectedplandata['final_price'], "", "", "", "0", "");
                  //
                  //                   // if(controller.selectedplandata['is_purchased'].toString() != '0'){
                  //                   //   // toast_info('Already Purchased');
                  //                   // }
                  //                   // else{
                  //                   //   // log("Selected Package ID: ${controller.slectedpackage}");
                  //                   //   // log("Selected Package ${controller.selectedplandata}");
                  //                   //   // Get.to(() => PlanCheckOutPage(data: controller.selectedplandata));
                  //                   // }
                  //                 }
                  //               : null,
                  //           // : controller.slectedpackage != '-1' ? primaryColor : controller.selectedplandata['is_purchased'].toString() != '1' ? primaryColor : const Color.fromARGB(255, 197, 195, 195),
                  //           child: Text(
                  //             controller.slectedpackage != '-1'
                  //                 ? 'Continue'
                  //                 : controller.selectedplandata['is_purchased'].toString() != '1'
                  //                     ? 'Continue'
                  //                     : 'Already Purchased',
                  //             style: TextStyle(
                  //               color: textwhiteColor,
                  //               fontSize: Get.width * .04,
                  //               fontWeight: FontWeight.w600,
                  //             ),
                  //           ),
                  //         ),
                  //       ),
                  //     ),
                  //   ],
                  // ),




                ),
              );
            });
          },
        );
      },
    );
  }

// Helper Widget for Feature Points
}
