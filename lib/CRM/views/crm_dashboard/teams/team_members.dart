import 'package:edutalim/CRM/api/apis.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class team_members extends StatefulWidget {

  String team_id;
  team_members({required this.team_id});

  @override
  State<team_members> createState() => _leads_listState();
}

class _leads_listState extends State<team_members> {

  List teamsdata = [];
  bool isLoading = true;

  get_teams()async{
    var data = await CRMApiBaseHandler.fet_team_memberss(widget.team_id.toString());
    setState(() {
      teamsdata = data['data']['team_members'];
      isLoading = false;
    });
  }


  @override
  void initState() {
    get_teams();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        title: Text("Team Members",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.black),),
      ),
      body: isLoading?
      loader()
          :
      teamsdata.isEmpty ?
      Container(
        width: Get.width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset('assets/icons/no_data.png',width: Get.width/4,),
            SizedBox(height: 20,),
            Container(
              child: Text("No Teams  Found",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.black,fontSize: 16),),
            ),
          ],
        ),
      )
          :
      SingleChildScrollView(
        physics: ScrollPhysics(),
        child: Container(
          child: Column(
            children: [

              SizedBox(height: 5,),
              ListView.builder(
                physics: NeverScrollableScrollPhysics(),
                itemCount: teamsdata.length,
                shrinkWrap: true,
                itemBuilder: (context, index){
                  Map data = teamsdata[index];
                  return Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        color: Colors.white
                    ),
                    margin: EdgeInsets.symmetric(horizontal: 15,vertical: 5),
                    child: Column(
                      children: [

                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 15,vertical: 13),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [

                              Container(
                                width: Get.width/8,
                                height: Get.width/8,
                                decoration: BoxDecoration(
                                  border: Border.all(color: primaryColor,width: 2),
                                  borderRadius: BorderRadius.circular(50),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(50),
                                  // child: Image.network(leads[index]['profile_picture'].toString(),fit: BoxFit.cover,
                                  child: Image.network(data['profile_picture'].toString(),fit: BoxFit.cover,
                                    errorBuilder: (a,b,c){
                                      return Image.asset('assets/images/avater_placeholder.jpg');
                                    },),
                                ),
                              ),

                              SizedBox(width: 10,),

                              Container(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [

                                    Row(
                                      children: [
                                        Container(
                                          // color: Colors.yellow,
                                          width: Get.width/1.8,
                                          child: Text(data['name'] ?? '--', style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: commontextColor,overflow: TextOverflow.ellipsis),),
                                          // child: Text(leads[index]['title'].toString(), style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: commontextColor,overflow: TextOverflow.ellipsis),),
                                        ),




                                      ],
                                    ),


                                    Container(
                                      child: Row(
                                        children: [
                                          Container(
                                            // color: Colors.yellow,
                                            width: Get.width/5,
                                            child: Text("Phone", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                          ),
                                          Container(
                                            // color: Colors.redAccent,
                                            width: Get.width/2.5,
                                            // child: Text(leads[index]['phone'].toString() == "null" ? ":  --" : ":  "+leads[index]['phone'].toString(), style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                            child: Text(data['phone'].toString() == "null" ? ":  --" : ":  "+data['phone'].toString(), style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                          ),

                                        ],
                                      ),
                                    ),

                                    Container(
                                      child: Row(
                                        children: [
                                          Container(
                                            // color: Colors.yellow,
                                            width: Get.width/5,
                                            child: Text("Whatsapp", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                          ),
                                          Container(
                                            // color: Colors.redAccent,
                                            width: Get.width/2.5,
                                            // child: Text(leads[index]['whatsapp'].toString() == "null" ? ":  --" : ":  "+leads[index]['whatsapp'].toString(), style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                            child: Text(data['whatsapp'].toString() == "null" ? ":  --" : ":  "+data['whatsapp'].toString(), style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                          ),

                                        ],
                                      ),
                                    ),

                                    // Container(
                                    //   child: Row(
                                    //     children: [
                                    //       Container(
                                    //         // color: Colors.yellow,
                                    //         width: Get.width/5,
                                    //         child: Text("Email", style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                    //       ),
                                    //       Container(
                                    //         // color: Colors.redAccent,
                                    //         width: Get.width/2.5,
                                    //         // child: Text(leads[index]['email'].toString() == "null" ? ":  --" : ":  "+leads[index]['email'].toString(), style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                    //         child: Text(filt_leadsdata[index]['email'].toString() == "null" ? ":  --" : ":  "+leads[index]['email'].toString(), style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500, color: commontextColor),),
                                    //       ),
                                    //
                                    //     ],
                                    //   ),
                                    // ),

                                  ],
                                ),
                              ),

                            ],
                          ),
                        ),


                        Container(
                          width: Get.width,
                          child:  Divider(thickness: 0.4,),
                        ),

                      ],
                    ),
                  );
                },
              ),

            ],
          ),
        ),
      ),
    );
  }
}
