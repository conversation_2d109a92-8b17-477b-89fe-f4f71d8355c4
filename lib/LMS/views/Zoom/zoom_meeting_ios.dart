import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lottie/lottie.dart';
import 'package:zoom_allinonesdk/zoom_allinonesdk.dart';
import 'dart:io' show Platform;

class zoom_meeting_ios extends StatefulWidget {
  Map data;
  zoom_meeting_ios({required this.data});

  @override
  State<zoom_meeting_ios> createState() => _JoinMeetingScreenState();
}

class _JoinMeetingScreenState extends State<zoom_meeting_ios> {

  @override
  void initState() {
    super.initState();
    platformCheck();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        width: Get.width,
        decoration: BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.cover,
              opacity: 0.1,
              image: AssetImage('assets/bg/zoom_bg.png'),
            )
        ),
        child: Stack(
          children: [

            AppBar(
              elevation: 0,
              toolbarHeight: 0,
              backgroundColor: Color(0xff00FFFFFF),
              iconTheme: IconThemeData(
                  color: Colors.black
              ),
            ),


            Container(
              width: Get.width,
              child:  Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Lottie.asset('assets/lotties/zoom_back.json',width: MediaQuery.of(context).size.width/2,),
                  SizedBox(height: 50,),
                  // Text(isInitialized ?  "Meeting Running" : "Something went wrong",style: TextStyle(color: Colors.black26,fontFamily: "poppins_regular",fontWeight: FontWeight.w600,fontSize: MediaQuery.of(context).size.width/30),),
                  Text("Click the 'Join Now'\nButton to Enter the Live Class",style: TextStyle(color: Colors.black54,fontFamily: "poppins_regular",fontWeight: FontWeight.w500,fontSize: MediaQuery.of(context).size.width/30),textAlign: TextAlign.center,),

                  const SizedBox(height: 30,),

                  GestureDetector(
                    onTap: (){
                      platformCheck();
                    },
                    child: Container(
                      width: Get.width/1.5,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                          color: Colors.blue,
                          border: Border.all(color: Colors.blue,width: 1),
                          borderRadius: BorderRadius.circular(1000)
                      ),
                      child: Text("Join Now",style: TextStyle(color: Colors.white,fontWeight: FontWeight.w700,fontSize: Get.width/20),),
                    ),
                  ),


                ],
              ),
            ),


            Positioned(
                top: 0,left: 0,right: 0,
                child: Container(
                  child: AppBar(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    iconTheme: IconThemeData(
                        color: Colors.blue
                    ),
                    automaticallyImplyLeading: false,
                    leading: GestureDetector(
                      onTap: (){
                        Get.back();
                      },
                      child: Wrap(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(1000),
                                boxShadow: [BoxShadow(
                                    color: Colors.grey.withOpacity(0.4),
                                    blurRadius: 5.0,
                                    spreadRadius: 1
                                ),]
                            ),
                            padding: EdgeInsets.all(5),
                            margin: EdgeInsets.only(left: 20,top: 5),
                            child: Icon(Icons.keyboard_arrow_left,color: Colors.blue,),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
            ),


          ],
        ),
      ),
    );
  }

  void platformCheck() {
      joinMeetingAndroidAndIos();
  }

  void joinMeetingAndroidAndIos() async {
    log("inside Meet");

    try {

      ZoomOptions zoomOptions = new ZoomOptions(
        domain: "zoom.us",
        // clientId: 'agIIwywKSKeKHTFKEnNgBA',
        clientId: widget.data['zoom_key'].toString(),
        // clientSecert: 'EoKG6pfVeKYg249R56ccnyRPLFaQFAMk',
        clientSecert: widget.data['zoom_secret'].toString(),
      );
      var meetingOptions = MeetingOptions(
        displayName: GetStorage().read('user_name').toString(),
        // meetingId: '3635760897',
        meetingId: widget.data['meeting_id'].toString(),
        // meetingPassword: 'PCVD1H',
        meetingPassword: widget.data['meeting_password'].toString(),
      );

      var zoom = ZoomAllInOneSdk();
      zoom.initZoom(zoomOptions: zoomOptions).then((results) {
        if (results[0] == 0) {
          zoom.joinMeting(meetingOptions: meetingOptions).then((loginResult) {});
        }
      }).catchError((error) {
        print("[Error Generated] : " + error);
      });

    } catch(e){
      log("message + ${e.toString()}");
    }

  }


}
