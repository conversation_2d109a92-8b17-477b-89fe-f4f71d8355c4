import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:otp_autofill/otp_autofill.dart';

import '../CRM/views/crm_dashboard/crm_dashboard.dart';
import '../LMS/api/auth_apis.dart';
import '../LMS/api/main_apis.dart';
import '../components/constants.dart';
import '../components/utils.dart';
import '../LMS/views/dashboard/dashboard.dart';

class Otpcontroller extends GetxController {
  int otpLength = 0;

  TextEditingController allotp = TextEditingController();
  TextEditingController otp1 = TextEditingController();
  TextEditingController otp2 = TextEditingController();
  TextEditingController otp3 = TextEditingController();
  TextEditingController otp4 = TextEditingController();

  FocusNode otp1OneFocus = FocusNode();
  FocusNode otp2OneFocus = FocusNode();
  FocusNode otp3OneFocus = FocusNode();
  FocusNode otp4OneFocus = FocusNode();

  String otp = '';

  //-----------------------OTP auto sec ------------------------------
  late OTPTextEditController otptextfldcontroller;
  late OTPInteractor otpInteractor;

  //-----------------------OTP auto sec ------------------------------

  Future<void> initInteractor() async {
    otpInteractor = OTPInteractor();
    // You can receive your app signature by using this method.
    final appSignature = await otpInteractor.getAppSignature();

    if (kDebugMode) {
      print('Your app signature: $appSignature');
    }
  }

  getOTP(from, phone, code,) {
    otptextfldcontroller = OTPTextEditController(
      codeLength: 4,
      onCodeReceive: (code) {
        print('otp received-------------------------------------');
        print('Your Application receive code - $code');

        var codedata = [];
        codedata = code.toString().split('');
        if (codedata.length > 0) {
          otp1.text = codedata[0];
        }

        if (codedata.length > 1) {
          otp2.text = codedata[1];
        }

        if (codedata.length > 2) {
          otp3.text = codedata[2];
        }

        if (codedata.length > 3) {
          otp4.text = codedata[3];
        }
      },
      otpInteractor: otpInteractor,
    )..startListenUserConsent(
        (code) {
          print('otp received---------------2----------------------$code');

          final exp = RegExp(r'(\d{4})');
          print(exp.stringMatch(code.toString()).toString());
          var otp123 = exp.stringMatch(code ?? '') ?? '';
          print("OTP NEW ---------- $otp123");

          otp1.text = otp123[0].toString();
          otp2.text = otp123[1].toString();
          otp3.text = otp123[2].toString();
          otp4.text = otp123[3].toString();

          if (otp123.length == 4) {
            verifyOTP(from.toString(), phone, code, otp123);
          }

          return exp.stringMatch(code ?? '') ?? '';
        },
      );
  }

  // List otp_numbers = [];
  bool isLoading = false;

  verifyOTP(from, phone, code, otp_numbers) async {

    isLoading = true;
    update();
    try {
      if (otp_numbers.length == 4) {
        log("OTP Length Inside  : ${otp_numbers.length}");
        var data = await AuthApis.verify_otp(phone, "$code", otp_numbers);
        if (data != null && data['status'].toString() == "1" || data['status'].toString() == "true") {
          toast_success(data['message'].toString());
          await GetStorage().write('auth_token', data['data']['token'].toString());
          await GetStorage().write('login_status', "true");
          await GetStorage().write('user_id', data['data']['user_data']['user_id'].toString());
          await GetStorage().write('user_role', data['data']['user_data']['role_id'].toString());
          await GetStorage().write('user_name', data['data']['user_data']['name'].toString());
          await GetStorage().write('user_phone', data['data']['user_data']['phone'].toString());
          await GetStorage().write('user_email', data['data']['user_data']['email'].toString());
          await GetStorage().write('role_id', data['data']['user_data']['role_id'].toString());
          await GetStorage().write('country_code', data['data']['user_data']['country_code'].toString());
          await GetStorage().write('profile_image', data['data']['user_data']['profile_image'].toString());

          // Add a small delay to ensure all data is written to storage
          await Future.delayed(Duration(milliseconds: 500));

          if(data['data']['user_data']['role_id'].toString() == "2")
            {
              Get.offAll(() => dashboard());
            }else{
            Get.offAll(() => crm_dashboard());
          }

          print('Otp success');
        } else {
          toast_error(data['message'].toString());
          print('Something went wrong in otp');
        }
      } else {
        toast_warning('Please Enter 4 digit OTP');
      }
    } catch (e) {
      log('otp error: $e');
    }

    isLoading = false;
    update();
  }

  // ---------otp resend------------------------
  StreamController timerStream = new StreamController<int>();
  late Timer resendCodeTimer;
  static const timerDuration = 30;

  activeCounter() {
    resendCodeTimer = Timer.periodic(Duration(seconds: 1), (Timer timer) {
      if (timerDuration - timer.tick > 0) {
        timerStream.sink.add(timerDuration - timer.tick);
      } else {
        timerStream.sink.add(0);
        resendCodeTimer.cancel();
      }
    });
  }

  resend_otp(context, phone, code) async {
    // await ApiBasehandler.login(phone,code,device_id.toString());
    await AuthApis.resend_otp(phone, "+$code");
    otp1.text = '';
    otp2.text = '';
    otp3.text = '';
    otp4.text = '';
    otp = '';
    otp1OneFocus.requestFocus();
    update();
  }

  late Timer _timerr;
}
