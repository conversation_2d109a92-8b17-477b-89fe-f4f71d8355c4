import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/mycoursecontroller.dart';

class practice extends StatefulWidget {
  // String course_id,section_id,lesson_id;
  // model_exams({required this.course_id,required this.section_id,required this.lesson_id});

  // String subject_id,lesson_id;
  // materials({required this.subject_id,required this.lesson_id});

  @override
  State<practice> createState() => _sessionsState();
}

class _sessionsState extends State<practice> {


  Mycoursecontroller controller = Get.put(Mycoursecontroller());
  int? sel_index;


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // controller.get_tests(course_id, section_id, lesson_id);
    // controller.get_exams(widget.subject_id.toString(),widget.lesson_id.toString());
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Mycoursecontroller>(
        builder: (controller) {
          return Scaffold(
            backgroundColor: textwhiteColor,
            appBar: AppBar(
              backgroundColor: textwhiteColor,
              surfaceTintColor: textwhiteColor,
              elevation: 5,
              centerTitle: true,
              leading:  GestureDetector(
                onTap: (){
                  Get.back();
                },
                child: Container(
                  // decoration: BoxDecoration(
                  //     color: primaryColor,
                  //     borderRadius: BorderRadius.circular(100)
                  // ),
                  margin: EdgeInsets.all(10),
                  child: Icon(Icons.arrow_back,color: textblackColor,),
                ),
              ),
              title: Container(
                child: Text("Practice",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w700,fontFamily: font_regular,fontSize: Get.width/24),),
              ),
            ),
            body: Container(
              height: Get.height,
              width: Get.width,
              color: textwhiteColor,
              child: Column(
                children: [

                  // controller.isexamsloading?loader():
                  // controller.exams.isEmpty?
                  Container(
                    height: 200,
                    child: Center(child: Text("No Practices Found",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.black,fontSize: 16),)),
                  )
                  // :
                  // Expanded(
                  //   child: Container(
                  //     padding: EdgeInsets.symmetric(horizontal: 15),
                  //     child: ListView.builder(
                  //       physics: ScrollPhysics(),
                  //       padding: EdgeInsets.symmetric(vertical: 20),
                  //       shrinkWrap: true,
                  //       // itemCount: 6,
                  //       itemCount: controller.practices.length,
                  //       itemBuilder: (BuildContext context, int index) {
                  //         Map data = controller.practices[index];
                  //         return GestureDetector(
                  //           onTap: (){
                  //             // if(data['free'].toString() == 'off'){
                  //             //   Get.to(()=> plans(course_id: GetStorage().read('course_id').toString()));
                  //             // }
                  //             // else{
                  //             //   data['exam_link'].toString() == ''?toast_info('Something wrong'):
                  //             //   Get.to(()=> webview_page(url: data['exam_link'].toString()));
                  //             //  print(data['exam_link'].toString());
                  //             // }
                  //           },
                  //           child: Stack(
                  //             children: [
                  //               Container(
                  //                 margin: EdgeInsets.only(bottom: 20),
                  //                 padding: EdgeInsets.symmetric(horizontal: 14,vertical: 15),
                  //                 decoration: BoxDecoration(
                  //                     color: sel_index == index ? primaryColor.withOpacity(0.2) : textwhiteColor,
                  //                     borderRadius: BorderRadius.circular(10),
                  //                     border: Border.all(color: sel_index == index ? primaryColor.withOpacity(1) : textblackColor.withOpacity(0.3),width: sel_index == index ? 1.2 : 0.8)
                  //                 ),
                  //                 child: Row(
                  //                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                   children: [
                  //                     Container(
                  //                       width: Get.width/1.8,
                  //                       // color: Colors.yellow,
                  //                       // child: Text("Biological Classification ${index + 1 }",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w600,fontSize: Get.width/24),),
                  //                       child: Text(data['title'].toString(),style: TextStyle(color: textblackColor,fontWeight: FontWeight.w600,fontSize: Get.width/24),),
                  //                     ),
                  //
                  //                     // Container(
                  //                     //   decoration: BoxDecoration(
                  //                     //     border: Border.all(color: sel_index == index ? primaryColor : Colors.black38,width: 2),
                  //                     //     borderRadius: BorderRadius.circular(1000),
                  //                     //   ),
                  //                     //   padding: EdgeInsets.all(2),
                  //                     //   child: Icon(Icons.circle,color: sel_index == index ? primaryColor : Colors.black38,size: 15,),
                  //                     // ),
                  //                     // data['free'].toString() == 'off'?Icon(Icons.lock,color: textblackColor.withOpacity(0.2),size:30,):
                  //                     Icon(Icons.keyboard_arrow_right_outlined,color: textblackColor.withOpacity(0.2),),
                  //
                  //                   ],
                  //                 ),
                  //               ),
                  //
                  //             ],
                  //           ),
                  //         );
                  //       },
                  //     ),
                  //   ),
                  // ),

                ],
              ),
            ),
            // floatingActionButton: FloatingActionButton(
            //   shape: RoundedRectangleBorder(
            //     borderRadius: BorderRadius.circular(1000),
            //   ),
            //   backgroundColor: primaryColor,
            //   onPressed: (){
            //     Get.to(()=> webview_page(url: 'https://trogon.info/tutorpro/skyil/home/<USER>/55/3'));
            //   },
            //   child: Icon(Icons.arrow_forward_ios_rounded,color: textwhiteColor,),
            // ),
          );
        }
    );
  }
}
