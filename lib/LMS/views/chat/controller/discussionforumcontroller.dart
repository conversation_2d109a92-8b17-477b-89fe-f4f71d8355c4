import 'package:edutalim/LMS/views/chat/apis/discussion_api.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DiscussionForumController extends GetxController{



  bool isDiscussionForumLoading = false;
  int? selectedIndex;
  List discussionData = [

    {
      "user_name" : "<PERSON><PERSON><PERSON>",
      "user_id" : "12345",
      "user_image" : "assets/img/user_placeholder.png",
      "question" : "What is Stock Marketing ? ",
      "answers" : [
        {
          "user_name" : "Hisham",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "<PERSON>",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "<PERSON><PERSON>",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Lijosh",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
      ]
    },
    {
      "user_name" : "Arshad P",
      "user_id" : "12345",
      "user_image" : "assets/img/user_placeholder.png",
      "question" : "What is Stock Marketing ? ",
      "answers" : [
        {
          "user_name" : "Hisham",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Theresa",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Anas",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Lijosh",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
      ]
    },
    {
      "user_name" : "Arshad P",
      "user_id" : "12345",
      "user_image" : "assets/img/user_placeholder.png",
      "question" : "What is Stock Marketing ? ",
      "answers" : [
        {
          "user_name" : "Hisham",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Theresa",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Anas",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Lijosh",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
      ]
    },
    {
      "user_name" : "Arshad P",
      "user_id" : "12345",
      "user_image" : "assets/img/user_placeholder.png",
      "question" : "What is Stock Marketing ? ",
      "answers" : [
        {
          "user_name" : "Hisham",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Theresa",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Anas",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
        {
          "user_name" : "Lijosh",
          "user_image" : "assets/img/user_placeholder.png",
          "content" : "Lorem ipsum, placeholder or dummy text used in typesetting and graphic design for previewing layouts. It features scrambled Latin text, which emphasizes the design over content of the layout. It is the standard placeholder text of the printing and publishing industries.",
        },
      ]
    }


  ];

  //Discussion List
  var courseID;
  var discussionList;
  bool  isdiscussionListLoading =false;
  void getDiscussionList()async{
    isdiscussionListLoading= true; update();
    var data = await DiscussionApihandler.get_discussion_list(courseID.toString());
    isdiscussionListLoading= false; update();

    if (data['status'].toString() == '1') {
      discussionList =data["data"];
    }
  }

  // Sub discussion List
  var discussionID;
  var subDiscussionList;
  bool  isSubDiscussionListLoading =false;
  void getSubDiscussionList()async{
    isSubDiscussionListLoading= true; update();
    var data = await DiscussionApihandler.get_sub_discussion_list(courseID.toString(),discussionID.toString());
    isSubDiscussionListLoading= false; update();

    if (data['status'].toString() == '1') {
      subDiscussionList =data["data"];
    }
  }





  // var  content ="";
// var discussion_type ="0" ;
// var discussion_id ="0";
  final TextEditingController contentController = TextEditingController();
  bool  isAddDiscussLoading =false;
// var filPath;
  void addDiscussion(discussion_type,discussion_id,filPath)async{
    isAddDiscussLoading= true; update();
    var data = await DiscussionApihandler.add_discussion(
        course_id: courseID.toString(),
        discussion_id: discussion_id.toString(),
        discussion_type: discussion_type.toString(),
        content: contentController.text.toString(),
        filePath:filPath
    );
    isAddDiscussLoading= false; update();
    if(data['status'].toString() == '1'){
      print("Add Discussion success");
      if(discussion_type=="0"){
        getDiscussionList();
      }else{
        getSubDiscussionList();
      }

      Get.back();
    }
  }

}
