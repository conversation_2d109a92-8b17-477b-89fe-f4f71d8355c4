import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:edutalim/LMS/api/main_apis.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../components/constants.dart';
import '../../components/utils.dart';

class AssignmentController extends GetxController {
  bool isPageLoading = true;

  int selectedTab = 0;

  PageController tabPageController = PageController();

  void getAssignments(course_id) async {
    isPageLoading = true;
    var data = await ApiBaseHandler.assignmentsList(course_id);
    currentAssignments = data['data']['current'];
    completedAssignments = data['data']['completed'];
    isPageLoading = false;
    update();
  }

  bool isDetailsLoading = true;
  Map assignmentDetails = {};

  void getAssignmentDetails(assignmentId) async {
    isDetailsLoading = true;
    var data = await ApiBaseHandler.assignmentDetails(assignmentId);
    assignmentDetails = data['data'];
    isDetailsLoading = false;
    update();
  }

  bool isAssignmentUpload = false;

  void uploadAssignment( assignment_id) async {
    isAssignmentUpload = true;
    var data = await ApiBaseHandler.uploadAssignemnt(assignmentFile: assignmentfile!, assignment_id: assignment_id);
    if (data['status'].toString() == "1" || data['status'].toString() == "true") {
      showSubmitSuccess(Get.context!);
    } else {
      toast_error(data['message'].toString());
    }
    isAssignmentUpload = false;
    update();
  }

  List currentAssignments = [];
  List completedAssignments = [];
  List instructions = [];

  void showSubmitSuccess(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return GetBuilder<AssignmentController>(builder: (controller) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            backgroundColor: Colors.white, // White background
            content: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.check_circle,color: Colors.green),
                  const SizedBox(height: 20),
                  Text("Submission Successful!", style: TextStyle(fontFamily: font_bold, fontSize: Get.width / 25)),
                  const SizedBox(height: 15),
                  Text("Your instructor will\nreview your submission.", style: TextStyle(fontSize: Get.width / 30, fontFamily: font_regular, color: const Color(0xFF8B8B8B)), textAlign: TextAlign.center),
                  const SizedBox(height: 20),
                  common_button2(
                      onPressed: () {
                        Get.back();
                        Get.back();
                      },
                      borderRadius: BorderRadius.circular(10),
                      bg: primaryColor,
                      child: Text("Back to Home", style: TextStyle(fontWeight: FontWeight.w600, fontFamily: font_regular, color: Colors.white))),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  bool isDownloading = false;
  double downloadProgress = 0.0;
  bool isDownloadCompleted = false;

  Future<void> downloadFile(String url, String fileName) async {
    log('----step--------------------------------------2');

    // Step 1: Check for Storage Permission
    requestPermission();
    log('----step--------------------------------------3');

    try {
      isDownloading = true;
      update();

      log('----step--------------------------------------4');

      // Step 2: Get the appropriate directory
      Directory? downloadsDirectory;
      if (Platform.isAndroid) {
        downloadsDirectory = Directory('/storage/emulated/0/Download');
      } else if (Platform.isIOS) {
        // For iOS, use Documents directory which is accessible to users via Files app
        downloadsDirectory = await getApplicationDocumentsDirectory();
      }

      log('----step--------------------------------------5');
      log(downloadsDirectory.toString());

      // Step 3: Create the App-Specific Folder
      // final appFolder = Directory('${downloadsDirectory!.path}/$appName');
      // if (!await appFolder.exists()) {
      //   await appFolder.create(recursive: true);
      // }

      log('----step--------------------------------------6');

      final filePath = '${downloadsDirectory!.path}/$fileName';
      final dio = Dio();

      log('----step--------------------------------------7');

      await dio.download(
        url,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            downloadProgress = (received / total * 100).toDouble();
            update();
          }
        },
      );

      log('----step--------------------------------------8');

      // Step 5: Notify the User
      if (Platform.isAndroid) {
        toast_success('Successfully Downloaded to Internal Storage - Downloads');
      } else {
        toast_success('Successfully Downloaded to Files -> On My Phone $fileName');
      }

      log('----step--------------------------------------9');

      isDownloadCompleted = true;
      update();
    } catch (e) {
      ScaffoldMessenger.of(Get.context!).showSnackBar(
        SnackBar(content: Text('Download failed: $e')),
      );
    } finally {
      isDownloading = false;
      update();
    }
  }

  requestPermission() async {
    if (Platform.isAndroid) {
      PermissionStatus status = await Permission.storage.request();
      log("-----------------------------status-----------------------------$status");

      log('----sec---------------------------------------1');

      if (status.isGranted) {
        log('Storage permission granted');
        // Proceed with your functionality
        log('----sec---------------------------------------2');
      } else if (status.isDenied) {
        log('----sec---------------------------------------3');
        log('Storage permission denied. Requesting again...');
        // Request again
        status = await Permission.storage.request();
        log('----sec---------------------------------------4');
        if (status.isGranted) {
          log('Permission granted after requesting again.');
          log('----sec---------------------------------------5');
        } else if (status.isPermanentlyDenied) {
          log('----sec---------------------------------------6');
          log('Permission permanently denied.');
          await openAppSettings();
          log('----sec---------------------------------------7');
        } else {
          log('----sec---------------------------------------8');
          log('Permission still denied.');
          // await openAppSettings();
        }
      } else if (status.isPermanentlyDenied) {
        log('----sec---------------------------------------9');
        log('Permission permanently denied. Redirecting to settings...');
        await openAppSettings();
      }

      // return status.isGranted;
    } else {
      return true; // No special permissions needed for iOS
    }
  }

  File? assignmentfile;
  String? filePath;

  getFromFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: false,
      type: FileType.custom,
      allowedExtensions: ['pdf'],
    );

    if (result != null && result.files.single.path != null) {
      filePath = result.files.single.path;

      assignmentfile = File(filePath!);

      print("File picked: \$filePath"); // Debugging purpose
      update(); // Call update if using GetX or setState if using StatefulWidget
    }
  }
}
