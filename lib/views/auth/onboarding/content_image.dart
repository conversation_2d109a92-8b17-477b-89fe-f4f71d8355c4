import 'package:edutalim/components/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class content_image extends StatelessWidget {
  const content_image({
    Key? key,
    this.title,
    this.text,
    this.image,
    this.color,
  }) : super(key: key);
  final String? title,text, image,color;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[

        Container(
          // color: Colors.yellow,
          child: Image.asset(
            image!,
            height: Get.height/2.5,
            // width: Get.width/3,
            fit: BoxFit.cover,
          ),
        ),

      ],
    );
  }
}
