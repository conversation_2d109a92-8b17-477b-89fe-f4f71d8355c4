// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
//
// class LiveClass extends StatefulWidget {
//   const LiveClass({super.key});
//
//   @override
//   State<LiveClass> createState() => _LiveClassState();
// }
//
// class _LiveClassState extends State<LiveClass> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: textwhiteColor,
//       appBar: AppBar(
//         backgroundColor: textwhiteColor,
//         surfaceTintColor: textwhiteColor,
//         elevation: 5,
//         centerTitle: true,
//         leading:  GestureDetector(
//           onTap: (){
//             Get.back();
//           },
//           child: Container(
//             // decoration: BoxDecoration(
//             //     color: primaryColor,
//             //     borderRadius: BorderRadius.circular(100)
//             // ),
//             margin: EdgeInsets.all(10),
//             child: Icon(Icons.arrow_back,color: textblackColor,),
//           ),
//         ),
//         title: Container(
//           child: Text("Live",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w700,fontFamily: font_regular,fontSize: Get.width/24),),
//         ),
//       ),
//       body: SingleChildScrollView(child: Column(
//         children: [
//           SizedBox(height: 20,),
//           Image.asset('assets/temp_images/Live Class.png'),
//         ],
//       )),
//     );
//   }
// }
