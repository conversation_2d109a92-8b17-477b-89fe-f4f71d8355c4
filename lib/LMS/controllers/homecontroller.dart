import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import '../../components/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../api/main_apis.dart';
import '../views/dashboard/dashboard.dart';

class Homecontroller extends GetxController {
  bool isUniversityLoading = true;
  var universitPage;

  getUniversityDetails(id) async {
    isUniversityLoading = true;
    var data = await ApiBaseHandler.universityDetails(id);
    universitPage = data['data'];
    isUniversityLoading = false;
    update();
  }


  List packages = [
  ];
  String slectedpackage = '-1',selectedEmi = '-1';
  Map selectedplandata = {},selectedEmiData = {};
  bool iscoursedetailsLoad = false;
  List coursedetails = [];
  getCourseDetails(course_id) async {
    iscoursedetailsLoad = true;
    var data = await ApiBaseHandler.courseDetails(course_id);
    if(data['status'].toString() == "true" || data['status'].toString() == "1"){
      coursedetails = [data['data']];
    } else {
      toast_info(data['message'].toString());
    }
    iscoursedetailsLoad = false;
    update();
  }



  bool isHomeLoading = true;
  get_homedata() async {
    isHomeLoading = true;

    var data = await ApiBaseHandler.studentDashboard();

    banners = data['data']['banners'];
    reels = data['data']['reels'];
    categories = data['data']['categories'];
    log(categories['fintalim']['thumbnail'].toString());
    secondary_banners = data['data']['secondary_banners'];
    testimonials = data['data']['testimonials'];

    print('course_id-------------${GetStorage().read('course_id')}');
    isHomeLoading = false;
    update();
  }




  final CarouselSliderController carouselcontroller = CarouselSliderController();
  final CarouselSliderController carouselcontroller2 = CarouselSliderController();
  final GlobalKey<ScaffoldState> scaffoldkey = GlobalKey();
  int carouselindex = 0, carouselindex2 = 0;
  List banners = [];
  List secondary_banners = [];
  List reels = [];
  var categories;
  List testimonials = [];

  bool isEnrolling = false;

  enrolll_course(course_id) async {

    isEnrolling = true;
    update();

    var data = await ApiBaseHandler.autoEnroll(course_id);
    if(data['status'].toString() == "true" || data['status'].toString() == "1"){
      toast_success(data['message'].toString());
      GetStorage().write('home_index', '1');
      Get.offAll(dashboard());
    } else {
      toast_info(data['message'].toString());
    }

    isEnrolling = false;
    update();


  }

  switch_course(course_id) async {

    isEnrolling = true;
    update();

    var data = await ApiBaseHandler.switchCourse(course_id);

    log("Log Dataaaaa : "+data.toString());
    if(data['status'].toString() == "true" || data['status'].toString() == "1"){
      toast_success(data['message'].toString());
      GetStorage().write('home_index', '1');
      Get.offAll(dashboard());
    } else {
      toast_info(data['message'].toString());
    }

    isEnrolling = false;
    update();


  }


  bool isCategoryDetailsLoading = true;
  var categoryDetailsPage;

  getCategoryDetails(id) async {
    isCategoryDetailsLoading = true;
    var data = await ApiBaseHandler.categoryDetails(id);
    categoryDetailsPage = data['data'];
    isCategoryDetailsLoading = false;
    update();
  }


  @override
  void onInit() {
    super.onInit();
  }
}
