// import 'package:flutter/material.dart';
// import 'package:speekiai/components/constants.dart';

// class EditMessageTile extends StatelessWidget {
//   EditMessageTile({super.key});
//   final String message;
//   void Function()? onPressed;

//   @override
//   Widget build(BuildContext context) {
//     return ListTile(
//       title: const Text(
//         'Edit Message',
//         style: TextStyle(fontSize: 12),
//       ),
//       onTap: () {
//         showDialog(
//           context: context,
//           builder: (BuildContext context) {
//             TextEditingController editController =
//                 TextEditingController(text: message);
//             return AlertDialog(
//               title: const Text(
//                 'Edit Message',
//                 style: TextStyle(fontSize: 15),
//               ),
//               content: TextField(
//                 controller: editController,
//                 decoration: const InputDecoration(
//                   hintText: 'Enter new message',
//                   hintStyle: TextStyle(fontSize: 10),
//                 ),
//               ),
//               actions: [
//                 TextButton(
//                   onPressed: () {
//                     if (editController.text.isNotEmpty) {
//                       onEditMessage(messageId, editController.text);
//                       Navigator.of(context).pop(); // Close dialog
//                     }
//                   },
//                   child: Text(
//                     'Save',
//                     style: TextStyle(color: primaryColor),
//                   ),
//                 ),
//                 TextButton(
//                   onPressed: () {
//                     Navigator.of(context).pop(); // Close dialog
//                   },
//                   child: Text(
//                     'Cancel',
//                     style: TextStyle(color: primaryColor),
//                   ),
//                 ),
//               ],
//             );
//           },
//         );
//       },
//     );
//   }
// }
