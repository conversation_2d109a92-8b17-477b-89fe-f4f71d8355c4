// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_zoom_meeting/zoom_options.dart';
// import 'package:flutter_zoom_meeting/zoom_view.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:lottie/lottie.dart';
//
// class zoom_meeting_android extends StatefulWidget {
//   Map data;
//   String jwt_token;
//
//   zoom_meeting_android({required this.data, required this.jwt_token});
//
//   @override
//   State<zoom_meeting_android> createState() => _zoom_meeting_androidState();
// }
//
// class _zoom_meeting_androidState extends State<zoom_meeting_android> {
//   @override
//   void initState() {
//     super.initState();
//     joinMeetingAndroidAndIos();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: Container(
//         width: Get.width,
//         decoration: const BoxDecoration(
//             image: DecorationImage(
//           fit: BoxFit.cover,
//           opacity: 0.1,
//           image: AssetImage('assets/bg/zoom_bg.png'),
//         )),
//         child: Stack(
//           children: [
//             AppBar(
//               elevation: 0,
//               toolbarHeight: 0,
//               backgroundColor: const Color(0xff00FFFFFF),
//               iconTheme: const IconThemeData(color: Colors.black),
//             ),
//             SizedBox(
//               width: Get.width,
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Lottie.asset(
//                     'assets/lotties/zoom_back.json',
//                     width: MediaQuery.of(context).size.width / 2,
//                   ),
//                   SizedBox(
//                     height: 50,
//                   ),
//                   Text(
//                     "Click the 'Join Now'\n Button to Enter the Live Class",
//                     style: TextStyle(color: Colors.black54, fontFamily: "poppins_regular", fontWeight: FontWeight.w500, fontSize: MediaQuery.of(context).size.width / 30),
//                     textAlign: TextAlign.center,
//                   ),
//                   SizedBox(
//                     height: 30,
//                   ),
//                   GestureDetector(
//                     onTap: () {
//                       joinMeetingAndroidAndIos();
//                     },
//                     child: Container(
//                       width: Get.width / 1.5,
//                       alignment: Alignment.center,
//                       padding: const EdgeInsets.all(15),
//                       decoration: BoxDecoration(color: Colors.blue, border: Border.all(color: Colors.blue, width: 1), borderRadius: BorderRadius.circular(1000)),
//                       child: Text(
//                         "Join Now",
//                         style: TextStyle(color: Colors.white, fontWeight: FontWeight.w700, fontSize: Get.width / 20),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             Positioned(
//                 top: 0,
//                 left: 0,
//                 right: 0,
//                 child: AppBar(
//                   backgroundColor: Colors.transparent,
//                   elevation: 0,
//                   iconTheme: const IconThemeData(color: Colors.blue),
//                   automaticallyImplyLeading: false,
//                   leading: GestureDetector(
//                     onTap: () {
//                       Get.back();
//                     },
//                     child: Wrap(
//                       children: [
//                         Container(
//                           decoration: BoxDecoration(
//                             color: Colors.white,
//                             borderRadius: BorderRadius.circular(1000),
//                             boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.4), blurRadius: 5.0, spreadRadius: 1)],
//                           ),
//                           padding: const EdgeInsets.all(5),
//                           margin: const EdgeInsets.only(left: 20, top: 5),
//                           child: const Icon(Icons.keyboard_arrow_left, color: Colors.blue),
//                         ),
//                       ],
//                     ),
//                   ),
//                 )),
//           ],
//         ),
//       ),
//     );
//   }
//
//   // Uncomment this code after integrating Zoom
//   void joinMeetingAndroidAndIos() async {
//     ZoomOptions zoomOptions = ZoomOptions(
//       domain: "zoom.us",
//       jwtToken: widget.jwt_token,
//     );
//     var meetingOptions = ZoomMeetingOptions(
//       displayName: GetStorage().read('user_name').toString(),
//       meetingId: widget.data['meeting_id'].toString(),
//       meetingPassword: widget.data['meeting_password'].toString(),
//     );
//
//     // var zoom = ZoomAllInOneSdk();
//     var zoom = ZoomView();
//     zoom.initZoom(zoomOptions).then((results) {
//       print(results);
//       if (results[0] == 0) {
//         zoom.joinMeeting(meetingOptions).then((loginResult) {
//           print('loginResult-------------------$loginResult');
//         });
//
//         zoom.onMeetingStatus().listen((status) {
//           if (status[0] == "MEETING_STATUS_INMEETING") {
//             // print("User Status : $status ");
//             // MyCourseApisHandler.takeLiveAttendance(widget.data['id'].toString());
//             //
//             // controller.startAttendanceTimer(widget.data['id'].toString());
//           }
//           if (status[0] == "MEETING_STATUS_RECONNECTING") {
//             log('MEETING_STATUS_RECONNECTING------------------------');
//             print('MEETING_STATUS_RECONNECTING------------------------');
//           }
//           if (status[0] == "MEETING_STATUS_IDLE") {
//             log('MEETING_STATUS_IDLE------------------------');
//             print('MEETING_STATUS_IDLE------------------------');
//           }
//           if (status[0] == "MEETING_STATUS_DISCONNECTING") {
//             log('MEETING_STATUS_DISCONNECTING------------------------');
//             print('MEETING_STATUS_DISCONNECTING------------------------');
//           }
//
//           if (status[0] == "MEETING_STATUS_ENDED" || status[0] == "MEETING_STATUS_DISCONNECTING") {
//             // print("User Status : Leaving The Meeting ");
//             log('MEETING_STATUS_DISCONNECTING------------------------');
//             log('MEETING_STATUS_ENDED------------------------');
//             print('MEETING_STATUS_DISCONNECTING------------------------');
//             print('MEETING_STATUS_ENDED------------------------');
//             // MyCourseApisHandler.updateLiveAttendance(widget.data['id'].toString());
//             // controller.stopAttendanceTimer();
//           }
//         });
//       }
//     }).catchError((error) {
//       print("[Error Generated] : $error");
//     });
//   }
// }
