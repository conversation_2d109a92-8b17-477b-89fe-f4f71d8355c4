import 'package:edutalim/components/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DiscussionForum extends StatefulWidget {
  const DiscussionForum({super.key});

  @override
  State<DiscussionForum> createState() => _DiscussionForumState();
}

class _DiscussionForumState extends State<DiscussionForum> {
  final TextEditingController _commentController = TextEditingController();
  bool _showSendButton = false;
  @override
  void initState() {
    super.initState();
    _commentController.addListener(() {
      setState(() {
        _showSendButton = _commentController.text.isNotEmpty;
      });
    });
  }

  final List<Map<String, dynamic>> discussions = [
    {
      'userName': '<PERSON><PERSON><PERSON>',
      'date': '16 Dec 2024',
      'question':
          'A car of mass 1000 kg is moving with velocity of 20 m/s. it becomes stope of after applying?.',
      'likes': 24,
      'comments': 12,
      'isLiked': false,
      'image': 'assets/temp_images/user_thumb.png'
    },
    {
      'userName': '<PERSON><PERSON>',
      'date': '15 Dec 2024',
      'question':
          'What is the formula for calculating force? Can someone explain with example?',
      'likes': 18,
      'comments': 8,
      'isLiked': true,
      'image': 'assets/temp_images/user_thumb.png'
    },
    {
      'userName': 'Rahul Verma',
      'date': '14 Dec 2024',
      'question': 'How does Newton\'s third law apply to rocket propulsion?',
      'likes': 32,
      'comments': 15,
      'isLiked': false,
      'image': 'assets/temp_images/user_thumb.png'
    },
    {
      'userName': 'Neha Gupta',
      'date': '13 Dec 2024',
      'question': 'Best resources to learn quantum physics basics?',
      'likes': 45,
      'comments': 22,
      'isLiked': false,
      'image': 'assets/temp_images/user_thumb.png'
    },
    {
      'userName': 'Vikram Singh',
      'date': '12 Dec 2024',
      'question':
          'Difference between kinetic and potential energy with real-life examples?',
      'likes': 29,
      'comments': 14,
      'isLiked': true,
      'image': 'assets/temp_images/user_thumb.png'
    },
  ];

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  bool isLiked = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: textwhiteColor,
      body: Container(
        height: Get.height,
        width: Get.width,
        color: textblackColor.withOpacity(0.1),
        child: Column(
          children: [
            AppBar(
              backgroundColor: textwhiteColor,
              surfaceTintColor: textwhiteColor,
              centerTitle: false,
              title: Text(
                "Discussion Forum",
                style: TextStyle(
                  fontSize: Get.width / 25,
                  fontWeight: FontWeight.w900,
                ),
              ),
              actions: [
                GestureDetector(
                  onTap: () {
                    showAskDoubtBottomSheet();
                  },
                  child: Image.asset(
                    'assets/temp_images/ask_doubt.png',
                    width: Get.width / 5,
                  ),
                ),
                const SizedBox(width: 15),
              ],
            ),
            const SizedBox(height: 5),
            Expanded(
              child: SingleChildScrollView(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: discussions.length,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.zero,
                  itemBuilder: (context, index) {
                    var data = discussions[index];
                    return GestureDetector(
                      onTap: () {},
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 3),
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 5),
                          decoration: BoxDecoration(color: textwhiteColor),
                          width: double.infinity,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(100),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8),
                                        child: Image.asset(
                                          data['image'].toString(),
                                          height: 40,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          data['userName'].toString(),
                                          style: TextStyle(
                                            color: textblackColor,
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        Text(
                                          data['date'].toString(),
                                          style: TextStyle(
                                            color: Colors.grey,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: Text(
                                    data['question'],
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 10),
                                // TextField with comment box
                                Row(
                                  children: [
                                    // Like button (circular avatar)
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0),
                                        child: SizedBox(
                                          height: 36,
                                          child: TextField(
                                            controller: _commentController,
                                            style: TextStyle(fontSize: 14),
                                            decoration: InputDecoration(
                                              filled: true,
                                              fillColor: Colors.grey[200],
                                              contentPadding: EdgeInsets.only(
                                                left: 16.0,
                                                right: _showSendButton
                                                    ? 40
                                                    : 16.0, // Make space for button
                                                top: 8.0,
                                                bottom: 8.0,
                                              ),
                                              isDense: true,
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                                borderSide: BorderSide.none,
                                              ),
                                              hintText: 'Type comment...',
                                              hintStyle: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey[600],
                                              ),
                                              suffixIcon: _showSendButton
                                                  ? IconButton(
                                                      icon: Icon(Icons.send,
                                                          size: 20),
                                                      color: Colors.blue,
                                                      padding: EdgeInsets.zero,
                                                      onPressed: () {
                                                        // Handle send action
                                                        print(
                                                            'Sending: ${_commentController.text}');
                                                        _commentController
                                                            .clear();
                                                      },
                                                    )
                                                  : null,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),

                                    InkWell(
                                      onTap: () {
                                        // Handle like action
                                        setState(() {
                                          discussions[index]['isLiked'] =
                                              !discussions[index]['isLiked'];
                                          discussions[index]['likes'] +=
                                              discussions[index]['isLiked']
                                                  ? 1
                                                  : -1;
                                        });
                                      },
                                      child: Container(
                                        width: 30,
                                        height: 30,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color:
                                                  Colors.blue.withOpacity(0.2)),
                                          color: Colors.grey[200],
                                          shape: BoxShape.circle,
                                        ),
                                        child: Center(
                                          // Center the icon
                                          child: Icon(
                                            data['isLiked']
                                                ? Icons.thumb_up
                                                : Icons.thumb_up_outlined,
                                            size: 16,
                                            color: data['isLiked']
                                                ? Colors.blue
                                                : Colors.grey[
                                                    600], // Change color based on isLiked
                                          ),
                                        ),
                                      ),
                                    ),

                                    const SizedBox(width: 8),
                                    // Comment button (circular avatar)
                                    InkWell(
                                      onTap: () {
                                        showPublicCommentsBottomSheet(context);
                                      },
                                      child: Container(
                                        width:
                                            60, // Increased width to accommodate count
                                        height: 30, // Kept same height
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color:
                                                  Colors.blue.withOpacity(0.2)),
                                          color: Colors.grey[200],
                                          borderRadius: BorderRadius.circular(
                                              20), // Changed to rounded rectangle
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.mode_comment,
                                              size: 16,
                                              color: Colors.grey[600],
                                            ),
                                            SizedBox(width: 4), // Small spacing
                                            Text(
                                              '12', // Your comment count here
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey[600],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                  ],
                                ),
                                // Like and comment count row
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void showAskDoubtBottomSheet() {
    final TextEditingController _doubtController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allows the sheet to take more space
      backgroundColor: Colors.transparent, // Makes the rounded corners visible
      builder: (context) {
        return Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context)
                .viewInsets
                .bottom, // Adjusts for keyboard
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(25),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Ask Your Doubt',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                SizedBox(height: 15),

                // Text Field
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: TextField(
                    controller: _doubtController,
                    maxLines: 5,
                    minLines: 3,
                    decoration: InputDecoration(
                      hintText: 'Type your doubt here...',
                      hintStyle: TextStyle(color: Colors.grey),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.all(15),
                    ),
                    style: TextStyle(fontSize: 16),
                  ),
                ),
                SizedBox(height: 20),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 16),
                    ),
                    onPressed: () {
                      if (_doubtController.text.isNotEmpty) {
                        // Handle doubt submission
                        print('Doubt submitted: ${_doubtController.text}');
                        Navigator.pop(context);
                        // Show success message
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Your doubt has been submitted!'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    },
                    child: Text(
                      'Ask Doubt',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void showPublicCommentsBottomSheet(BuildContext context) {
    // Sample comments data with user profiles and timestamps
    List<Map<String, dynamic>> comments = [
      {
        'username': 'Alex Johnson',
        'content': 'This event was amazing! The speakers were fantastic.',
        'profile': 'assets/temp_images/user_thumb.png',
        'time': '2 hours ago',
      },
      {
        'username': 'Sarah Miller',
        'content': 'Does anyone have photos from the afterparty?',
        'profile': 'assets/temp_images/user_thumb.png',
        'time': '1 hour ago',
      },
      {
        'username': 'Michael Chen',
        'content': 'Looking forward to the next one!',
        'profile': 'assets/temp_images/user_thumb.png',
        'time': '45 mins ago',
      },
    ];

    TextEditingController commentController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 16,
                right: 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  SizedBox(
                    height: 10,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Comments',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                  ),

                  // Comments List
                  Expanded(
                    child: ListView.builder(
                      itemCount: comments.length,
                      itemBuilder: (context, index) {
                        final comment = comments[index];
                        return CommentCard(
                          username: comment['username'],
                          content: comment['content'],
                          profileImage: comment['profile'],
                          time: comment['time'],
                        );
                      },
                    ),
                  ),

                  // Add Comment Section
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16, top: 8),
                    child: Row(
                      children: [
                        // User Avatar
                        CircleAvatar(
                          radius: 20,
                          backgroundImage:
                              AssetImage('assets/temp_images/user_thumb.png'),
                        ),
                        const SizedBox(width: 12),

                        // Comment Input
                        Expanded(
                          child: TextField(
                            controller: commentController,
                            decoration: InputDecoration(
                              hintText: 'Write a public comment...',
                              filled: true,
                              fillColor: Colors.grey[200],
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(24),
                                borderSide: BorderSide.none,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),

                        // Post Button
                        IconButton(
                          icon: const Icon(Icons.send),
                          color: Colors.blue,
                          onPressed: () {
                            final content = commentController.text.trim();
                            if (content.isNotEmpty) {
                              setState(() {
                                comments.insert(0, {
                                  'username': 'You',
                                  'content': content,
                                  'profile':
                                      'assets/temp_images/user_thumb.png',
                                  'time': 'Just now',
                                });
                                commentController.clear();
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

// Example usage:
// ElevatedButton(
//   onPressed: () => showPublicCommentsBottomSheet(context),
//   child: Text('Show Comments'),
// )
}

class CommentCard extends StatelessWidget {
  final String username;
  final String content;
  final String profileImage;
  final String time;

  const CommentCard({
    Key? key,
    required this.username,
    required this.content,
    required this.profileImage,
    required this.time,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Info Row
            Row(
              children: [
                CircleAvatar(
                  radius: 18,
                  backgroundImage: AssetImage(profileImage),
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      username,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      time,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Comment Content
            Text(content),
            const SizedBox(height: 8),

            // Like Button
          ],
        ),
      ),
    );
  }
}
