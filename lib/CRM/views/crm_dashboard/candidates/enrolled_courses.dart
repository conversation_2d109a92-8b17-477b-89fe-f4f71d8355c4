import 'package:edutalim/CRM/controller/candidatecontroller.dart';
import 'package:edutalim/CRM/controller/candidateenrolledcontroller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../components/constants.dart';
import '../../../../components/utils.dart';

class enrolled_courses extends StatefulWidget {
  Map data;

  enrolled_courses({required this.data});

  @override
  State<enrolled_courses> createState() => _leads_listState();
}

class _leads_listState extends State<enrolled_courses> {



  CandidateEnrolledCoures controller = Get.put(CandidateEnrolledCoures());

  @override
  void initState() {
    controller.get_branches();
    controller.get_courses();
    controller.get_enrolled_courses();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "Enrolled Courses",
          style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16),
        ),
      ),
      body: Column(
        children: [
          SizedBox(
            height: 10,
          ),
          GestureDetector(
            onTap: () {
              filter_bottom_sheet();
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20),
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // Image.asset('assets/icons/pie_chart_2.png',width: Get.width/12,),
                      Icon(Icons.add),
                      SizedBox(
                        width: 15,
                      ),
                      Container(
                        child: Text(
                          "Enroll a course",
                          style: TextStyle(color: Colors.black, fontWeight: FontWeight.w700, fontSize: Get.width / 30),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 15,
          ),
          controller.isLoading
              ? loader()
              : controller.enrolled_courses.isEmpty
                  ? Container(
                      height: Get.width,
                      width: Get.width,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: Get.width / 4,
                            height: Get.width / 4,
                            child: Image.asset(
                              'assets/icons/no_data.png',
                              width: Get.width / 4,
                              errorBuilder: (a, b, c) {
                                return Image.asset(
                                  'assets/images/avater_placeholder.jpg',
                                  fit: BoxFit.cover,
                                );
                              },
                            ),
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Container(
                            child: Text(
                              "No Data Found",
                              style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16),
                            ),
                          ),
                        ],
                      ),
                    )
                  : Expanded(
                      child: ListView.builder(
                        physics: ScrollPhysics(),
                        itemCount: controller.enrolled_courses.length,
                        shrinkWrap: true,
                        itemBuilder: (context, index1) {
                          return Container(
                            margin: EdgeInsets.only(bottom: 12),
                            child: Stack(
                              children: [
                                Container(
                                  width: Get.width,
                                  padding: EdgeInsets.symmetric(vertical: 20, horizontal: 12),
                                  margin: EdgeInsets.symmetric(horizontal: 20),
                                  decoration: BoxDecoration(color: Colors.white, boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 6)], borderRadius: BorderRadius.circular(12)),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 12),
                                        // color: Colors.yellow,
                                        // decoration: BoxDecoration(),
                                        child: Text(
                                          (index1 + 1).toString(),
                                          style: TextStyle(fontFamily: 'poppins_regular', fontWeight: FontWeight.w600, color: Colors.black, fontSize: Get.width * .04),
                                        ),
                                      ),
                                      SizedBox(
                                        width: 10,
                                      ),
                                      Expanded(
                                        child: Container(
                                          // color: Colors.yellow,
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              // Container(
                                              //   width: Get.width*.7,
                                              //   // color: Colors.yellow,
                                              //   child: Text("Name",style: TextStyle(fontFamily: 'poppins_bold',color: Colors.black,fontSize: Get.width*.04),),
                                              // ),
                                              Container(
                                                width: Get.width * .7,
                                                // color: Colors.yellow,
                                                child: Text(
                                                  controller.enrolled_courses[index1]['course_name'].toString(),
                                                  style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontWeight: FontWeight.w600, fontSize: Get.width * .035),
                                                ),
                                              ),
                                              Container(
                                                width: Get.width * .7,
                                                // color: Colors.yellow,
                                                child: Text(
                                                  'Branch - ${controller.enrolled_courses[index1]['branch_name'].toString()}',
                                                  style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontWeight: FontWeight.w500, fontSize: Get.width * .035),
                                                ),
                                              ),
                                              Container(
                                                width: Get.width * .7,
                                                // color: Colors.yellow,
                                                child: Text(
                                                  'Batch - ${controller.enrolled_courses[index1]['batch_name'].toString()}',
                                                  style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontWeight: FontWeight.w500, fontSize: Get.width * .035),
                                                ),
                                              ),
                                              Container(
                                                child: Text(
                                                  controller.enrolled_courses[index1]['enrolled_date'].toString(),
                                                  style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: Get.width * .03),
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Positioned(
                                //     top: 0,
                                //     right: 25,
                                //     child: IconButton(onPressed: (){}, icon: Icon(CupertinoIcons.delete_solid,color: Colors.red,)))
                              ],
                            ),
                          );
                        },
                      ),
                    ),
          SizedBox(height: 15)
        ],
      ),
    );
  }

  // filter bottom sheet sec
  void filter_bottom_sheet() {
    showModalBottomSheet(
      backgroundColor: Colors.white,
      isScrollControlled: true,
      context: context,
      useSafeArea: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      builder: (builder) {
        return GetBuilder<CandidateEnrolledCoures>(
          builder: (controller) {
            return Padding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Stack(
                children: [
                  Container(
                    constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height / 1.5),
                    child: Column(
                      children: [
                        SizedBox(height: 30),
                        Expanded(
                          child: SingleChildScrollView(
                            physics: ScrollPhysics(),
                            child: Wrap(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text("Course",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            StatefulBuilder(builder: (context, setState) {
                                              return Container(
                                                width: MediaQuery.of(context).size.width,
                                                decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                                child: DropdownButtonHideUnderline(
                                                  child: ButtonTheme(
                                                    alignedDropdown: true,
                                                    child: StatefulBuilder(builder: (context, setState) {
                                                      return DropdownButton<String>(
                                                        value: controller.sel_course,
                                                        iconSize: 30,
                                                        dropdownColor: Colors.white,
                                                        style: TextStyle(color: Colors.black54, fontSize: 14),
                                                        hint: Text('- select a course -'),
                                                        onChanged: (newValue) async {
                                                          controller.sel_course = newValue;
                                                          controller.update();
                                                          await controller.get_batches(controller.sel_branch, controller.sel_course); // Fetch courses
                                                          controller.update();
                                                        },
                                                        items: controller.courses.map<DropdownMenuItem<String>>((item) {
                                                          return DropdownMenuItem<String>(
                                                            value: item['id'].toString(),
                                                            child: StatefulBuilder(builder: (context, setState) {
                                                              return SizedBox(
                                                                width: MediaQuery.of(context).size.width / 1.6,
                                                                child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                              );
                                                            }),
                                                          );
                                                        }).toList(),
                                                      );
                                                    }),
                                                  ),
                                                ),
                                              );
                                            }),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text("Branch",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            Container(
                                              width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_branch,
                                                    iconSize: 30,
                                                    dropdownColor: Colors.white,
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('- select a branch -'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_branch = newValue;
                                                      controller.update();
                                                      await controller.get_batches(controller.sel_branch, controller.sel_course);
                                                      controller.update();
                                                    },
                                                    items: controller.branches.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                          value: item['id'].toString(),
                                                          child: SizedBox(
                                                            width: MediaQuery.of(context).size.width / 1.6,
                                                            child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                          ));
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      // -----------------------------sort  sec--------------------------------------------------------
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 10),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 10),
                                            Text("Batch",
                                                style: TextStyle(fontFamily: "poppins_regular", fontSize: MediaQuery.of(context).size.width / 25, fontWeight: FontWeight.w500, color: Colors.black),
                                                textAlign: TextAlign.left),
                                            SizedBox(height: 10),
                                            Container(
                                              width: MediaQuery.of(context).size.width,
                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), border: Border.all(color: Colors.black26, width: 0.5), color: Colors.white),
                                              child: DropdownButtonHideUnderline(
                                                child: ButtonTheme(
                                                  alignedDropdown: true,
                                                  child: DropdownButton<String>(
                                                    value: controller.sel_batch,
                                                    iconSize: 30,
                                                    dropdownColor: Colors.white,
                                                    style: TextStyle(color: Colors.black54, fontSize: 14),
                                                    hint: Text('- select a batch -'),
                                                    onChanged: (newValue) async {
                                                      controller.sel_batch = newValue;
                                                      controller.update();
                                                    },
                                                    items: controller.batches.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['id'].toString(),
                                                        child: SizedBox(
                                                          width: MediaQuery.of(context).size.width / 1.6,
                                                          child: Text(item['title'].toString(), style: TextStyle(fontSize: MediaQuery.of(context).size.width / 27), maxLines: 2),
                                                        ),
                                                      );
                                                    }).toList(),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 20),
                                          ],
                                        ),
                                      ),

                                      SizedBox(height: 30),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    Get.back();
                                    controller.get_enroll_a_course();
                                  },
                                  child: Container(
                                    width: Get.width / 4,
                                    decoration: BoxDecoration(color: primaryColor, border: Border.all(color: primaryColor, width: 1), borderRadius: BorderRadius.circular(1000)),
                                    padding: EdgeInsets.symmetric(vertical: 15),
                                    child: controller.addbtn_press
                                        ? SizedBox(height: 15, width: 15, child: CircularProgressIndicator(color: Colors.white))
                                        : Text("Enroll Now", textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontFamily: "poppins_regular", fontWeight: FontWeight.w600)),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                  Positioned(right: 0, left: 0, top: 0, child: Container(color: Colors.transparent, child: Image.asset('assets/icons/drag_handle.png'))),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
