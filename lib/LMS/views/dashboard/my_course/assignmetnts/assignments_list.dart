import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../../controllers/assignmentcontroller.dart';
import 'assignment_details.dart';

class assignment_list extends StatefulWidget {
  String course_id;

  assignment_list({required this.course_id});

  @override
  State<assignment_list> createState() => _assignment_listState();
}

class _assignment_listState extends State<assignment_list> {
  AssignmentController controller = Get.put(AssignmentController());

  @override
  void initState() {
    controller.getAssignments(widget.course_id.toLowerCase());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AssignmentController>(builder: (context) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          children: [
            // tool bar
            Container(
              decoration: BoxDecoration(color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(.1), blurRadius: 60, spreadRadius: 0, offset: const Offset(0, 3))]),
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 0),
              child: Column(
                children: [
                  const SizedBox(
                    height: 15,
                  ),
                  AppBar(
                    backgroundColor: Colors.white,
                    surfaceTintColor: Colors.white,
                    toolbarHeight: 0,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                            color: Colors.transparent,
                            padding: const EdgeInsets.all(5),
                            child: Icon(
                              CupertinoIcons.left_chevron,
                              color: Colors.black,
                              size: Get.width / 18,
                            )),
                      ),
                      const SizedBox(width: 15),
                      Text(
                        "Assignments",
                        style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 23),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                ],
              ),
            ),

            // const SizedBox(height: 20),

            Container(
              decoration: BoxDecoration(color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.2), blurRadius: 5, offset: const Offset(0, 5))]),
              child: IntrinsicHeight(
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          controller.selectedTab = 0;
                          controller.update();
                        },
                        child: Container(
                          color: Colors.transparent,
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 5),
                                    child: Text(
                                      "Current",
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: Get.width / 25,
                                      ),
                                    ),
                                  ),
                                  if (controller.selectedTab == 0)
                                    Container(
                                      width: 5,
                                      height: 5,
                                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), color: primaryColor),
                                    )
                                ],
                              ),
                              if (controller.selectedTab == 0)
                                Divider(
                                  thickness: 2,
                                  color: primaryColor,
                                  height: 2,
                                )
                            ],
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          controller.selectedTab = 1;
                          controller.update();
                        },
                        child: Container(
                          color: Colors.transparent,
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 5),
                                    child: Text(
                                      "Completed",
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: Get.width / 25,
                                      ),
                                    ),
                                  ),
                                  if (controller.selectedTab == 1)
                                    Container(
                                      width: 5,
                                      height: 5,
                                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), color: primaryColor),
                                    )
                                ],
                              ),
                              if (controller.selectedTab == 1)
                                Divider(
                                  thickness: 2,
                                  color: primaryColor,
                                  height: 2,
                                )
                            ],
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),

            controller.isPageLoading
                ? Expanded(child: Center(child: loader()))
                : Expanded(
                    child: PageView.builder(
                        itemCount: 2,
                        controller: controller.tabPageController,
                        onPageChanged: (index) {
                          controller.selectedTab = index;
                          controller.update();
                        },
                        itemBuilder: (_, index) {
                          return Container(
                              child: controller.selectedTab == 0
                                  ? Container(
                                      child: controller.currentAssignments.isNotEmpty
                                          ? ListView.builder(
                                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                                              shrinkWrap: true,
                                              itemCount: controller.currentAssignments.length,
                                              itemBuilder: (_, index) {
                                                Map data = controller.currentAssignments[index];
                                                return Container(
                                                  padding: const EdgeInsets.all(16),
                                                  margin: const EdgeInsets.only(bottom: 15),
                                                  decoration: BoxDecoration(color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.2), blurRadius: 5)], borderRadius: BorderRadius.circular(8)),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(data['name'].toString(), style: TextStyle(color: secondaryColor, fontSize: Get.width / 26, fontWeight: FontWeight.w600)),
                                                      const SizedBox(height: 7),
                                                      Text(
                                                        data['description'].toString(),
                                                        style: TextStyle(
                                                          color: const Color(0xFF7B7B7B),
                                                          fontSize: Get.width / 31,
                                                          fontWeight: FontWeight.w400,
                                                          fontFamily: font_regular,
                                                        ),
                                                      ),
                                                      const SizedBox(height: 15),
                                                      Row(
                                                        crossAxisAlignment: CrossAxisAlignment.end,
                                                        children: [
                                                          Expanded(
                                                              child: Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Text('Due date', style: TextStyle(color: textblackColor.withValues(alpha: 0.5), fontSize: Get.width / 28, fontFamily: font_regular)),
                                                              const SizedBox(height: 5),
                                                              Text(
                                                                data['due_date'].toString(),
                                                                style: TextStyle(
                                                                  color: data['is_due'].toString() == "1" ? Colors.red : Colors.black,
                                                                  fontSize: Get.width / 28,
                                                                  fontFamily: font_regular,
                                                                  fontWeight: FontWeight.w600,
                                                                ),
                                                              )
                                                            ],
                                                          )),
                                                          const SizedBox(width: 15),
                                                          common_button2(
                                                            height: Get.width / 10,
                                                            onPressed: () {
                                                              Get.to(() => assignment_details(assignment_id: data['id'].toString(), from: "current"))!.then((_) {
                                                                controller.getAssignments(widget.course_id.toString());
                                                              });
                                                            },
                                                            bg: primaryColor,
                                                            borderRadius: BorderRadius.circular(4),
                                                            child: Text("     View Details    ", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 30, color: Colors.white)),
                                                          )
                                                        ],
                                                      )
                                                    ],
                                                  ),
                                                );
                                              })
                                          : SizedBox(height: Get.width, child: const Center(child: Text(" No Current Assignments Found"))),
                                    )
                                  : Container(
                                      child: controller.completedAssignments.isNotEmpty
                                          ? ListView.builder(
                                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                                              shrinkWrap: true,
                                              itemCount: controller.completedAssignments.length,
                                              itemBuilder: (_, index) {
                                                Map data = controller.completedAssignments[index];
                                                return Container(
                                                  padding: const EdgeInsets.all(16),
                                                  margin: const EdgeInsets.only(bottom: 15),
                                                  decoration: BoxDecoration(color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.2), blurRadius: 5)], borderRadius: BorderRadius.circular(8)),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        data['name'].toString(),
                                                        style: TextStyle(color: secondaryColor, fontSize: Get.width / 26, fontWeight: FontWeight.w600),
                                                      ),
                                                      const SizedBox(height: 7),
                                                      Text(
                                                        data['description'].toString(),
                                                        style: TextStyle(color: const Color(0xFF7B7B7B), fontSize: Get.width / 31, fontWeight: FontWeight.w400, fontFamily: font_regular),
                                                      ),
                                                      const SizedBox(height: 15),
                                                      Row(
                                                        crossAxisAlignment: CrossAxisAlignment.end,
                                                        children: [
                                                          Expanded(
                                                              child: Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Text('Submission date', style: TextStyle(color: textblackColor.withValues(alpha: 0.5), fontSize: Get.width / 28, fontFamily: font_regular)),
                                                              const SizedBox(
                                                                height: 5,
                                                              ),
                                                              Text(data['submit_date'].toString(), style: TextStyle(color: Colors.black, fontSize: Get.width / 28, fontFamily: font_regular)),
                                                            ],
                                                          )),
                                                          const SizedBox(width: 15),
                                                          common_button2(
                                                            height: Get.width / 10,
                                                            onPressed: () {
                                                              Get.to(() => assignment_details(assignment_id: data['id'].toString(), from: "result"))!.then((_) => controller.getAssignments(widget.course_id.toString()));

                                                              // Get.to(() => assignment_details(
                                                              //       assignment_id: data['id'].toString(),
                                                              //       from: "result",
                                                              //     ));
                                                            },
                                                            bg: primaryColor,
                                                            borderRadius: BorderRadius.circular(4),
                                                            child: Text("     View Result    ", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 30, color: Colors.white)),
                                                          )
                                                        ],
                                                      )
                                                    ],
                                                  ),
                                                );
                                              })
                                          : SizedBox(
                                              height: Get.width,
                                              child: const Center(
                                                child: Text(" No Upcoming Assignments Found"),
                                              ),
                                            ),
                                    ));
                        }),
                  )
          ],
        ),
      );
    });
  }
}
