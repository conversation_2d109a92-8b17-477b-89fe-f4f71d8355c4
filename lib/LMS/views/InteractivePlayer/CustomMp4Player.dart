import 'dart:developer';

import 'package:edutalim/LMS/views/InteractivePlayer/widgets/PlayerControllers.dart';
import 'package:edutalim/LMS/views/InteractivePlayer/widgets/player_intraction.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:video_player/video_player.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'Controller/VideoPlayerController.dart';
import 'better_player.dart';

class CustomMp4Player extends StatefulWidget {
  CustomMp4Player({super.key, required this.video_data});

  Map video_data;

  @override
  State<CustomMp4Player> createState() => _CustomMp4PlayerState();
}

class _CustomMp4PlayerState extends State<CustomMp4Player> {
  VideoPlayerGetXController controller = Get.put(VideoPlayerGetXController());

  @override
  void initState() {
    WakelockPlus.enable();
    controller.videoFiles = widget.video_data['video_links'];
    controller.videoData = widget.video_data;
    if (controller.videoFiles.isNotEmpty) {
      controller.isVideoError = false;
      log("On Post");
      controller.selectedFile = controller.videoFiles[0];
      controller.playerController = VideoPlayerController.networkUrl(Uri.parse(controller.selectedFile!['video_url'].toString()))
        ..initialize().then((_) {

          setState(() {
            controller.playerController.addListener(controller.onPlayerEvent);
            controller.playerController.play();
            log("Video ID for seeking: ${controller.videoData['id'].toString()}");

            // --- Retrieve and Parse Stored Duration ---
            final storedDurationString =
            GetStorage().read<String>(controller.videoData['id'].toString());
            print("Stored duration string: $storedDurationString");

            if (storedDurationString != null && storedDurationString.isNotEmpty) {
              try {
                Duration? seekDuration = parseDurationString(storedDurationString);
                if (seekDuration != null) {
                  print("Parsed seek position: $seekDuration");
                  // Check if the seek position is valid (not negative and within video duration)
                  // Note: controller.playerController.value.duration might not be available immediately
                  // if the video headers haven't fully loaded. You might need to delay this check
                  // or be optimistic. For now, we'll just check non-negativity.
                  if (seekDuration.isNegative) {
                    print("Warning: Parsed duration is negative. Not seeking.");
                  } else {
                    // Check if player's duration is available and if seek is within bounds
                    final videoDuration = controller.playerController.value.duration;
                    if (videoDuration != Duration.zero && seekDuration > videoDuration) {
                      print("Warning: Seek duration ($seekDuration) exceeds video duration ($videoDuration). Seeking to end.");
                      controller.playerController.seekTo(videoDuration);
                    } else {
                      controller.playerController.seekTo(seekDuration);
                    }
                  }
                } else {
                  print("Error: Could not parse stored duration string: $storedDurationString");
                }
              } catch (e) {
                print("Error parsing or seeking with stored duration: $e");
                // Optionally, handle the error, e.g., by not seeking or seeking to Duration.zero
              }
            } else {
              print("No stored seek position found for this video.");
            }
            // --- End of Duration Parsing and Seeking ---

            controller.startShowTimer();
            print("Player initialized and playing.");
          });

          // setState(() {
          //   controller.playerController.addListener(controller.onPlayerEvent);
          //   controller.playerController.play();
          //   log(controller.videoData['id'].toString());
          //   print("Current Need Seek Position : ${GetStorage().read(controller.videoData['id'].toString())}");
          //   print("Current Video ID : ${controller.videoData['id'].toString()}");
          //   if (GetStorage().read(controller.videoData['id']) != null) {
          //
          //     controller.playerController.seekTo(GetStorage().read(controller.videoData['id'].toString()));
          //   }
          //   controller.startShowTimer();
          //   print("initialised");
          // });

        }).catchError((error) {
          print("Errror$error");
          setState(() {
            controller.isVideoError = true;
          });
        });
    }
    super.initState();
  }

  Duration? parseDurationString(String durationString) {
    try {
      final parts = durationString.split(':');
      if (parts.length != 3) return null; // HH, MM, SS.micros

      final hours = int.tryParse(parts[0]);
      final minutes = int.tryParse(parts[1]);

      final secondParts = parts[2].split('.');
      final seconds = int.tryParse(secondParts[0]);
      final microseconds = secondParts.length > 1 ? int.tryParse(secondParts[1]) : 0;

      if (hours == null || minutes == null || seconds == null || microseconds == null) {
        return null;
      }

      // Basic validation for time components
      if (hours < 0 || minutes < 0 || minutes >= 60 || seconds < 0 || seconds >= 60 || microseconds < 0 || microseconds > 999999) {
        print("Warning: Invalid time component in duration string: $durationString");
        return null; // Or handle more gracefully, e.g., clamp values
      }


      return Duration(
        hours: hours,
        minutes: minutes,
        seconds: seconds,
        microseconds: microseconds,
      );
    } catch (e) {
      print("Exception while parsing duration string '$durationString': $e");
      return null;
    }
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    GetStorage().write(controller.videoData['id'].toString(), controller.playerController.value.position.toString());
    controller.playerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VideoPlayerGetXController>(builder: (controller) {
      return Container(
          child: controller.isVideoError
              ? AspectRatio(aspectRatio: 16 / 9, child: better_player_page(data: controller.sel_video ?? {"video_links": []}))
              : controller.playerController.value.isInitialized
                  ? AspectRatio(
                      aspectRatio: controller.playerController.value.aspectRatio,
                      child: Stack(
                        children: [
                          GestureDetector(
                            onTap: () {
                              controller.startShowTimer();
                            },
                            child: Container(
                              width: MediaQuery.of(context).size.width,
                              alignment: Alignment.center,
                              child: AspectRatio(
                                aspectRatio: controller.playerController.value.aspectRatio,
                                child: VideoPlayer(controller.playerController),
                              ),
                            ),
                          ),
                          if (controller.showControl)
                            Positioned(
                                top: 0,
                                bottom: 0,
                                left: 0,
                                right: 0,
                                child: AnimatedOpacity(
                                  opacity: controller.showControl ? 1.0 : 0.0,
                                  duration: const Duration(milliseconds: 300),
                                  child: GestureDetector(
                                      onTap: () {
                                        controller.showControl = false;
                                        controller.update();
                                      },
                                      child: PlayerControllers(isFullScreen: false)),
                                )),
                          if (controller.isShowingTheIntraction)
                            Positioned(
                                top: 0,
                                bottom: 0,
                                left: 0,
                                right: 0,
                                child: Container(margin: EdgeInsets.symmetric(horizontal: 10, vertical: 5), decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), border: Border.all(width: 0.3, color: Colors.grey)), child: PlayerInteraction(isFullScreen: false))),
                        ],
                      ),
                    )
                  : AspectRatio(
                      aspectRatio: 16 / 9,
                      child: Container(
                          color: Colors.black,
                          child: const Center(
                              child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.red,
                          )))));
    });
  }
}
