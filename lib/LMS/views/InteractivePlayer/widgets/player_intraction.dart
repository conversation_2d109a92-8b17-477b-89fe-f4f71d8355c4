import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:video_player/video_player.dart';

import 'package:edutalim/components/utils.dart';
import '../Controller/VideoPlayerController.dart';

class PlayerInteraction extends StatefulWidget {
  bool isFullScreen;

  PlayerInteraction({
    required this.isFullScreen,
    super.key,
  });

  @override
  _PlayerInteractionState createState() => _PlayerInteractionState();
}

class _PlayerInteractionState extends State<PlayerInteraction> {
  VideoPlayerGetXController controller = Get.put(VideoPlayerGetXController());

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VideoPlayerGetXController>(builder: (controller) {
      return Container(
        child: Stack(
          children: [



            // test area
            // Text(controller.videoData['actions_in_player'][controller.detected_actions_index]['answer_index'].toString()),

            Container(
              width: Get.width,
              height: Get.height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
              padding: EdgeInsets.only(top: 10,left: 10,right: 10,bottom: 10),
              child:
              controller.isInteractionIsSubmitted
                  ?
              Container(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [

                    controller.selectedMultipleChoiceOption == int.parse(controller.videoData['actions_in_player'][controller.detected_position_index]['answer_index'].toString()) ?
                    Image.asset('lib/views/InteractivePlayer/assets/check.png',width: Get.width/8,)
                    :
                    Image.asset('lib/views/InteractivePlayer/assets/close.png',width: Get.width/15,),


                    SizedBox(height: 15,),

                    controller.selectedMultipleChoiceOption == int.parse(controller.videoData['actions_in_player'][controller.detected_position_index]['answer_index'].toString()) ?
                    Text("Correct Answer!",style: TextStyle(fontWeight: FontWeight.w600,color: CupertinoColors.systemGreen,fontSize: Get.width/20),)
                        :
                    Text("Your answer is Wrong!",style: TextStyle(fontWeight: FontWeight.w600,color: CupertinoColors.systemRed,fontSize: Get.width/25),),

                    if(controller.selectedMultipleChoiceOption != int.parse(controller.videoData['actions_in_player'][controller.detected_position_index]['answer_index'].toString()))
                    Container(
                      margin: EdgeInsets.only(top: 10),
                      child: Stack(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: CupertinoColors.systemGreen.withValues(alpha: 0.05),
                              border: Border.all(color: CupertinoColors.systemGreen,width: 0.5),
                            ),
                            width: Get.width/1.2,
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                              controller.videoData['actions_in_player'][controller.detected_position_index]['answers'][int.parse(controller.videoData['actions_in_player'][controller.detected_position_index]['answer_index'].toString())].toString(),
                              style: TextStyle(fontWeight: FontWeight.w600,color: CupertinoColors.systemGreen,fontSize: Get.width/25),
                            ),
                          ),

                          Positioned(
                            child: Container(
                              decoration: BoxDecoration(
                                color: CupertinoColors.systemGreen,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(5),
                                  bottomRight: Radius.circular(5),
                                )
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 5,vertical: 0),
                              child: Text("Ans",style: TextStyle(fontSize: Get.width/40,color: Colors.white),),
                            ),
                          ),

                        ],
                      ),
                    ),

                    const SizedBox(height: 15),

                    Center(
                      child:  GestureDetector(
                        onTap: () async {
                          controller.selectedMultipleChoiceOption = null;
                          controller.isInteractionIsSubmitted = false;
                          controller.isShowingTheIntraction = false;
                          await controller.playerController.seekTo( controller.playerController.value.position + Duration(milliseconds: 1000));
                          await controller.playerController.play();
                          controller.update();
                        },
                        child: Container(
                          decoration: BoxDecoration(color:
                          controller.selectedMultipleChoiceOption == int.parse(controller.videoData['actions_in_player'][controller.detected_position_index]['answer_index'].toString()) ?
                          CupertinoColors.systemGreen
                              :
                          CupertinoColors.systemBlue,
                              borderRadius: BorderRadius.circular(1000)),
                          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                          child:
                          controller.selectedMultipleChoiceOption == int.parse(controller.videoData['actions_in_player'][controller.detected_position_index]['answer_index'].toString()) ?
                          Text("Continue", style: TextStyle(fontSize: Get.width / 35, fontWeight: FontWeight.w600, color: Colors.white),)
                              :
                          Text("Continue Watching", style: TextStyle(fontSize: Get.width / 35, fontWeight: FontWeight.w600, color: Colors.white),),
                        ),
                      ),
                    ),

                  ],
                ),
              )
                  :
              SingleChildScrollView(
                child: Container(
                  child:
                  controller.videoData['positions'].isEmpty ?
                  Container()
                  :
                  Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [

                      const SizedBox(height: 5),

                      // question
                      Row(
                        children: [
                          Image.asset('lib/views/InteractivePlayer/assets/question_1.png',width: 18,),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Container(
                              child: Text(controller.videoData['actions_in_player'][controller.detected_position_index]['question'].toString(), style: TextStyle(fontSize: Get.width / 28, fontWeight: FontWeight.w600))
                              ,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),

                      // options listing
                      Container(
                        // color: Colors.yellow,
                        width: Get.width/1.3,
                        child: ListView.builder(
                          itemCount: controller.videoData['actions_in_player'][controller.detected_position_index]['answers'].length,
                          shrinkWrap: true,
                          physics: ScrollPhysics(),
                          padding: EdgeInsets.all(0),
                          itemBuilder: (_, index) {
                            return GestureDetector(
                              onTap: () {
                                controller.selectedMultipleChoiceOption = index;
                                controller.update();
                              },
                              child: Container(
                                padding: EdgeInsets.all(5),
                                margin: EdgeInsets.only(bottom: 5),
                                child: Row(
                                  children: [
                                    Icon(
                                      controller.selectedMultipleChoiceOption.toString() == index.toString() ? Icons.check_box_rounded : Icons.check_box_outline_blank_rounded,
                                      size: Get.width / 20,
                                      color: Colors.purple,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      controller.videoData['actions_in_player'][controller.detected_position_index]['answers'][index].toString(),
                                      style: TextStyle(fontWeight: FontWeight.w400, fontSize: Get.width / 30),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),

                      const SizedBox(height: 10),

                    ],
                  ),
                ),
              ),
            ),


            if(controller.isInteractionIsSubmitted && controller.selectedMultipleChoiceOption == int.parse(controller.videoData['actions_in_player'][controller.detected_position_index]['answer_index'].toString()))
              Positioned(
                child: IgnorePointer(
                  child: Container(
                    width: Get.width,
                    child: Lottie.asset('lib/views/InteractivePlayer/assets/cograts.json',repeat: false),
                  ),
                ),
              ),


            if(controller.isInteractionIsSubmitted == false)
              Positioned(
                right: 13,
                bottom: 13,
                child: Container(
                  child: Column(
                    children: [

                      GestureDetector(
                        onTap: () async {
                          controller.isInteractionIsSubmitted = false;
                          controller.isShowingTheIntraction = false;
                          await controller.playerController.seekTo(controller.playerController.value.position + Duration(milliseconds: 1000));
                          await controller.playerController.play();
                          controller.update();
                        },
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(1000),
                              border: Border.all(color: Colors.black.withValues(alpha: 0.5))
                          ),
                          alignment: Alignment.center,
                          padding: EdgeInsets.all(2),
                          width: Get.width/12,
                          height: Get.width/12,
                          child: Text("Skip",style: TextStyle(fontSize: Get.width/40),),
                        ),
                      ),
                      SizedBox(height: 10,),
                      GestureDetector(
                        onTap: (){

                          // last work area

                          print("---------------------------"+controller.selectedMultipleChoiceOption.toString());
                          print("---------------------------"+controller.detected_position_index.toString());

                          // print(controller.videoData.toString());


                          if(controller.selectedMultipleChoiceOption == null)
                          {
                            toast_warning('Please select an option');
                          }
                          else{

                            print("------type-----"+controller.videoData['actions_in_player'][controller.detected_position_index]['type'].toString());
                            print("------and ind-----"+controller.selectedMultipleChoiceOption.toString());
                            print('---------|--------------------|----------');
                            // log(controller.videoData['actions_in_player'][controller.detected_position_index]['routing_data'][controller.selectedMultipleChoiceOption].toString());
                            // log("isInteractionIsSubmitted : ${controller.isInteractionIsSubmitted}");

                            if(controller.videoData['actions_in_player'][controller.detected_position_index]['type'].toString() == "video")
                              {


                                setState(() {
                                  controller.isShowingTheIntraction = false;
                                  controller.isInteractionIsSubmitted = false;
                                  controller.videoData = controller.videoData['actions_in_player'][controller.detected_position_index]['routing_data'][controller.selectedMultipleChoiceOption];
                                  controller.videoFiles = controller.videoData['video_files'];
                                  controller.changeVideo(controller.videoFiles[0]);
                                  // controller.playerController.play();
                                  controller.update();
                                });


                              }else{
                              setState(() {
                                controller.isInteractionIsSubmitted = true;
                                controller.update();
                              });
                            }



                          }

                        },
                        child: Container(
                          child: Image.asset('lib/views/InteractivePlayer/assets/check.png',width: Get.width/12,),
                        ),
                      ),

                    ],
                  ),
                ),
              ),


          ],
        ),
      );
    });
  }
}
