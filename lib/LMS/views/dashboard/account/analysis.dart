import 'package:cached_network_image/cached_network_image.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/LMS/controllers/analyticscontroller.dart';
import 'package:edutalim/components/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';

class AnalysisPage extends StatefulWidget {
  const AnalysisPage({super.key});

  @override
  State<AnalysisPage> createState() => _AnalysisPageState();
}

class _AnalysisPageState extends State<AnalysisPage> with SingleTickerProviderStateMixin {
  late final AnalysisController _controller;
  late final TabController _tabController;

  // Cache frequently accessed values
  late final double _screenWidth;
  late final double _screenHeight;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(AnalysisController());
    _tabController = TabController(length: 2, vsync: this);
    _screenWidth = Get.width;
    _screenHeight = Get.height;

    // Load data once
    _controller.getAnalysis();
  }

  @override
  void dispose() {
    _controller.selectedActiveSubjectIndex = 0;
    _controller.selectedActiveCourseIndex = 0;
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AnalysisController>(
      builder: (controller) => Scaffold(
        backgroundColor: textwhiteColor,
        appBar: _buildAppBar(),
        body: controller.isAnalysisLoading ? loader() : _buildBody(controller),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      automaticallyImplyLeading: true,
      backgroundColor: appbarwhiteColor,
      surfaceTintColor: appbarwhiteColor,
      centerTitle: false,
      title: Text("Analysis", style: TextStyle(fontSize: _screenWidth / 20, fontWeight: FontWeight.w800)),
    );
  }

  Widget _buildBody(AnalysisController controller) {
    return Column(
      children: [
        const SizedBox(height: 10),
        _buildTabBar(),
        SizedBox(height: _screenHeight / 28),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildCourseTab(controller.activeCourses, isActive: true),
              _buildCourseTab(controller.completedCourse, isActive: false),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      indicatorColor: secondaryColor,
      indicatorWeight: 3,
      labelColor: primaryColor,
      unselectedLabelColor: Colors.grey,
      labelStyle: TextStyle(fontSize: _screenWidth / 25, fontWeight: FontWeight.w800),
      tabs: const [
        Tab(text: "Active Course"),
        Tab(text: "Completed Course"),
      ],
    );
  }

  Widget _buildCourseTab(List courses, {required bool isActive}) {
    if (courses.isEmpty) {
      return _buildEmptyState("No Courses Found");
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          _buildCourseList(courses, isActive),
          const SizedBox(height: 30),
          _buildCourseAnalysis(courses, isActive),
          const SizedBox(height: 200),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Text(message, style: TextStyle(fontSize: _screenWidth / 28, color: Colors.grey[600])),
      ),
    );
  }

  Widget _buildCourseList(List courses, bool isActive) {
    return SizedBox(
      height: _screenWidth / 5.5,
      child: ListView.builder(
        itemCount: courses.length,
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        itemBuilder: (context, index) => _buildCourseItem(courses[index], index, isActive),
      ),
    );
  }

  Widget _buildCourseItem(Map course, int index, bool isActive) {
    final isSelected = isActive ? _controller.selectedActiveCourseIndex == index : _controller.selectedCompletedCourseIndex == index;

    return GestureDetector(
      onTap: () => _selectCourse(index, isActive),
      child: Container(
        margin: EdgeInsets.only(left: index == 0 ? 20 : 8, right: index == 3 ? 20 : 8),
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: isSelected ? secondarylightColor : Colors.grey.withOpacity(0.2), width: isSelected ? 2 : 1),
        ),
        width: _screenWidth / 3,
        child: _buildCourseImage(course),
      ),
    );
  }

  Widget _buildCourseImage(Map course) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: CachedNetworkImage(
            width: _screenWidth,
            fit: BoxFit.cover,
            imageUrl: course['thumbnail'] ?? "",
            errorWidget: (_, __, ___) => Image.asset("assets/images/placeholder_banner.webp", fit: BoxFit.cover),
          ),
        ),
        Positioned.fill(
          child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Colors.black.withOpacity(0.4),
            ),
            child: Text(course['course_title'] ?? "", style: TextStyle(color: Colors.white, fontSize: _screenWidth / 40, fontWeight: FontWeight.w600, fontFamily: font_bold), textAlign: TextAlign.center),
          ),
        ),
      ],
    );
  }

  Widget _buildCourseAnalysis(List courses, bool isActive) {
    final selectedIndex = isActive ? _controller.selectedActiveCourseIndex ?? 0 : _controller.selectedCompletedCourseIndex ?? 0;

    final selectedCourse = courses[selectedIndex];
    final progress = _parseProgress(selectedCourse['course_progress']);

    return Column(
      children: [
        _buildCircularProgress(progress),
        const SizedBox(height: 20),
        _buildSectionTitle("Overall Performance"),
        const SizedBox(height: 30),
        _buildOverallPerformanceCards(selectedCourse),
        if (_hasSubjects(selectedCourse)) ...[
          const SizedBox(height: 30),
          _buildSubjectAnalysis(selectedCourse, isActive),
        ],
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(fontFamily: font_semibold, fontSize: _screenWidth / 28, fontWeight: FontWeight.w600),
    );
  }

  Widget _buildOverallPerformanceCards(Map course) {
    final performance = course['overall_performance'];

    return Column(
      children: [
        _buildProgressCard(
          title: "Video Watched",
          total: _parseInt(performance['video']['total']),
          progress: _parseInt(performance['video']['watched']),
        ),
        const SizedBox(height: 15),
        _buildProgressCard(
          title: "Material Watched",
          total: _parseInt(performance['material']['total']),
          progress: _parseInt(performance['material']['watched']),
        ),
        const SizedBox(height: 15),
        _buildProgressCard(
          title: "Exam Attempted",
          total: _parseInt(performance['exam']['total']),
          progress: _parseInt(performance['exam']['attempted']),
        ),
      ],
    );
  }

  Widget _buildSubjectAnalysis(Map course, bool isActive) {
    final subjects = course['subjects'] as List;
    final selectedSubjectIndex = isActive ? _controller.selectedActiveSubjectIndex : _controller.selectedCompletedSubjectIndex;

    final selectedSubject = subjects[selectedSubjectIndex!];
    final subjectProgress = _parseProgress(selectedSubject['progress']);

    return Column(
      children: [
        const SizedBox(height: 20),
        _buildSectionTitle("Subject Wise Performance"),
        const SizedBox(height: 30),
        _buildCircularProgress(subjectProgress),
        const SizedBox(height: 20),
        _buildSubjectList(subjects, isActive),
        const SizedBox(height: 30),
        _buildSubjectPerformanceCards(selectedSubject),
      ],
    );
  }

  Widget _buildSubjectList(List subjects, bool isActive) {
    return SizedBox(
      height: _screenWidth / 5.5,
      child: ListView.builder(
        itemCount: subjects.length,
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        itemBuilder: (context, index) => _buildSubjectItem(subjects[index], index, isActive),
      ),
    );
  }

  Widget _buildSubjectItem(Map subject, int index, bool isActive) {
    final isSelected = isActive ? _controller.selectedActiveSubjectIndex == index : _controller.selectedCompletedSubjectIndex == index;

    return Container(
      padding: const EdgeInsets.all(10),
      alignment: Alignment.center,
      width: _screenWidth / 2.8,
      margin: EdgeInsets.only(
        left: index == 0 ? 20 : 8,
        right: index == 3 ? 20 : 8,
      ),
      decoration: BoxDecoration(
        color: isSelected ? secondaryColor : secondaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(10),
      ),
      child: GestureDetector(
        onTap: () => _selectSubject(index, isActive),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              subject['subject_name'] ?? "",
              style: TextStyle(fontFamily: font_semibold, fontSize: _screenWidth / 40, color: isSelected ? Colors.white : primaryColor),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
            if (isSelected)
              Container(
                margin: const EdgeInsets.only(top: 10),
                height: 2.5,
                width: _screenWidth / 5.7,
                decoration: BoxDecoration(
                  color: secondarylightColor,
                  borderRadius: BorderRadius.circular(1000),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectPerformanceCards(Map subject) {
    final performance = subject['overall_performance'];

    return Column(
      children: [
        _buildProgressCard(
          title: "Video Watched",
          total: _parseInt(performance['video']['total']),
          progress: _parseInt(performance['video']['watched']),
        ),
        const SizedBox(height: 15),
        _buildProgressCard(
          title: "Material Watched",
          total: _parseInt(performance['material']['total']),
          progress: _parseInt(performance['material']['watched']),
        ),
        const SizedBox(height: 15),
        _buildProgressCard(
          title: "Exam Attempted",
          total: _parseInt(performance['exam']['total']),
          progress: _parseInt(performance['exam']['watched']),
        ),
      ],
    );
  }

  Widget _buildProgressCard({
    required String title,
    required int total,
    required int progress,
  }) {
    return Container(
      width: _screenWidth,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: const Color(0xFFE8E8E8)),
        gradient: const LinearGradient(colors: [Color(0xFFFFF6DC), Colors.white], begin: Alignment.centerLeft, end: Alignment.centerRight, stops: [0.1, 0.5]),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title, style: TextStyle(fontSize: _screenWidth / 30, fontFamily: font_semibold)),
              Text("$progress / $total", style: TextStyle(fontSize: _screenWidth / 30, fontFamily: font_semibold)),
            ],
          ),
          const SizedBox(height: 15),
          LinearPercentIndicator(
            width: _screenWidth * 0.8,
            padding: EdgeInsets.zero,
            progressColor: primaryColor,
            lineHeight: 8,
            percent: _getProgress(progress, total),
            barRadius: const Radius.circular(1000),
            backgroundColor: const Color(0xFFEAEAEA),
          ),
        ],
      ),
    );
  }

  Widget _buildCircularProgress(int current) {
    return CircularStepProgressIndicator(
      totalSteps: 100,
      currentStep: current,
      stepSize: 8,
      selectedColor: secondarylightColor,
      unselectedColor: Colors.grey[300],
      roundedCap: (_, __) => true,
      padding: 0,
      width: _screenWidth / 5.5,
      height: _screenWidth / 5.5,
      child: Center(
        child: Text("$current%", style: TextStyle(fontFamily: font_semibold, fontSize: _screenWidth / 28), textAlign: TextAlign.center),
      ),
    );
  }

  // Helper methods
  void _selectCourse(int index, bool isActive) {
    if (isActive) {
      _controller.selectedActiveCourseIndex = index;
      _controller.selectedActiveSubjectIndex = 0;
    } else {
      _controller.selectedCompletedCourseIndex = index;
      _controller.selectedCompletedSubjectIndex = 0;
    }
    _controller.update();
  }

  void _selectSubject(int index, bool isActive) {
    if (isActive) {
      _controller.selectedActiveSubjectIndex = index;
    } else {
      _controller.selectedCompletedSubjectIndex = index;
    }
    _controller.update();
  }

  int _parseProgress(dynamic progress) {
    if (progress == null) return 0;
    return double.parse(progress.toString()).toInt();
  }

  int _parseInt(dynamic value) {
    if (value == null) return 0;
    return int.parse(value.toString());
  }

  bool _hasSubjects(Map course) {
    final subjects = course['subjects'];
    return subjects != null && subjects.toString() != "null" && subjects.isNotEmpty;
  }

  double _getProgress(int attempted, int total) {
    if (total == 0) return 0.0;
    return attempted / total;
  }
}
