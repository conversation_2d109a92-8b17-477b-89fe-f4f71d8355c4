// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_html/flutter_html.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:stockex/components/constants.dart';
// import 'package:stockex/components/utils.dart';
// import 'package:stockex/controller/combopackagecontroller.dart';
// import 'package:stockex/views/dashboard/home/<USER>/course_details.dart';
// import 'package:stockex/views/dashboard/payment/checkout.dart';
//
// class combo_package_details extends StatefulWidget {
//   var plan;
//
//   combo_package_details({super.key, required this.plan});
//
//   @override
//   State<combo_package_details> createState() => _combo_package_detailsState();
// }
//
// class _combo_package_detailsState extends State<combo_package_details> {
//   ComboPackageController controller = Get.put(ComboPackageController());
//
//   @override
//   void initState() {
//     controller.plan = widget.plan;
//
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     var mode = GetStorage().read('dark_mode');
//
//     return GetBuilder<ComboPackageController>(builder: (controller) {
//       return Scaffold(
//         backgroundColor: mode.toString() == "1" ? primaryColor : Colors.white,
//         body: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             AppBar(
//               toolbarHeight: 0,
//               backgroundColor: mode.toString() == "1" ? primaryColor : Colors.white,
//               surfaceTintColor: mode.toString() == "1" ? primaryColor : Colors.white,
//             ),
//             GestureDetector(
//               onTap: () {
//                 Get.back();
//               },
//               child: Container(
//                 width: Get.width / 12,
//                 height: Get.width / 12,
//                 margin: const EdgeInsets.all(20),
//                 alignment: Alignment.center,
//                 padding: const EdgeInsets.all(5),
//                 decoration: BoxDecoration(borderRadius: BorderRadius.circular(100), color: mode.toString() == "1" ? Colors.white.withValues(alpha: 0.10) : Colors.black.withValues(alpha: 0.05), border: Border.all(width: 01, color: mode.toString() == "1" ? Colors.white : Colors.black)),
//                 child: Icon(
//                   CupertinoIcons.left_chevron,
//                   size: Get.width / 20,
//                   color: mode.toString() == "1" ? Colors.white : Colors.black,
//                 ),
//               ),
//             ),
//             const SizedBox(height: 15),
//             Expanded(
//               child: Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 20.0),
//                 child: Container(
//                   decoration: BoxDecoration(color: mode.toString() == "1" ? Colors.white.withValues(alpha: 0.25) : Colors.white, borderRadius: BorderRadius.circular(15), boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.2), blurRadius: 5)]),
//                   child: Stack(
//                     children: [
//                       Column(
//                         children: [
//                           Expanded(
//                             child: Container(
//                               width: Get.width,
//                               child: SingleChildScrollView(
//                                 child: Column(
//                                   crossAxisAlignment: CrossAxisAlignment.start,
//                                   children: [
//                                     Container(
//                                       width: Get.width,
//                                       padding: const EdgeInsets.only(top: 30.0, left: 16, right: 16),
//                                       child: Text(
//                                         controller.plan['title'].toString(),
//                                         style: TextStyle(fontFamily: font_regular, fontSize: Get.width / 25, fontWeight: FontWeight.w600, color: mode.toString() == "1" ? Colors.white : Colors.black),
//                                         textAlign: TextAlign.start,
//                                       ),
//                                     ),
//                                     Padding(
//                                       padding: const EdgeInsets.symmetric(horizontal: 15.0),
//                                       child: Html(
//                                           style: {"p": Style(margin: Margins.zero, padding: HtmlPaddings.zero, fontFamily: font_light, fontSize: FontSize(12.5), color: mode.toString() == "1" ? Colors.white.withValues(alpha: 0.8) : Colors.black87)}, data: controller.plan['description'].toString()),
//                                     ),
//                                     const SizedBox(height: 5),
//                                     const Divider(height: 0.1, thickness: 0.3),
//                                     const SizedBox(height: 20),
//                                     Padding(
//                                       padding: const EdgeInsets.symmetric(horizontal: 20.0),
//                                       child: Text("Included Courses   : ", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 28, fontFamily: font_light, color: mode.toString() == "1" ? Colors.white : Colors.black)),
//                                     ),
//                                     const SizedBox(height: 15),
//                                     ListView.builder(
//                                         itemCount: controller.plan['courses'].length,
//                                         shrinkWrap: true,
//                                         padding: const EdgeInsets.all(0),
//                                         physics: const NeverScrollableScrollPhysics(),
//                                         itemBuilder: (_, index) {
//                                           var data = controller.plan['courses'][index];
//                                           return Container(
//                                             margin: const EdgeInsets.only(bottom: 5),
//                                             child: ListTile(
//                                               onTap: () {
//                                                 Get.to(() => CourseDetails(data: data, from: "plan"));
//                                               },
//                                               contentPadding: const EdgeInsets.symmetric(horizontal: 15),
//                                               leading: ClipRRect(
//                                                 borderRadius: BorderRadius.circular(4),
//                                                 child: CachedNetworkImage(
//                                                   imageUrl: data['thumbnail'].toString(),
//                                                   width: Get.width / 6,
//                                                   fit: BoxFit.cover,
//                                                   height: Get.width/10,
//                                                   errorWidget: (_,__,___){ return Image.asset("assets/images/placeholder_square.png",width: Get.width/10);},
//                                                 ),
//                                               ),
//                                               title: Text(data['title'].toString(), style: TextStyle(fontFamily: font_light, fontWeight: FontWeight.w600, color: mode.toString() == "1" ? Colors.white : Colors.black), maxLines: 1, overflow: TextOverflow.ellipsis),
//                                               subtitle: Text(removehtml_tags(data['description'].toString()), maxLines: 1, overflow: TextOverflow.ellipsis, style: TextStyle(fontSize: Get.width / 32, color: mode.toString() == "1" ? Colors.white : Colors.black)),
//                                               trailing: Icon(CupertinoIcons.right_chevron, color: mode.toString() == "1" ? Colors.white60 : Colors.black45),
//                                             ),
//                                           );
//                                         })
//                                   ],
//                                 ),
//                               ),
//                             ),
//                           ),
//                           Container(
//                             child: Column(
//                               children: [
//                                 const Divider(height: 0.1, thickness: 0.3),
//                                 Container(
//                                   padding: const EdgeInsets.all(16),
//                                   child: Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Container(
//                                         padding: const EdgeInsets.symmetric(horizontal: 5),
//                                         child: Column(
//                                           mainAxisAlignment: MainAxisAlignment.start,
//                                           crossAxisAlignment: CrossAxisAlignment.start,
//                                           children: [
//                                             Row(
//                                               mainAxisAlignment: MainAxisAlignment.end,
//                                               children: [
//                                                 Icon(Icons.currency_rupee, size: Get.width / 20, color: mode.toString() == "1" ? Colors.white : primaryColor),
//                                                 Text(controller.plan['payable_amount'].toString(), style: TextStyle(fontSize: Get.width / 20, fontWeight: FontWeight.w600, fontFamily: font_semibold, color: mode.toString() == "1" ? Colors.white : primaryColor)),
//                                                 const SizedBox(width: 15),
//                                                 Container(
//                                                   padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
//                                                   decoration: BoxDecoration(borderRadius: BorderRadius.circular(1000), color: secondaryColor),
//                                                   child: Row(
//                                                     children: [
//                                                       Icon(Icons.discount_outlined, size: Get.width / 35, color: Colors.white),
//                                                       const SizedBox(width: 5),
//                                                       Text("${controller.plan['discount_percentage'].toString()}%", style: TextStyle(fontFamily: font_medium, fontSize: Get.width / 35, color: Colors.white))
//                                                     ],
//                                                   ),
//                                                 )
//                                               ],
//                                             ),
//                                             const SizedBox(height: 2),
//                                             Row(
//                                               mainAxisAlignment: MainAxisAlignment.end,
//                                               children: [
//                                                 const SizedBox(width: 3),
//                                                 Icon(Icons.currency_rupee, size: Get.width / 30, color: mode.toString() == "1" ? Colors.white60 : Colors.black54),
//                                                 Text(controller.plan['actual_amount'].toString(),
//                                                     style: TextStyle(
//                                                         fontSize: Get.width / 30, fontWeight: FontWeight.w400, decoration: TextDecoration.lineThrough, decorationColor: mode.toString() == "1" ? Colors.white60 : Colors.black54, color: mode.toString() == "1" ? Colors.white60 : Colors.black54)),
//                                               ],
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                       GestureDetector(
//                                         onTap: () {
//                                           Get.to(() => checkout(plan: controller.plan));
//                                         },
//                                         child: Container(
//                                           margin: const EdgeInsets.only(right: 10),
//                                           padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
//                                           decoration: BoxDecoration(color: mode.toString() == "1" ? Colors.white60 : primaryColor, borderRadius: BorderRadius.circular(10)),
//                                           child: Text("   Buy Now   ", style: TextStyle(color: mode.toString() == "1" ? Colors.black : Colors.white, fontSize: Get.width / 32, fontFamily: font_regular, fontWeight: FontWeight.w600)),
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 )
//                               ],
//                             ),
//                           )
//                         ],
//                       ),
//                       Positioned(
//                         right: 0,
//                         top: 0,
//                         child: Container(
//                           padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
//                           decoration: BoxDecoration(color: primaryColor, borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(4), topRight: Radius.circular(15))),
//                           child: Row(
//                             children: [
//                               Icon(Icons.calendar_month_rounded, size: Get.width / 25, color: Colors.white),
//                               const SizedBox(width: 8),
//                               Text("${controller.plan['duration']} Days", style: TextStyle(fontSize: Get.width / 35, fontFamily: font_regular, fontWeight: FontWeight.w600, color: Colors.white)),
//                             ],
//                           ),
//                         ),
//                       )
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//             const SizedBox(height: 25)
//           ],
//         ),
//       );
//     });
//   }
// }
