// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:speekiai/components/constants.dart';
// import 'package:speekiai/components/utils.dart';
// import 'package:speekiai/controller/groupchatcontroller.dart';

// void showCommentModal({
//   required BuildContext context,
//   required List<dynamic> replyMessages,
//   required void Function()? onPressed,
//   TextEditingController? textController,
// }) {
//   showModalBottomSheet(
//     context: context,
//     isScrollControlled: true, // Allows the modal to take up more screen space
//     builder: (BuildContext context) {
//       return StatefulBuilder(
//         builder: (context, StateSetter setstate) {
//           return GetBuilder<GroupchatController>(
//             builder: (controller) {
//               return Padding(
//                 padding: EdgeInsets.only(
//                   bottom: MediaQuery.of(context)
//                       .viewInsets
//                       .bottom, // Adjusts for keyboard height
//                 ),
//                 child: SingleChildScrollView(
//                   child: Container(
//                     padding: const EdgeInsets.all(16.0),
//                     child: Column(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         const Divider(
//                           indent: 150,
//                           endIndent: 150,
//                           thickness: 5,
//                         ),
//                         const Text('Comments'),
//                         verticalSpace(10),
//                         const Divider(
//                           thickness: .5,
//                         ),
//                         const SizedBox(height: 10),
//                         SizedBox(
//                           height: 200,
//                           child: ListView.builder(
//                             itemCount: replyMessages.length,
//                             itemBuilder: (context, index) {
//                               return ListTile(
//                                 leading: CircleAvatar(
//                                   radius: 18,
//                                   backgroundImage: NetworkImage(
//                                       replyMessages[index]['sender_image']
//                                           .toString()),
//                                   onBackgroundImageError:
//                                       (exception, stackTrace) {
//                                     AssetImage(
//                                         "assets/img/user_placeholder.png");
//                                   },
//                                 ),
//                                 title: Text(
//                                   replyMessages[index]['message'],
//                                   style: TextStyle(fontSize: 12),
//                                 ),
//                               );
//                             },
//                           ),
//                         ),
//                         const SizedBox(height: 10),
//                         TextField(
//                           maxLines: null,
//                           keyboardType: TextInputType.text,
//                           controller: textController,
//                           style: TextStyle(
//                               fontSize: Get.width / 28, color: Colors.black),
//                           decoration: InputDecoration(
//                             focusedBorder: OutlineInputBorder(
//                               borderSide: const BorderSide(
//                                   color: Colors.transparent, width: 1),
//                               borderRadius: BorderRadius.circular(10.0),
//                             ),
//                             enabledBorder: OutlineInputBorder(
//                               borderSide: const BorderSide(
//                                   color: Colors.black, width: 1),
//                               borderRadius: BorderRadius.circular(10.0),
//                             ),
//                             filled: true,
//                             hintStyle: TextStyle(
//                               fontFamily: font_regular,
//                               color: Colors.black38,
//                               fontSize: Get.width / 28,
//                             ),
//                             suffixIcon: IconButton(
//                               onPressed: onPressed,
//                               icon: controller.commonLoader
//                                   ? loader()
//                                   : const Icon(Icons.send),
//                             ),
//                             hintText: "Type your message here",
//                             fillColor: Colors.transparent,
//                             contentPadding: const EdgeInsets.symmetric(
//                                 vertical: 15.0, horizontal: 20),
//                           ),
//                         )
//                       ],
//                     ),
//                   ),
//                 ),
//               );
//             },
//           );
//         },
//       );
//     },
//   );
// }
