import 'dart:convert';
import 'dart:developer';

import 'package:edutalim/LMS/views/tools/error_404.dart';
import 'package:edutalim/LMS/views/tools/error_500.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import 'package:http/http.dart' as http;

import '../../components/constants.dart';
import '../../components/utils.dart';
import '../../views/auth/login.dart';

class AuthApis {
  static Future<dynamic> register(name, phone, code) async {
    final url = Uri.parse("${api}auth/register");
    final devID = await get_device_info();
    final body = {'name': name, 'country_code': code, 'phone': phone, 'device_id': devID};

    try {
      final response = await http.post(url, body: body);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data;
      }
      if (response.statusCode == 400) {
        final data = jsonDecode(response.body);
        return data;
      } else if (response.statusCode == 500) {
        Get.offAll(() => error_500());
      } else if (response.statusCode == 404) {
        Get.offAll(() => error_404());
      } else if (response.statusCode == 401) {
        Get.offAll(() => Login());
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static Future<dynamic> resend_otp(String phone, String code) async {
    final url = Uri.parse("${api}auth/login/resend-otp");
    final devID = await get_device_info();
    final body = {'country_code': code, 'phone': phone, 'device_id': devID};

    try {
      final response = await http.post(url, body: body);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data;
      } else if (response.statusCode == 500) {
        Get.offAll(() => error_500());
      } else if (response.statusCode == 404) {
        Get.offAll(() => error_404());
      } else if (response.statusCode == 401) {
        Get.offAll(() => Login());
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static Future verify_otp(String phone, String code, otp) async {
    final url = Uri.parse("${api}auth/login/verify-otp");
    final body = {'country_code': code, 'phone': phone, 'otp': otp};
    log(url.toString());
    log(body.toString());

    try {
      final response = await http.post(
        url,
        body: body,
      );
      log('response----------------------------${body}');
      log('response----------------------------${response.statusCode}');

      log(
        "response -------------  ${response.body.toString()}",
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
      if (response.statusCode == 400) {
        return jsonDecode(response.body);
      } else if (response.statusCode == 500) {
        // Get.offAll(() => error_500());
      } else if (response.statusCode == 404) {
        // Get.offAll(() => error_404());
      } else if (response.statusCode == 401) {
        // Get.offAll(() => Login());
      } else {
        return 'failed';
      }
    } catch (e) {
      print("OTP error: $e");
      return 'failed';
    }
  }

  static Future<dynamic> login(String phone, String code) async {
    final url = Uri.parse("${api}auth/login");
    final devID = await get_device_info(); // If needed

    log('url-----------------------$url');
    final body = {
      'country_code': code,
      'phone': phone,
      'device_id': devID, // Add this if your API expects device ID
    };

    try {
      final response = await http.post(
        url,
        body: body,
      );

      log('response----------------------------${response.statusCode}');
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else if (response.statusCode == 400) {
        return jsonDecode(response.body);
      } else if (response.statusCode == 500) {
        Get.offAll(() => error_500());
        log('500----------------------------');
      } else if (response.statusCode == 404) {
        Get.offAll(() => error_404());
      } else if (response.statusCode == 401) {
        Get.offAll(() => Login());
      } else {
        return 'failed';
      }
    } catch (e) {
      print("Login error: $e");
      return 'failed';
    }
  }
}
