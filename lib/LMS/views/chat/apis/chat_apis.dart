import 'dart:convert';
import 'dart:io';
import 'package:edutalim/components/constants.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;

class ChatApihandler {

  static Future fet_transcript(chat_id) async {
    var request = http.MultipartRequest(
        "POST", Uri.parse("${api}speeki_chat/chat_transcript"));
    request.fields["auth_token"] = GetStorage().read('auth_token').toString();
    request.fields["chat_id"] = chat_id.toString();
    var response = await request.send();
    var responseData = await response.stream.toBytes();
    var responseString = utf8.decode(responseData); // Ensure correct decoding

    print(responseString);
    return jsonDecode(responseString);
  }

  static Future fet_chat_summary(chat_id) async {
    var request = http.MultipartRequest(
        "POST", Uri.parse("${api}speeki_chat/assessment_summary"));
    request.fields["auth_token"] = GetStorage().read('auth_token').toString();
    request.fields["chat_id"] = chat_id.toString();
    var response = await request.send();
    var responseData = await response.stream.toBytes();
    var responseString = String.fromCharCodes(responseData);
    print(responseString.toString());

    return jsonDecode(responseString);
  }

  static Future fet_send_chat(chat_id, language_code, type, audio, word) async {
    print('api_sec------------uuuuuuu------');
    print(audio.toString());

    print('api sec--------');
    print(word.toString());

    var request = http.MultipartRequest(
        "POST", Uri.parse("${api}speeki_chat/chat_with_ai"));
    request.fields["auth_token"] = GetStorage().read('auth_token').toString();
    request.fields["chat_id"] = chat_id.toString();
    request.fields["language_code"] = language_code.toString();
    request.fields["type"] = type.toString();
    request.fields["word"] = word.toString();
    // print(audio.path.toString());

    if (type.toString() == 'audio') {
      var pic = await http.MultipartFile.fromPath(
          "audio", audio.toString()); //file.path
      request.files.add(pic);
    }

    var response = await request.send();
    var responseData = await response.stream.toBytes();
    var responseString = String.fromCharCodes(responseData);
    print(responseString.toString());
    return jsonDecode(responseString);
  }

  static Future chats(language_code, chat_id) async {
    var request =
        http.MultipartRequest("POST", Uri.parse("${api}speeki_chat/index"));
    request.fields["auth_token"] = GetStorage().read('auth_token').toString();
    request.fields["language_code"] = language_code.toString();
    request.fields["chat_id"] = chat_id.toString();
    var response = await request.send();
    var responseData = await response.stream.toBytes();
    var responseString = String.fromCharCodes(responseData);
    print(responseString.toString());
    return jsonDecode(responseString);
  }

  static Future chatList() async {
    var url = Uri.parse(
        '${api}message/index?auth_token=${GetStorage().read('auth_token')}');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    print(response.body);
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else if (response.statusCode == 401) {
        // ApiBasehandler.authError();
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static Future messageList(chat_id) async {
    var url = Uri.parse(
        '${api}message/get_chat_list?chat_id=$chat_id&auth_token=${GetStorage().read('auth_token')}');
    print('Request URL: $url');

    try {
      http.Response response = await http.get(
        url,
        headers: {"content-type": "application/json"},
      );
      print('Message List response : ${response.body}');
      if (response.statusCode == 200) {
        // Decode the response using utf8 and jsonDecode
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);

        print('Decoded Data: $decodedData');

        // Check if the response contains the expected fields
        if (decodedData != null && decodedData['status'] == 1) {
          return decodedData;
        } else {
          print('Error: Unexpected response structure.');
          return 'failed';
        }
      } else if (response.statusCode == 401) {
        // ApiBasehandler.authError();
      } else {
        print('Error: Failed with status code ${response.statusCode}');
        return 'failed';
      }
    } catch (e) {
      // Catch and print any errors during the request or decoding
      print('Error: $e');
      return 'failed';
    }
  }

  static Future sendMessage({
    required String chatType,
    required String chatTypeId,
    required String messageType,
    required String message,
    required String isTaggedMessage,
    required String taggedMsgId,
    File? filePath,
  }) async {
    var request =
        http.MultipartRequest("POST", Uri.parse("${api}message/send_message"));
    request.fields["auth_token"] = GetStorage().read('auth_token').toString();
    request.fields["chat_type"] = chatType.toString();
    request.fields["chat_type_id"] = chatTypeId.toString();
    request.fields["message_type"] = messageType.toString();
    request.fields["message"] = message.toString();
    request.fields["is_tagged_message"] = isTaggedMessage.toString();
    request.fields["tagged_msg_id"] = taggedMsgId.toString();

    if (filePath != null) {
      print('a');
      var pic =
          await http.MultipartFile.fromPath("file", filePath.path.toString());
      print('b');
      //add multipart to request
      request.files.add(pic);
      print('c');
    }

    var response = await request.send();

    var responseData = await response.stream.toBytes();
    var responseString = String.fromCharCodes(responseData);
    print('Message response : ${responseString}');
    print(responseString.toString());
    return jsonDecode(responseString);
  }

  static Future deleteMessage(message_id) async {
    var url = Uri.parse(
        '${api}message/delete_message?message_id=$message_id&auth_token=${GetStorage().read('auth_token')}');
    print('Request URL: $url');

    try {
      http.Response response = await http.get(
        url,
        headers: {"content-type": "application/json"},
      );

      if (response.statusCode == 200) {
        // Decode the response using utf8 and jsonDecode
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);

        print('Decoded Data: $decodedData');

        // Check if the response contains the expected fields
        if (decodedData != null && decodedData['status'] == 1) {
          return decodedData;
        } else {
          return 'failed';
        }
      } else if (response.statusCode == 401) {
        // ApiBasehandler.authError();
      } else {
        return 'failed';
      }
    } catch (e) {
      // Catch and print any errors during the request or decoding
      print('Error: $e');
      return 'failed';
    }
  }

  static Future editMessage(message_id, message) async {
    var url = Uri.parse(
        '${api}message/edit_message?message_id=$message_id&message=$message&auth_token=${GetStorage().read('auth_token')}');
    print('Request URL: $url');

    try {
      http.Response response = await http.get(
        url,
        headers: {"content-type": "application/json"},
      );

      if (response.statusCode == 200) {
        // Decode the response using utf8 and jsonDecode
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);

        print('Decoded Data: $decodedData');

        // Check if the response contains the expected fields
        if (decodedData != null && decodedData['status'] == 1) {
          return decodedData;
        } else {
          return 'failed';
        }
      } else if (response.statusCode == 401) {
        // ApiBasehandler.authError();
      } else {
        return 'failed';
      }
    } catch (e) {
      // Catch and print any errors during the request or decoding
      print('Error: $e');
      return 'failed';
    }
  }

  static Future getComments(message_id) async {
    var url = Uri.parse(
        '${api}message/get_chat_details?message_id=$message_id&auth_token=${GetStorage().read('auth_token')}');
    print('Request URL: $url');

    try {
      http.Response response = await http.get(
        url,
        headers: {"content-type": "application/json"},
      );

      print('Comment: ${response.body}');

      if (response.statusCode == 200) {
        // Decode the response using utf8 and jsonDecode
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);

        // Check if the response contains the expected fields
        if (decodedData != null && decodedData['status'] == 1) {
          return decodedData;
        } else {
          print('Error: Unexpected response structure.');
          return 'failed';
        }
      } else if (response.statusCode == 401) {
        // ApiBasehandler.authError();
      } else {
        print('Error: Failed with status code ${response.statusCode}');
        return 'failed';
      }
    } catch (e) {
      // Catch and print any errors during the request or decoding
      print('Error: $e');
      return 'failed';
    }
  }

  static Future fetchChatMessages() async {
    var url = Uri.parse(
        '${api}message/get_chat_list_new?chat_id=0&from=0&to=10&count=10&auth_token=${GetStorage().read('auth_token')}');
    print('Request URL: $url');

    try {
      http.Response response = await http.get(
        url,
        headers: {"content-type": "application/json"},
      );
      print('Message List response2 : ${response.body}');
      if (response.statusCode == 200) {
        // Decode the response using utf8 and jsonDecode
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);

        print('Decoded Data: $decodedData');

        // Check if the response contains the expected fields
        if (decodedData != null && decodedData['status'] == 1) {
          return decodedData;
        } else {
          print('Error: Unexpected response structure.');
          return 'failed';
        }
      } else if (response.statusCode == 401) {
        // ApiBasehandler.authError();
      } else {
        print('Error: Failed with status code ${response.statusCode}');
        return 'failed';
      }
    } catch (e) {
      // Catch and print any errors during the request or decoding
      print('Error: $e');
      return 'failed';
    }
  }
}
