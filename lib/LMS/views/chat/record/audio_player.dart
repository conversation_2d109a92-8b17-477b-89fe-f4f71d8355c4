// import 'dart:async';
// import 'package:audioplayers/audioplayers.dart' as ap;
// import 'package:audioplayers/audioplayers.dart';
// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/controller/chat/speekichatcontroller.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// class AudioPlayer extends StatefulWidget {
//   /// Path from where to play recorded audio
//   final String source, sel_country, chat_id;
//
//   /// Callback when audio file should be removed
//   /// Setting this to null hides the delete button
//   final VoidCallback onDelete;
//
//   const AudioPlayer({
//     super.key,
//     required this.source,
//     required this.onDelete,
//     required this.sel_country,
//     required this.chat_id,
//   });
//
//   @override
//   AudioPlayerState createState() => AudioPlayerState();
// }
//
// class AudioPlayerState extends State<AudioPlayer> {
//   static const double _controlSize = 56;
//   static const double _deleteBtnSize = 24;
//
//   final _audioPlayer = ap.AudioPlayer()..setReleaseMode(ReleaseMode.stop);
//   late StreamSubscription<void> _playerStateChangedSubscription;
//   late StreamSubscription<Duration?> _durationChangedSubscription;
//   late StreamSubscription<Duration> _positionChangedSubscription;
//   Duration? _position;
//   Duration? _duration;
//
//   @override
//   void initState() {
//     _playerStateChangedSubscription =
//         _audioPlayer.onPlayerComplete.listen((state) async {
//       await stop();
//     });
//     _positionChangedSubscription = _audioPlayer.onPositionChanged.listen(
//       (position) => setState(() {
//         _position = position;
//       }),
//     );
//     _durationChangedSubscription = _audioPlayer.onDurationChanged.listen(
//       (duration) => setState(() {
//         _duration = duration;
//       }),
//     );
//
//     _audioPlayer.setSource(_source);
//
//     super.initState();
//   }
//
//   @override
//   void dispose() {
//     _playerStateChangedSubscription.cancel();
//     _positionChangedSubscription.cancel();
//     _durationChangedSubscription.cancel();
//     _audioPlayer.dispose();
//     super.dispose();
//   }
//
//   Speekichatcontroller controller = Get.put(Speekichatcontroller());
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<Speekichatcontroller>(builder: (controller) {
//       return LayoutBuilder(
//         builder: (context, constraints) {
//           return Container(
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Container(
//                   margin: EdgeInsets.symmetric(horizontal: 15),
//                   padding:
//                       EdgeInsets.only(left: 15, right: 15, top: 15, bottom: 15),
//                   decoration: BoxDecoration(
//                       color: textwhiteColor,
//                       borderRadius: BorderRadius.circular(20),
//                       boxShadow: [
//                         BoxShadow(
//                             color: textblackColor.withOpacity(0.2),
//                             blurRadius: 10,
//                             spreadRadius: 1),
//                       ]),
//                   child: Column(
//                     children: [
//                       Row(
//                         children: [
//                           _buildControl(),
//                           Column(
//                             children: [
//                               Row(
//                                 children: [
//                                   Container(
//                                     width: Get.width / 1.38,
//                                     child: _buildSlider(constraints.maxWidth),
//                                   ),
//                                 ],
//                               ),
//                               Container(
//                                 // color: Colors.yellow,
//                                 width: Get.width / 1.6,
//                                 child: Row(
//                                   mainAxisAlignment:
//                                       MainAxisAlignment.spaceBetween,
//                                   children: [
//                                     Container(
//                                       child: Text(
//                                         '${_duration.toString().split('.')[0] ?? 0.0}',
//                                         style:
//                                             TextStyle(fontSize: Get.width / 35),
//                                       ),
//                                     ),
//                                     Container(
//                                       child: Text(
//                                         '  ${_position.toString().split('.')[0] ?? 0.0}',
//                                         style:
//                                             TextStyle(fontSize: Get.width / 35),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ],
//                       ),
//                       SizedBox(
//                         height: 15,
//                       ),
//                       Container(
//                         child: Row(
//                           children: [
//                             GestureDetector(
//                               onTap: () {
//                                 setState(() {
//                                   controller.is_detection_success = true;
//                                 });
//                                 if (_audioPlayer.state ==
//                                     ap.PlayerState.playing) {
//                                   stop().then((value) => widget.onDelete());
//                                 } else {
//                                   widget.onDelete();
//                                 }
//                               },
//                               child: Container(
//                                 decoration: BoxDecoration(
//                                     color: textwhiteColor,
//                                     borderRadius: BorderRadius.circular(8),
//                                     border: Border.all(color: primaryColor)),
//                                 height: 45,
//                                 padding: EdgeInsets.all(10),
//                                 alignment: Alignment.center,
//                                 child: Text(
//                                   'Cancel',
//                                   style: TextStyle(
//                                       color: primaryColor,
//                                       fontFamily: font_medium),
//                                 ),
//                               ),
//                             ),
//                             SizedBox(
//                               width: 6,
//                             ),
//                             Expanded(
//                               child: GestureDetector(
//                                 onTap: () {
//                                   print('check-------');
//                                   print(controller.is_detection_success
//                                       .toString());
//
//                                   if (controller.is_detection_success) {
//                                     controller.call_send_chat(
//                                         widget.chat_id,
//                                         widget.sel_country,
//                                         'audio',
//                                         widget.source,
//                                         '');
//                                   } else {
//                                     setState(() {
//                                       controller.is_detection_success = true;
//                                       controller.showPlayer = false;
//                                     });
//
//                                     if (_audioPlayer.state ==
//                                         ap.PlayerState.playing) {
//                                       stop().then((value) => widget.onDelete());
//                                     } else {
//                                       widget.onDelete();
//                                     }
//
//                                     // here work
//
//                                     // _start();
//                                   }
//                                   // (language_code,word,word_id,type,audio)
//                                 },
//                                 child: Container(
//                                   decoration: BoxDecoration(
//                                     color: controller.is_detection_success
//                                         ? primaryColor
//                                         : Colors.orange,
//                                     borderRadius: BorderRadius.circular(8),
//                                     border: Border.all(
//                                       color: controller.is_detection_success
//                                           ? primaryColor
//                                           : Colors.orange,
//                                     ),
//                                   ),
//                                   padding: EdgeInsets.all(10),
//                                   alignment: Alignment.center,
//                                   height: 45,
//                                   child: controller.ischataudiosent
//                                       ? Container(
//                                           // height: 20,
//                                           // width: Get.width/15,
//                                           child: Image.asset(
//                                               'assets/lottie/audio_processing.gif'),
//                                         )
//                                       : Text(
//                                           controller.is_detection_success
//                                               ? 'Check'
//                                               : 'Retry',
//                                           style: TextStyle(
//                                               color: textwhiteColor,
//                                               fontFamily: font_medium),
//                                         ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 SizedBox(
//                   height: Get.height / 30,
//                 ),
//               ],
//             ),
//           );
//         },
//       );
//     });
//   }
//
//   Widget _buildControl() {
//     Icon icon;
//     Color color;
//
//     if (_audioPlayer.state == ap.PlayerState.playing) {
//       icon = Icon(
//         Icons.pause,
//         color: Colors.green,
//       );
//       color = Colors.green.withOpacity(0.1);
//     } else {
//       final theme = Theme.of(context);
//       icon = Icon(
//         Icons.play_arrow,
//         color: theme.primaryColor,
//       );
//       color = theme.primaryColor.withOpacity(0.1);
//     }
//
//     return ClipOval(
//       child: Material(
//         color: color,
//         child: InkWell(
//           child: Container(padding: EdgeInsets.all(8), child: icon),
//           onTap: () {
//             if (_audioPlayer.state == ap.PlayerState.playing) {
//               pause();
//             } else {
//               play();
//             }
//           },
//         ),
//       ),
//     );
//   }
//
//   Widget _buildSlider(double widgetWidth) {
//     bool canSetValue = false;
//     final duration = _duration;
//     final position = _position;
//
//     if (duration != null && position != null) {
//       canSetValue = position.inMilliseconds > 0;
//       canSetValue &= position.inMilliseconds < duration.inMilliseconds;
//     }
//
//     double width = widgetWidth - _controlSize - _deleteBtnSize;
//     width -= _deleteBtnSize;
//
//     return Slider(
//       activeColor: primaryColor,
//       inactiveColor: secondaryColor,
//       onChanged: (v) {
//         print(v.toString() + '----------');
//
//         if (duration != null) {
//           final position = v * duration.inMilliseconds;
//           _audioPlayer.seek(Duration(milliseconds: position.round()));
//         }
//       },
//       value: canSetValue && duration != null && position != null
//           ? position.inMilliseconds / duration.inMilliseconds
//           : 0.0,
//     );
//   }
//
//   Future<void> play() => _audioPlayer.play(_source);
//
//   Future<void> pause() async {
//     await _audioPlayer.pause();
//     setState(() {});
//   }
//
//   Future<void> stop() async {
//     await _audioPlayer.stop();
//     setState(() {});
//   }
//
//   Source get _source =>
//       kIsWeb ? ap.UrlSource(widget.source) : ap.DeviceFileSource(widget.source);
// }
