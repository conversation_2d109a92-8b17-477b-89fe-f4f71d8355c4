// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter/widgets.dart';
// import 'package:flutter_html/flutter_html.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:stockex/components/constants.dart';
// import 'package:stockex/components/utils.dart';
// import 'package:stockex/controller/planscontroller.dart';
// import 'package:stockex/views/dashboard/payment/checkout.dart';
//
// class plans extends StatefulWidget {
//   String course_id;
//   Map courseData;
//
//   plans({required this.course_id, required this.courseData});
//
//   @override
//   State<plans> createState() => _plansState();
// }
//
// class _plansState extends State<plans> {
//   // String? sel_plan;
//   String sel_plan = '';
//   List selectedPlan = [];
//
//   Plancontroller controller = Get.put(Plancontroller());
//
//   @override
//   void initState() {
//     controller.get_plans(widget.course_id);
//     selectedPlan = controller.plans;
//     print('selectedPlan-------------------$selectedPlan');
//     // controller.get_plans('1');
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     var mode = GetStorage().read('dark_mode');
//
//     return GetBuilder<Plancontroller>(builder: (controller) {
//       return Scaffold(
//         backgroundColor: mode.toString() == "1" ? primaryColor : Colors.white,
//         appBar: AppBar(
//           // systemOverlayStyle: SystemUiOverlayStyle(statusBarColor: primaryColor),
//           backgroundColor: mode.toString() == "1" ? primaryColor : Color(0xff00ffffff),
//           surfaceTintColor: mode.toString() == "1" ? primaryColor : Color(0xff00ffffff),
//           leading: IconButton(
//             onPressed: () {
//               Navigator.pop(context);
//             },
//             icon: Icon(Icons.close, color: mode.toString() == "1" ? Colors.white : Colors.black),
//           ),
//           title: Text('Plans', style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 20, color: mode.toString() == "1" ? Colors.white : textblackColor)),
//           centerTitle: true,
//         ),
//         body: Container(
//             width: Get.width,
//             child: Column(
//               children: [
//                 Expanded(
//                   child: controller.plansLoading
//                       ? loader()
//                       : SingleChildScrollView(
//                           child: Column(
//                             children: [
//                               const SizedBox(height: 20),
//                               controller.plans.isEmpty
//                                   ? SizedBox(
//                                       height: 200,
//                                       child: Center(child: Text("No Plans Found", style: TextStyle(fontFamily: 'poppins_regular', color: mode.toString() == "1" ? Colors.white : Colors.black, fontSize: 16))),
//                                     )
//                                   : Container(
//                                       padding: const EdgeInsets.symmetric(horizontal: 20),
//                                       child: ListView.builder(
//                                         physics: const ScrollPhysics(),
//                                         padding: const EdgeInsets.all(0),
//                                         shrinkWrap: true,
//                                         // itemCount: 3,
//                                         itemCount: controller.plans.length,
//                                         itemBuilder: (BuildContext context, int index) {
//                                           Map data = controller.plans[index];
//                                           return GestureDetector(
//                                             onTap: () async {
//                                               setState(() {
//                                                 sel_plan = index.toString();
//                                                 selectedPlan = [data];
//                                               });
//                                             },
//                                             child: Stack(
//                                               children: [
//                                                 Column(
//                                                   children: [
//                                                     Container(
//                                                       decoration: BoxDecoration(
//                                                           borderRadius: BorderRadius.circular(8),
//                                                           color: mode.toString() == "1" ? Colors.white.withValues(alpha: 0.3) : Colors.white,
//                                                           border: Border.all(
//                                                               width: sel_plan.toString() == index.toString() ? 1 : 0,
//                                                               color: sel_plan.toString() == index.toString()
//                                                                   ? mode.toString() == "1"
//                                                                       ? Colors.white
//                                                                       : primaryColor
//                                                                   : Colors.transparent),
//                                                           boxShadow: [BoxShadow(color: primaryColor.withValues(alpha: 0.2), blurRadius: 5, offset: Offset(0, 2))]),
//                                                       margin: const EdgeInsets.only(bottom: 10),
//                                                       padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
//                                                       child: Column(
//                                                         children: [
//                                                           const SizedBox(height: 25),
//                                                           Container(
//                                                             padding: const EdgeInsets.symmetric(horizontal: 5),
//                                                             child: Row(
//                                                               children: [
//                                                                 Icon(
//                                                                   sel_plan == index.toString() ? Icons.check_circle_rounded : Icons.circle_outlined,
//                                                                   size: Get.width / 15,
//                                                                   color: mode.toString() == "1" ? Colors.white : Colors.black,
//                                                                 ),
//                                                                 const SizedBox(width: 13),
//                                                                 Expanded(
//                                                                   child: Text(data['title'].toString(), style: TextStyle(fontSize: Get.width / 22, fontWeight: FontWeight.bold, color: mode.toString() == "1" ? Colors.white : Colors.black)),
//                                                                 ),
//                                                                 const SizedBox(width: 13),
//                                                               ],
//                                                             ),
//                                                           ),
//                                                           const SizedBox(height: 10),
//                                                           SizedBox(
//                                                             width: Get.width * .8,
//                                                             child: Html(
//                                                               data: data['description'].toString(),
//                                                               style: {
//                                                                 "p": Style(
//                                                                   color: mode.toString() == "1" ? Colors.white : Colors.black,
//                                                                   fontSize: FontSize(12.7),
//                                                                 )
//                                                               },
//                                                             ),
//                                                           ),
//                                                           if (data['is_combo'].toString() == "1")
//                                                             Column(
//                                                               children: [
//                                                                 Container(
//                                                                   margin: const EdgeInsets.symmetric(horizontal: 5),
//                                                                   padding: const EdgeInsets.all(10),
//                                                                   decoration: BoxDecoration(color: secondaryColor.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(10)),
//                                                                   child: Column(
//                                                                     crossAxisAlignment: CrossAxisAlignment.start,
//                                                                     children: [
//                                                                       Padding(
//                                                                         padding: const EdgeInsets.symmetric(horizontal: 10.0),
//                                                                         child: Text("Included Courses", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 30)),
//                                                                       ),
//                                                                       const SizedBox(height: 10),
//                                                                       ListView.builder(
//                                                                         shrinkWrap: true,
//                                                                         physics: const NeverScrollableScrollPhysics(),
//                                                                         itemCount: data['courses'].length,
//                                                                         itemBuilder: (_, index) {
//                                                                           Map item = data['courses'][index];
//                                                                           return Container(
//                                                                             margin: const EdgeInsets.only(bottom: 5),
//                                                                             padding: const EdgeInsets.symmetric(horizontal: 15),
//                                                                             child: Row(
//                                                                               children: [
//                                                                                 Icon(CupertinoIcons.check_mark_circled, color: secondaryColor.withValues(alpha: 0.75), size: Get.width / 18),
//                                                                                 const SizedBox(width: 10),
//                                                                                 Expanded(child: Text(item['title'].toString(), style: TextStyle(fontSize: Get.width / 26, color: Colors.black, fontWeight: FontWeight.w600, fontFamily: font_light)))
//                                                                               ],
//                                                                             ),
//                                                                           );
//                                                                         },
//                                                                       ),
//                                                                     ],
//                                                                   ),
//                                                                 ),
//                                                                 const SizedBox(height: 10)
//                                                               ],
//                                                             ),
//                                                           Container(
//                                                             padding: const EdgeInsets.symmetric(horizontal: 5),
//                                                             child: Column(
//                                                               children: [
//                                                                 Row(
//                                                                   mainAxisAlignment: MainAxisAlignment.end,
//                                                                   children: [
//                                                                     Icon(Icons.currency_rupee, size: Get.width / 20, color: mode.toString() == "1" ? Colors.white : primaryColor),
//                                                                     Text(data['payable_amount'].toString(), style: TextStyle(fontSize: Get.width / 20, fontWeight: FontWeight.w600, fontFamily: font_semibold, color: mode.toString() == "1" ? Colors.white : primaryColor)),
//                                                                     const SizedBox(width: 15),
//                                                                     Container(
//                                                                       padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 3),
//                                                                       decoration: BoxDecoration(borderRadius: BorderRadius.circular(1000), color: secondaryColor),
//                                                                       child: Row(
//                                                                         children: [
//                                                                           Icon(Icons.discount_outlined, size: Get.width / 26, color: Colors.white),
//                                                                           const SizedBox(width: 5),
//                                                                           Text("${data['discount_percentage']}%", style: TextStyle(fontFamily: font_medium, fontSize: Get.width / 26, color: Colors.white))
//                                                                         ],
//                                                                       ),
//                                                                     )
//                                                                   ],
//                                                                 ),
//                                                                 const SizedBox(height: 5),
//                                                                 Row(
//                                                                   mainAxisAlignment: MainAxisAlignment.end,
//                                                                   children: [
//                                                                     Icon(Icons.currency_rupee, size: Get.width / 25, color: mode.toString() == "1" ? Colors.white60 : Colors.black54),
//                                                                     Text(data['actual_amount'].toString(),
//                                                                         style: TextStyle(
//                                                                             fontSize: Get.width / 25,
//                                                                             fontWeight: FontWeight.w500,
//                                                                             decoration: TextDecoration.lineThrough,
//                                                                             decorationColor: mode.toString() == "1" ? Colors.white60 : Colors.black54,
//                                                                             color: mode.toString() == "1" ? Colors.white60 : Colors.black54)),
//                                                                   ],
//                                                                 ),
//                                                               ],
//                                                             ),
//                                                           ),
//                                                           const SizedBox(height: 10),
//                                                         ],
//                                                       ),
//                                                     ),
//                                                   ],
//                                                 ),
//                                                 Positioned(
//                                                   right: 0,
//                                                   top: 0,
//                                                   child: Container(
//                                                     padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
//                                                     decoration: BoxDecoration(color: primaryColor, borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(8), topRight: Radius.circular(8))),
//                                                     child: Row(
//                                                       children: [
//                                                         Icon(Icons.calendar_month_rounded, size: Get.width / 25, color: Colors.white),
//                                                         const SizedBox(width: 8),
//                                                         Text("${data['duration']} Days", style: TextStyle(fontSize: Get.width / 32, fontFamily: font_regular, fontWeight: FontWeight.w600, color: Colors.white)),
//                                                       ],
//                                                     ),
//                                                   ),
//                                                 )
//                                               ],
//                                             ),
//                                           );
//                                         },
//                                       ),
//                                     ),
//                               const SizedBox(height: 200)
//                             ],
//                           ),
//                         ),
//                 ),
//                 GestureDetector(
//                   onTap: () {
//                     if (sel_plan.toString() == '') {
//                       toast_info('Select a Plan');
//                     } else {
//                       if (selectedPlan[0]['is_purchased'].toString() == '1') {
//                         toast_info('Already Purchased');
//                       } else {
//                         Get.to(() => checkout(
//                               plan: selectedPlan[0],
//                             ));
//
//                         // controller.get_create_order(
//                         //   selectedPlan[0]['phone'].toString(),
//                         //   selectedPlan[0]['email'].toString(),
//                         //   selectedPlan[0]['description'].toString(),
//                         //   selectedPlan[0]['payable_amount'].toString(),
//                         //   selectedPlan[0]['razorpay_api_key'].toString(),
//                         //   selectedPlan[0]['razorpay_api_secret_key'].toString(),
//                         //   selectedPlan[0]['id'].toString(),
//                         // );
//                       }
//                     }
//                   },
//                   child: Container(
//                     width: Get.width,
//                     margin: const EdgeInsets.all(20),
//                     decoration: BoxDecoration(color: mode.toString() == "1" ? Colors.white24 : primaryColor, borderRadius: BorderRadius.circular(10)),
//                     padding: const EdgeInsets.all(20),
//                     alignment: Alignment.center,
//                     child: Text("Continue", style: TextStyle(color: textwhiteColor, fontSize: Get.width / 23, fontWeight: FontWeight.w600)),
//                   ),
//                 ),
//               ],
//             )),
//       );
//     });
//   }
// }
