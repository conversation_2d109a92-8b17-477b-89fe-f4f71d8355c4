import 'package:edutalim/components/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';

class LeadsDetails extends StatefulWidget {
  const LeadsDetails({super.key});

  @override
  State<LeadsDetails> createState() => _LeadsDetailsState();
}

class _LeadsDetailsState extends State<LeadsDetails> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Lead Details',
          style: TextStyle(
              color: textblackColor, fontSize: 16, fontWeight: FontWeight.bold),
        ),
        leading: GestureDetector(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: textblackColor,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            GestureDetector(
            
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(10)),
                    color: Colors.white),
                // margin: EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                child: Column(
                  children: [
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 15, vertical: 13),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            width: Get.width / 8,
                            height: Get.width / 8,
                            decoration: BoxDecoration(
                              border: Border.all(color: primaryColor, width: 2),
                              borderRadius: BorderRadius.circular(50),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(50),
                              child: Image.asset(
                                'assets/images/avater_placeholder.jpg',
                                // controller.filt_dueleadsdata[index]['profile_picture'].toString(),
                                fit: BoxFit.cover,
                                errorBuilder: (a, b, c) {
                                  return Image.asset(
                                      'assets/images/avater_placeholder.jpg');
                                },
                              ),
                            ),
                          ),
                          SizedBox(width: 10),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Row(
                                children: [
                                  SizedBox(
                                    width: Get.width / 1.65,
                                    child: Text(
                                      'Test',
                                      // controller.filt_dueleadsdata[index]['name'] ?? '--',
                                      style: TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.w600,
                                          color: commontextColor,
                                          overflow: TextOverflow.ellipsis),
                                    ),
                                  ),
                                  Icon(
                                    Icons.keyboard_arrow_right,
                                  )
                                ],
                              ),
                              Container(
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: Get.width / 5,
                                      child: Text("Phone",
                                          style: TextStyle(
                                              fontSize: 11,
                                              fontWeight: FontWeight.w500,
                                              color: commontextColor)),
                                    ),
                                    SizedBox(
                                      width: Get.width / 2.5,
                                      child: Text(
                                        '7676767676',
                                        // controller.filt_dueleadsdata[index]['phone'].toString() == "null" ? ":  --" : ":  ${controller.filt_dueleadsdata[index]['phone']}",
                                        style: TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.w500,
                                            color: commontextColor),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Row(
                                children: [
                                  SizedBox(
                                    width: Get.width / 5,
                                    child: Text("Alternate number",
                                        style: TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.w500,
                                            color: commontextColor)),
                                  ),
                                  SizedBox(
                                    width: Get.width / 2.5,
                                    child: Text(
                                      '9999999999',
                                      // controller.filt_dueleadsdata[index]['secondary_phone'].toString() == "" ? ":  --" : ":  ${controller.filt_dueleadsdata[index]['whatsapp']}",
                                      style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w500,
                                          color: commontextColor),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  SizedBox(
                                    width: Get.width / 5,
                                    child: Text(
                                      "Email",
                                      style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w500,
                                          color: commontextColor),
                                    ),
                                  ),
                                  SizedBox(
                                    width: Get.width / 2.5,
                                    child: Text(
                                      '<EMAIL>',
                                      // controller.filt_dueleadsdata[index]['email'].toString() == "null" ? ":  --" : ":  ${controller.filt_dueleadsdata[index]['email']}",
                                      style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w500,
                                          color: commontextColor),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 3),
                              Row(
                                children: [
                                  SizedBox(
                                    width: Get.width / 5,
                                    child: Text("Lead Status",
                                        style: TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.w600,
                                            color: commontextColor)),
                                  ),
                                  SizedBox(
                                    width: Get.width / 2.5,
                                    child: Text(
                                      'Follow-Up',
                                      // controller.filt_dueleadsdata[index]['status_name'].toString() == "" ? ":  --" : ":  ${controller.filt_dueleadsdata[index]['status_name'].toString()}",
                                      style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w600,
                                          color: commontextColor),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                        margin: EdgeInsets.symmetric(horizontal: 20),
                        padding:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        width: Get.width,
                        decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(8)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // if (controller.filt_dueleadsdata[index]['course_name'].toString() != "")
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Course  : ".trim(),
                                    style: TextStyle(
                                        color: Colors.black54,
                                        fontSize: Get.width / 30)),
                                SizedBox(width: 5),
                                Expanded(
                                  child: Text(
                                    'Course',
                                    // controller.filt_dueleadsdata[index]['course_name'].toString().trim(),
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 14,
                                        fontFamily: 'poppins_regular'),
                                  ),
                                ),
                              ],
                            ),
                            // if (controller.filt_dueleadsdata[index]['category_name'].toString() != "")
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Category  : ".trim(),
                                    style: TextStyle(
                                        color: Colors.black54,
                                        fontSize: Get.width / 30)),
                                SizedBox(width: 5),
                                Expanded(
                                  child: Text(
                                    'Category'.trim(),
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 14,
                                        fontFamily: 'poppins_regular'),
                                  ),
                                ),
                              ],
                            ),
                            // if (controller.filt_dueleadsdata[index]['source_name'].toString() != "")
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Lead Source  : ".trim(),
                                    style: TextStyle(
                                        color: Colors.black54,
                                        fontSize: Get.width / 30)),
                                SizedBox(width: 5),
                                Expanded(
                                  child: Text(
                                    'test'.toString().trim(),
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 14,
                                        fontFamily: 'poppins_regular'),
                                  ),
                                ),
                              ],
                            ),
                            // if (controller.filt_dueleadsdata[index]['remarks'].toString() != "")
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Remark  : ".trim(),
                                    style: TextStyle(
                                        color: Colors.black54,
                                        fontStyle: FontStyle.italic,
                                        fontSize: Get.width / 30)),
                                SizedBox(width: 5),
                                Expanded(
                                  child: Text(
                                    'Need to ask '.toString().trim(),
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 14,
                                        fontFamily: 'poppins_regular'),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        )),
                    Divider(color: Colors.black.withValues(alpha: 0.1)),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('created on',
                                  style: TextStyle(
                                      color: Colors.black54,
                                      fontSize: Get.width / 38)),
                              Text('21-2-25'.toString(),
                                  style: TextStyle(
                                      color: Colors.black54,
                                      fontSize: Get.width / 33)),
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('updated on',
                                  style: TextStyle(
                                      color: Colors.green,
                                      fontSize: Get.width / 38)),
                              Text('21-12-35'.toString(),
                                  style: TextStyle(
                                      color: Colors.green,
                                      fontSize: Get.width / 33)),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Divider(color: Colors.black.withValues(alpha: 0.1)),
                    Container(
                        margin: EdgeInsets.symmetric(vertical: 12),
                        child: IntrinsicHeight(
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    var content = Uri.parse("Hi ${'TEst'}");
                                    // launchURL("https://wa.me/${controller.filt_dueleadsdata[index]['country_code'].toString() + controller.filt_leadsdata[index]['phone'].toString()}?text=${content.toString().trim()}");
                                  },
                                  child: Container(
                                    color: Colors.transparent,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Image.asset('assets/icons/whatsapp.png',
                                            width: Get.width / 17),
                                        SizedBox(width: 6),
                                        Text("Whatsapp",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: Colors.green,
                                                fontFamily: "poppins_regular")),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              VerticalDivider(color: Colors.black12),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {},
                                  child: Container(
                                    color: Colors.transparent,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.call,
                                            color: Colors.green, size: 20),
                                        SizedBox(width: 6),
                                        Text("Call",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: Colors.green,
                                                fontFamily: "poppins_regular")),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
