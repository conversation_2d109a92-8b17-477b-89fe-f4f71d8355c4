import 'package:edutalim/LMS/views/InteractivePlayer/widgets/PlayerControllers.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import 'package:video_player/video_player.dart';

import 'Controller/VideoPlayerController.dart';

class FullscreenPlayer extends StatefulWidget {
  @override
  State<FullscreenPlayer> createState() => _FullscreenPlayer();
  const FullscreenPlayer({Key? key}) : super(key: key);

}


class _FullscreenPlayer extends State<FullscreenPlayer>{

  VideoPlayerGetXController controller = Get.put(VideoPlayerGetXController());

  @override
  void initState() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
    ]);

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    super.initState();
  }

  @override
  void dispose() {

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,overlays: [SystemUiOverlay.top,SystemUiOverlay.bottom]);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitDown,
      DeviceOrientation.portraitUp,
    ]);

    super.dispose();
  }


  @override
  Widget build(BuildContext context) {



    return GetBuilder<VideoPlayerGetXController>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: Stack(
            children: [
              Center(
                child: GestureDetector(
                  onTap: (){
                    setState(() {
                      controller.startShowTimer();
                    });
                  },
                  child: AspectRatio(
                    aspectRatio: controller.playerController.value.aspectRatio,
                    child: VideoPlayer(controller.playerController),
                  ),
                ),
              ),

              if(controller.showControl)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                child: GestureDetector(
                    onTap: (){setState(() {controller.showControl = false;});},
                    child: PlayerControllers(isFullScreen: true)
                ),
              )
            ],
          ),
        );
      }
    );
  }
}

