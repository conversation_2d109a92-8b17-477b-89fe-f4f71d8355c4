import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../controllers/registercontroller.dart';
import 'login.dart';
import 'otp_old.dart';

class register extends StatefulWidget {

  @override
  State<register> createState() => _loginState();
}

class _loginState extends State<register> {

  Registercontroller controller = Get.put(Registercontroller());

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Registercontroller>(
        builder: (controller){
          return GestureDetector(
            onTap: (){
              FocusScope.of(context).unfocus();
            },
            child: Scaffold(
              backgroundColor: textwhiteColor,
              // appBar: AppBar(
              //   toolbarHeight: 0,
              //   backgroundColor: textwhiteColor,
              //   surfaceTintColor: textwhiteColor,
              // ),
              body: Container(
                width: Get.width,
                height: Get.height,
                // padding: EdgeInsets.symmetric(horizontal: 20),
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    // mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        height: Get.height/4,
                        width: Get.width,
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage("assets/bg/signup_bg.png"),
                                fit: BoxFit.cover
                            )
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(height: 10,),
                            GestureDetector(
                              onTap: () {
                                Get.back();
                              },
                                child: Icon(Icons.arrow_back_ios_rounded,color: textwhiteColor,size: Get.width/20,)),
                            SizedBox(height: 30,),
                            Text("Signup",style: TextStyle(color: textwhiteColor,fontWeight: FontWeight.bold,fontSize: Get.width/16),),
                            SizedBox(height: 10,),
                            Text("Create an account to access professional teaching \ncourses and start learning today.",style: TextStyle(color: textwhiteColor,fontSize: Get.width/32),),
                          ],
                        ),
                      ),
                  
                  
                  
                      // name
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        child: Column(
                          children: [
                            SizedBox(height: 30,),
                            Container(
                              alignment: Alignment.centerLeft,
                              child: Text("Name",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w800,fontSize: Get.width/27),),
                            ),
                            SizedBox(height: 10,),
                            Container(
                              width: Get.width,
                              // height: 100,
                              child: TextField(
                                // keyboardType: TextInputType.number,
                                style: TextStyle(
                                    fontSize: Get.width/25,
                                    height: 1.5,
                                    color: Colors.black
                                ),
                                controller: controller.nameclr,
                                textCapitalization: TextCapitalization.words,
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: textblackColor.withOpacity(0.2),width: 1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: textblackColor.withOpacity(0.2),width: 1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  filled: true,
                                  hintStyle: TextStyle(fontSize: Get.width/25,color: Colors.black26,fontFamily: "poppins_regular",fontWeight: FontWeight.w400),
                                  hintText: "Enter your name",
                                  fillColor: Colors.white,
                                  contentPadding: const EdgeInsets.symmetric(vertical: 15.0,horizontal: 20),
                                  // prefixIcon: Icon(Icons.account_circle),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  
                      // phone
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        child: Column(
                          children: [
                            SizedBox(height: 20,),
                            Container(
                              alignment: Alignment.centerLeft,
                              // child: Text("Login",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w700,fontSize: Get.width/15),),
                              child: Text("Phone No",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w800,fontSize: Get.width/27),),
                            ),
                            SizedBox(height: 13,),
                            Container(
                              height: 80,
                              decoration: BoxDecoration(
                                  color: Colors.transparent,
                                  borderRadius: BorderRadius.only(topLeft: Radius.circular(10),topRight: Radius.circular(10))
                              ),
                              // padding: EdgeInsets.only(left: MediaQuery.of(context).size.width/10,right: MediaQuery.of(context).size.width/10,top: 10),
                              // width: MediaQuery.of(context).size.width*.9,
                              child: IntlPhoneField(
                                dropdownIconPosition: IconPosition.trailing,
                                flagsButtonMargin: EdgeInsets.only(left: 10),
                                controller: controller.phoneclr,
                                style: TextStyle(fontSize: Get.width/25,color: Colors.black),
                                decoration: InputDecoration(
                                  // counterText: '',
                                  fillColor: Colors.white,
                                  filled: true,
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: textblackColor.withOpacity(0.4),width: 0.6),
                                    borderRadius: BorderRadius.all(Radius.circular(10)),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: textblackColor.withOpacity(0.4),width: 0.5),
                                    borderRadius: BorderRadius.all(Radius.circular(10)),
                                  ),
                                  border: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black54,width: 0.1),
                                    borderRadius: BorderRadius.all(Radius.circular(10)),
                                  ),
                  
                                  errorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.red,width: 0.5),
                                    borderRadius: BorderRadius.all(Radius.circular(10)),
                                  ),
                  
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.red,width: 0.5),
                                    borderRadius: BorderRadius.all(Radius.circular(10)),
                                  ),
                                  hintStyle : TextStyle(fontSize: Get.width/25,color: Colors.black38),
                                  hintText: "Mobile Number",
                                ),
                                initialCountryCode: 'IN',
                                onChanged: (phone){
                                  // print(phone.completeNumber);
                                },
                                onCountryChanged: (country_code) {
                                  // print('code--------------${country_code.code.toString()}');
                                  // print('dialCode--------------${country_code.dialCode.toString()}');
                                  // print('flag--------------${country_code.flag.toString()}');
                                  // print('regionCode--------------${country_code.regionCode.toString()}');
                                  setState(() {
                                    controller.countrycode_number = country_code.dialCode.toString();
                                  });
                                },
                                // onChanged:
                              ),
                            ),
                          ],
                        ),
                      ),
                  
                  
                  
                      SizedBox(height: 25,),
                      Container(margin: EdgeInsets.symmetric(horizontal: 20),
                        child: common_button_gradient_secondary(
                          onPressed: () {
                            // toast_info('Coming soon');
                            // Get.to(()=> OtpPage(phone: '', userId: '', country_code: '', from: '',));
                            controller.get_register();
                          },
                          // height: 50,
                          borderRadius: BorderRadius.circular(10),
                          width: Get.width*.9,
                          child: controller.isLoading ?
                          Container(
                            height: 25,
                            width: 25,
                            child: CircularProgressIndicator(color: textwhiteColor,),
                          )
                              :
                          Text("Continue",style: TextStyle(color: textwhiteColor,fontWeight: FontWeight.w800,fontSize: Get.width/22),),
                        ),
                      ),
                  
                  
                      SizedBox(height: 20,),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("Already a member?"),
                          SizedBox(width: 5,),
                          GestureDetector(
                            onTap: () {
                              Get.to(Login());
                            },
                              child: Text("Sign in",style: TextStyle(fontFamily: font_bold,color: primaryColor),))
                        ],
                      ),
                      // GestureDetector(
                      //   onTap: (){
                      //     launchURL('https://prepscale.com/privacy-policy-2/');
                      //   },
                      //   child: Container(
                      //       width: Get.width/1.1,
                      //       // color: Colors.yellow,
                      //       child: Wrap(
                      //         alignment: WrapAlignment.center,
                      //         children: [
                      //           Text("By continuing you are accepting our  ",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w400,fontSize: Get.width/30),),
                      //           // SizedBox(width: 3,),
                      //           Text("Privacy Policy ",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w700,fontSize: Get.width/30),),
                      //           // SizedBox(width: 3,),
                      //           Text("and ",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w400,fontSize: Get.width/30),),
                      //           // SizedBox(width: 3,),
                      //           Text("Terms & Conditions",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w700,fontSize: Get.width/30),),
                      //         ],
                      //       )
                      //   ),
                      // ),
                  
                  
                      SizedBox(height: Get.height/7,),
                  
                      // GestureDetector(
                      //   onTap: (){
                      //     Get.to(login());
                      //   },
                      //   child: Container(
                      //     decoration: BoxDecoration(
                      //         color: textwhiteColor,
                      //         borderRadius: BorderRadius.circular(10),
                      //         border: Border.all(color: primaryColor)
                      //     ),
                      //     width: Get.width,
                      //     alignment: Alignment.center,
                      //     padding: EdgeInsets.all(15),
                      //     child: Text("Already have an account",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w600,fontSize: Get.width/25),),
                      //   ),
                      // ),
                  
                  
                      SizedBox(height: Get.height/60,),
                  
                    ],
                  ),
                ),
              ),
            ),
          );
        }
    );
  }

}

