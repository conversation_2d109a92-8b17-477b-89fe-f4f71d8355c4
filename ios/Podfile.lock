PODS:
  - better_player_plus (1.0.0):
    - <PERSON><PERSON> (~> 6.0.0)
    - Flutter
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
  - Cache (6.0.0)
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_tts (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - HLSCachingReverseProxyServer (0.1.0):
    - GCDWebServer (~> 3.5)
    - PINCache (>= 3.0.1-beta.3)
  - otp_autofill (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PINCache (3.0.4):
    - PINCache/Arc-exception-safe (= 3.0.4)
    - PINCache/Core (= 3.0.4)
  - PINCache/Arc-exception-safe (3.0.4):
    - PINCache/Core
  - PINCache/Core (3.0.4):
    - PINOperation (~> 1.2.3)
  - PINOperation (1.2.3)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - share_plus (0.0.1):
    - Flutter
  - speech_to_text (0.0.1):
    - Flutter
    - FlutterMacOS
    - Try
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - Try (2.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - zoom_allinonesdk (0.0.1):
    - Flutter

DEPENDENCIES:
  - better_player_plus (from `.symlinks/plugins/better_player_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - otp_autofill (from `.symlinks/plugins/otp_autofill/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - speech_to_text (from `.symlinks/plugins/speech_to_text/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - zoom_allinonesdk (from `.symlinks/plugins/zoom_allinonesdk/ios`)

SPEC REPOS:
  trunk:
    - Cache
    - DKImagePickerController
    - DKPhotoGallery
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
    - PINOperation
    - SDWebImage
    - SwiftyGif
    - Try

EXTERNAL SOURCES:
  better_player_plus:
    :path: ".symlinks/plugins/better_player_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  otp_autofill:
    :path: ".symlinks/plugins/otp_autofill/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  speech_to_text:
    :path: ".symlinks/plugins/speech_to_text/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"
  zoom_allinonesdk:
    :path: ".symlinks/plugins/zoom_allinonesdk/ios"

SPEC CHECKSUMS:
  better_player_plus: 10794c0ed1b3b4ae058939e22a6172f850a2039b
  Cache: 4ca7e00363fca5455f26534e5607634c820ffc2d
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  flutter_tts: b88dbc8655d3dc961bc4a796e4e16a4cc1795833
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  HLSCachingReverseProxyServer: 59935e1e0244ad7f3375d75b5ef46e8eb26ab181
  otp_autofill: a50d5ea8173b9a6fd930a4f1252416793a4e1906
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PINCache: d9a87a0ff397acffe9e2f0db972ac14680441158
  PINOperation: fb563bcc9c32c26d6c78aaff967d405aa2ee74a7
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  speech_to_text: 9dc43a5df3cbc2813f8c7cc9bd0fbf94268ed7ac
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  Try: 5ef669ae832617b3cee58cb2c6f99fb767a4ff96
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  zoom_allinonesdk: dee3e3bebb7d5d6b972fdd20fa4a9d349fd85711

PODFILE CHECKSUM: 53a6aebc29ccee84c41f92f409fc20cd4ca011f1

COCOAPODS: 1.16.2
