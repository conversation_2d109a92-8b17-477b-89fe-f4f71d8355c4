import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../controllers/otpcontroller.dart';
import 'login.dart';

class otp extends StatefulWidget {
  String from,phone,code;
  otp({required this.from, required this.phone,required this.code,});

  @override
  State<otp> createState() => _loginState();
}

class _loginState extends State<otp> {

  Otpcontroller controller = Get.put(Otpcontroller());


  @override
  void initState() {
    controller.activeCounter();
    controller.initInteractor();
    controller.getOTP(widget.from.toString(),widget.phone.toString(),widget.code.toString());
    super.initState();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    controller.otptextfldcontroller.stopListen();
    controller.otpInteractor.stopListenForCode();
  }


  @override
  Widget build(BuildContext context) {
    return GetBuilder<Otpcontroller>(
        builder: (controller){
          return GestureDetector(
            onTap: (){
              FocusScope.of(context).unfocus();
            },
            child: Scaffold(
              backgroundColor: textwhiteColor,
              appBar: AppBar(
                toolbarHeight: 0,
                backgroundColor: textwhiteColor,
                surfaceTintColor: textwhiteColor,
              ),
              body: SingleChildScrollView(
                child: Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [

                      // Container(
                      //   padding: EdgeInsets.symmetric(vertical: Get.height/25),
                      //   child: Image.asset('assets/images/otp.png',width: Get.width/3.5,),
                      // ),

                      // SizedBox(height: 10,),
                      SizedBox(height: Get.height/8,),

                      Container(
                        width: Get.width,
                        padding: EdgeInsets.symmetric(horizontal: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              // alignment: Alignment.centerLeft,
                              child: Text("OTP Verification",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w700,fontSize: Get.width/17,fontFamily: font_semibold),),
                            ),
                            SizedBox(height: 10,),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  // alignment: Alignment.centerLeft,
                                  child: Text("We have sent the code to your \nmobile number ${widget.code} ${widget.phone}",style: TextStyle(color: textblackColor.withOpacity(0.4),fontWeight: FontWeight.w400,fontSize: Get.width/27),),
                                ),
                                // SizedBox(width: 10,),
                                // GestureDetector(
                                //     onTap: (){
                                //       // Get.back();
                                //       Get.offAll(()=> Login());
                                //     },
                                //     child: Text("Edit",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w600,fontSize: Get.width/27),)),
                              ],
                            ),
                          ],
                        ),
                      ),


                      SizedBox(height: Get.height/20,),

                      // Container(
                      //   // color: Colors.yellow,
                      //   padding: EdgeInsets.symmetric(horizontal: 20),
                      //   child: Row(
                      //     mainAxisSize: MainAxisSize.min,
                      //     children: [
                      //
                      //       otp_card(otp_numbers: controller.otp_numbers,field: 1),
                      //       otp_card(otp_numbers: controller.otp_numbers,field: 2),
                      //       otp_card(otp_numbers: controller.otp_numbers,field: 3),
                      //       otp_card(otp_numbers: controller.otp_numbers,field: 4),
                      //
                      //
                      //     ],
                      //   ),
                      // ),

                      Container(
                        width: Get.width,
                        margin: EdgeInsets.symmetric(horizontal: 20),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [



                            _textFieldOTP(first: true, last: false,controllerr: controller.otp1,node: controller.otp1OneFocus),
                            const SizedBox(width: 12,),
                            _textFieldOTP(first: false, last: false,controllerr: controller.otp2,node: controller.otp2OneFocus),
                            const SizedBox(width: 12,),
                            _textFieldOTP(first: false, last: false,controllerr: controller.otp3,node: controller.otp3OneFocus),
                            const SizedBox(width: 12,),
                            _textFieldOTP(first: false, last: true,controllerr: controller.otp4,node: controller.otp4OneFocus),

                          ],
                        ),
                      ),

                      SizedBox(height: 25),
                      // resend option ----------
                      Container(
                        alignment: Alignment.center,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [

                            StreamBuilder(
                              stream: controller.timerStream.stream,
                              builder: (BuildContext ctx,
                                  AsyncSnapshot snapshot) {
                                return InkWell(
                                  onTap: snapshot.data == 0 ? () {
                                    // your sending code method
                                    controller.timerStream.sink.add(30);
                                    controller.activeCounter();
                                    // controller.resend_otp(Get.context);
                                    controller.initInteractor();
                                    controller.getOTP(widget.from.toString(),widget.phone.toString(),widget.code.toString());
                                    controller.resend_otp(context,widget.phone.toString(),widget.code.toString());
                                  } : null,
                                  child:
                                  snapshot.data == 0 ?
                                  // resend_otp
                                  Container(
                                      color: Colors.transparent,
                                      child: Wrap(
                                        children: [
                                          Text("Don’t Receive Code ?",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w400,fontSize: Get.width/26),),
                                          SizedBox(width: 10,),
                                          Text("Resend",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w700,fontSize: Get.width/26),),
                                        ],
                                      )
                                  )
                                      :
                                  Container(
                                    child: Text("Resend Again (${snapshot.hasData ? snapshot.data.toString() : 30}s)",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w600,fontSize: MediaQuery.of(context).size.width/25,height: 2.5,),),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      // SizedBox(height: 25),
                      SizedBox(height: 30),


                      common_button_gradient_secondary(
                        onPressed: () {
                          // controller.get_verifyotp(widget.user_id.toString());
                          setState(() {
                            controller.otp = controller.otp1.text.toString() + controller.otp2.text.toString() + controller.otp3.text.toString() + controller.otp4.text.toString();
                            if(controller.otp.length == 4){
                              // controller.getOTP(widget.from.toString(),widget.phone.toString(),widget.code.toString());
                              controller.verifyOTP(widget.from.toString(),widget.phone.toString(),widget.code.toString(),controller.otp);
                            }
                          });
                        },
                        // bg: controller.otp_numbers.length == 4 ? primaryColor : primaryColor.withOpacity(0.4),
                        // bg: controller.otp.length == 4 ? primaryColor : primaryColor.withOpacity(0.4),
                        borderRadius: BorderRadius.circular(0),
                        width: Get.width,
                        child: controller.isLoading ?
                        Container(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(color: textwhiteColor,),
                        )
                            :
                        Text("Verify",style: TextStyle(color: textwhiteColor,fontWeight: FontWeight.w600,fontSize: Get.width/22),),
                      ),

                      // SizedBox(height: 25),
                      //
                      // GestureDetector(
                      //   onTap: (){
                      //     // Get.to(()=> register());
                      //   },
                      //   child: Container(
                      //       color: Colors.transparent,
                      //       child: Wrap(
                      //         children: [
                      //           Text("Don’t Receive Code ?",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w400,fontSize: Get.width/26),),
                      //           SizedBox(width: 10,),
                      //           Text("Resend",style: TextStyle(color: primaryColor,fontWeight: FontWeight.w700,fontSize: Get.width/26),),
                      //         ],
                      //       )
                      //   ),
                      // ),



                      SizedBox(height: 40,),

                      // Expanded(
                      //   child: Container(
                      //     child: GridView.builder(
                      //         physics: ScrollPhysics(),
                      //         padding: EdgeInsets.all(0),
                      //         shrinkWrap: true,
                      //         itemCount: 12,
                      //         gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      //           childAspectRatio: 2/1.05,
                      //           crossAxisCount: 3,
                      //           mainAxisSpacing: 0,
                      //           crossAxisSpacing: 0,
                      //         ),
                      //         itemBuilder: (context, index) {
                      //           return InkWell(
                      //             // borderRadius: BorderRadius.circular(1000),
                      //             onTap: (){
                      //               setState(() {
                      //                 // otp_numbers.clear();
                      //                 if(index == 11){
                      //                   print("delte------");
                      //                   if(controller.otp_numbers.length > 0)
                      //                   {
                      //                     controller.otp_numbers.removeLast();
                      //                   }
                      //                   print(controller.otp_numbers.toString());
                      //                 }
                      //                 // else if(index == 10)
                      //                 // {
                      //                 //   otp_numbers.add(0);
                      //                 //   print(otp_numbers.toString());
                      //                 //   print(otp_numbers.length.toString()+"----");
                      //                 // }
                      //                 else if(controller.otp_numbers.length <= 3)
                      //                 {
                      //                   if(index == 10)
                      //                   {
                      //                     controller.otp_numbers.add(0);
                      //                   }else{
                      //                     controller.otp_numbers.add(index+1);
                      //                   }
                      //
                      //                   print(controller.otp_numbers.toString());
                      //                   print(controller.otp_numbers.length.toString()+"----");
                      //                 }
                      //               });
                      //
                      //               if(controller.otp_numbers.length == 4)
                      //               {
                      //                 controller.get_verifyotp(widget.user_id.toString());
                      //               }
                      //
                      //             },
                      //             child: Container(
                      //               alignment: Alignment.center,
                      //               child:
                      //               index == 10 ?
                      //               Text('0',style: TextStyle(color: Colors.black,fontWeight: FontWeight.w600,fontSize: Get.width/16),)
                      //                   :
                      //               index == 9 ? Container()
                      //                   :
                      //               index == 11 ? Container(
                      //                 child: SvgPicture.asset('assets/icons/delete_one.svg',width: 25,),
                      //               )
                      //                   :
                      //               Text((index+1).toString(),style: TextStyle(color: Colors.black,fontWeight: FontWeight.w600,fontSize: Get.width/16),),
                      //             ),
                      //           );
                      //         }
                      //     ),
                      //   ),
                      // ),


                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }



  Widget _textFieldOTP({bool ? first, last, TextEditingController ? controllerr, FocusNode? node}) {
    return Container(
      // height: Get.height / 12,
      width: Get.width/6,
      height: Get.width/5,
      alignment: Alignment.center,
      // color: Colors.transparent,
      child: AspectRatio(
        aspectRatio: 0.95,
        child: TextField(
          focusNode: node ,
          controller: controllerr,
          autofocus: true,
          onChanged: (value) {

            print('this...........');
            print(value.toString());

            print(controller.otptextfldcontroller.text.toString()+"-----fld");


            String otp = controller.otp1.text.toString() + controller.otp2.text.toString() + controller.otp3.text.toString() + controller.otp4.text.toString();
            controller.otpLength = otp.length;
            print('otpLength--------------------------${controller.otpLength}');

            if (value.length == 1 && last == false) {
              FocusScope.of(context).nextFocus();
            }

            if (value.isEmpty && first == false) {
              FocusScope.of(context).previousFocus();
            }

            if (value.length == 1 && last == true) {
              controller.verifyOTP(widget.from.toString(),widget.phone.toString(),widget.code.toString(),otp);
            }

          },

          showCursor: false,
          readOnly: false,
          textAlign: TextAlign.center,
          style: TextStyle(color: textblackColor,fontWeight: FontWeight.w800,fontSize: Get.width/20),
          keyboardType: TextInputType.number,
          maxLength: 1,
          decoration: InputDecoration(
            // fillColor: textblackColor.withOpacity(.2),
            // focusColor: textblackColor.withOpacity(.2),
            counter: Offstage(),
            border: OutlineInputBorder(
              // borderSide: BorderSide(width: 0.5, color: Colors.black),
              borderSide: BorderSide(width: 0.9, color: controllerr != null?primaryColor: Colors.black26),
              borderRadius: BorderRadius.circular(10),
            ),
            enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(width: 0.9, color: Colors.black26),
                borderRadius: BorderRadius.circular(10)
            ),
            focusedBorder: OutlineInputBorder(
              // borderSide: BorderSide(width: 0.5, color: Colors.black),
              borderSide: BorderSide(width: 0.9, color: controllerr != null?primaryColor: Colors.black26),
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
      ),
    );
  }



}


class otp_card extends StatelessWidget {
  List otp_numbers;
  int field;
  otp_card({required this.otp_numbers,required this.field});

  @override
  Widget build(BuildContext context) {
    return
      Container(
        child: Column(
          children: [
            // Text(otp_numbers.toString()),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: field == otp_numbers.length+1 ? primaryColor.withOpacity(.7) : Color(0xffD9D9D9),width: 0.7),
                borderRadius: BorderRadius.circular(10),
                color: field == otp_numbers.length+1 ? primaryColor.withOpacity(.15) : Colors.white,
              ),
              // padding: EdgeInsets.symmetric(vertical: 22,horizontal: 27),
              margin: EdgeInsets.symmetric(horizontal: 5),
              width: Get.width/6,
              height: Get.width/6,
              alignment: Alignment.center,
              child: Text(
                field == 1 && otp_numbers.length > 0 ? otp_numbers[0].toString()
                    : field == 2 && otp_numbers.length > 1 ? otp_numbers[1].toString()
                    : field == 3 && otp_numbers.length > 2 ? otp_numbers[2].toString()
                    : field == 4 && otp_numbers.length > 3 ? otp_numbers[3].toString()
                    : '',
                style: TextStyle(color: textblackColor,fontWeight: FontWeight.w800,fontSize: Get.width/20),),
            )
          ],
        ),
      );
  }
}

