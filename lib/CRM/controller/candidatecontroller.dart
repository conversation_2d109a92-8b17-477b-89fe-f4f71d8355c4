import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../api/apis.dart';

class CandidateController extends GetxController {
  late String member_id;

  List branches = [];
  bool isBranchLoading = true;
  String? sel_branch;

  TextEditingController follow_from_date = TextEditingController();
  TextEditingController follow_to_date = TextEditingController();

  // String from_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
  // String from_date = "${DateFormat('dd-MM-yyyy').format(DateTime.now().subtract(const Duration(days: 7)))}";
  String from_date = "All";

  // String to_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";
  String to_date = "All";

  DateTime? fromDate;
  DateTime? toDate;

  get_branches() async {
    print("called------");
    var data = await CRMApiBaseHandler.fet_branches();
    // branches = data['data']['branches'];
    branches = [
      {
        "id": "1",
        "title": "HO-KINFRA",
        "is_main": "1",
        "course_id": null,
        "created_at": "2025-01-03 15:42:28",
        "updated_at": "2025-02-14 11:49:15",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "2",
        "title": "KASARGOD",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-01-03 15:49:18",
        "updated_at": "2025-02-06 14:56:50",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "3",
        "title": "PAYYANNUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-01-03 16:36:22",
        "updated_at": "2025-02-06 14:57:02",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "4",
        "title": "KANNUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-01-11 18:45:42",
        "updated_at": "2025-02-06 14:57:14",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "5",
        "title": "VATAKARA",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-01-31 09:48:06",
        "updated_at": "2025-02-06 14:57:26",
        "deleted_at": null,
        "created_by": "1",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "6",
        "title": "CALICUT",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:57:35",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "7",
        "title": "TIRUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:58:16",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "8",
        "title": "KOTTAKKAL",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:58:25",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "9",
        "title": "KONDOTTY",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:58:45",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "10",
        "title": "MANJERI",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:58:54",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "11",
        "title": "NILAMBUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:04",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "12",
        "title": "PERINTHALMANNA",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:14",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "13",
        "title": "OTTAPPALAM",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:25",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "14",
        "title": "THRISSUR",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:36",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "15",
        "title": "ALUVA",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:44",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "16",
        "title": "KOLLAM",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 14:59:51",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "17",
        "title": "HYDERABAD",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 15:00:02",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "18",
        "title": "GUWAHATTY",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 15:00:17",
        "updated_at": null,
        "deleted_at": null,
        "created_by": "26",
        "updated_by": null,
        "deleted_by": null
      },
      {
        "id": "19",
        "title": "Delhi",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-06 15:00:30",
        "updated_at": "2025-02-07 15:47:17",
        "deleted_at": null,
        "created_by": "26",
        "updated_by": "26",
        "deleted_by": null
      },
      {
        "id": "20",
        "title": "DUBAI",
        "is_main": "0",
        "course_id": null,
        "created_at": "2025-02-07 14:49:08",
        "updated_at": "2025-02-07 14:49:33",
        "deleted_at": null,
        "created_by": "26",
        "updated_by": "26",
        "deleted_by": null
      }
    ];
    isBranchLoading = false;
    update();
  }

  List leads = [], alldata = [];
  bool isLoading = false;
  String? sel_status, sel_country, sel_university, sel_source, sel_team;
  TextEditingController searchfld = TextEditingController();

  get_leads() async {
    isLoading = false;
    print("called------");
    // var data = await CRMApiBaseHandler.fet_cadidates_new(sel_branch ?? '', member_id.toString() == "null" ? "" : member_id.toString(), from_date == null || from_date == "All" ? '' : from_date.toString(),
    //     to_date == null || to_date == "All" ? '' : to_date.toString(), sel_status ?? '', sel_country ?? '', sel_university ?? '', sel_source ?? '', sel_team ?? '');
    //
    // filt_leadsdata = leads = data[0]['data']['students'];
    // alldata = [data[0]['data']];

    filt_leadsdata = leads = [
      {
        "id": "85",
        "name": "TEST LEAD ",
        "code": "91",
        "phone": "978464849",
        "email": "<EMAIL>",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "13",
        "team_id": null,
        "telecaller_id": "83",
        "role_id": "13",
        "lead_id": "792",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "83",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-02-24 14:44:32",
        "updated_at": null,
        "deleted_at": null
      },
      {
        "id": "86",
        "name": "ABHISHEK",
        "code": "91",
        "phone": "9645010967",
        "email": "",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "6",
        "team_id": null,
        "telecaller_id": "76",
        "role_id": "13",
        "lead_id": "681",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "76",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-02-24 15:01:22",
        "updated_at": null,
        "deleted_at": null
      },
      {
        "id": "88",
        "name": "Suhail",
        "code": "91",
        "phone": "8089790155",
        "email": "",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "8",
        "team_id": null,
        "telecaller_id": "56",
        "role_id": "13",
        "lead_id": "70",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "56",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-02-25 14:20:19",
        "updated_at": null,
        "deleted_at": null
      },
      {
        "id": "89",
        "name": "ARJUN K",
        "code": "91",
        "phone": "8301058967",
        "email": "",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "6",
        "team_id": null,
        "telecaller_id": "76",
        "role_id": "13",
        "lead_id": "969",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "76",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-02-26 11:49:40",
        "updated_at": null,
        "deleted_at": null
      },
      {
        "id": "91",
        "name": "Afsal Rahman T",
        "code": "91",
        "phone": "9037312852",
        "email": "",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "13",
        "team_id": null,
        "telecaller_id": "60",
        "role_id": "13",
        "lead_id": "1018",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "60",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-02-27 14:55:19",
        "updated_at": null,
        "deleted_at": null
      },
      {
        "id": "92",
        "name": "Thoufeek Rahman N M",
        "code": "91",
        "phone": "7558007656",
        "email": "",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "13",
        "team_id": null,
        "telecaller_id": "60",
        "role_id": "13",
        "lead_id": "1019",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "60",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-02-27 14:58:51",
        "updated_at": null,
        "deleted_at": null
      },
      {
        "id": "94",
        "name": "NAVEEN PRATHEEP",
        "code": "91",
        "phone": "8075778198",
        "email": "",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "6",
        "team_id": null,
        "telecaller_id": "76",
        "role_id": "13",
        "lead_id": "1084",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "76",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-03-01 10:57:19",
        "updated_at": null,
        "deleted_at": null
      },
      {
        "id": "95",
        "name": "Kamarudheen ",
        "code": "91",
        "phone": "9567399149",
        "email": "",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "8",
        "team_id": null,
        "telecaller_id": "56",
        "role_id": "13",
        "lead_id": "871",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "56",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-03-03 10:54:30",
        "updated_at": null,
        "deleted_at": null
      },
      {
        "id": "96",
        "name": "JAISAL MEHABOOB.K ",
        "code": "91",
        "phone": "7034056668",
        "email": "",
        "prev_password": null,
        "profile_picture": null,
        "branch_id": "12",
        "team_id": null,
        "telecaller_id": "59",
        "role_id": "13",
        "lead_id": "1189",
        "is_team_lead": null,
        "is_team_manager": null,
        "current_role": null,
        "otp": null,
        "created_by": "59",
        "updated_by": null,
        "deleted_by": null,
        "created_at": "2025-03-05 11:13:16",
        "updated_at": null,
        "deleted_at": null
      },
    ];
    alldata = [
      {
        "students": [
          {
            "id": "85",
            "name": "TEST LEAD ",
            "code": "91",
            "phone": "978464849",
            "email": "<EMAIL>",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "13",
            "team_id": null,
            "telecaller_id": "83",
            "role_id": "13",
            "lead_id": "792",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "83",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-02-24 14:44:32",
            "updated_at": null,
            "deleted_at": null
          },
          {
            "id": "86",
            "name": "ABHISHEK",
            "code": "91",
            "phone": "9645010967",
            "email": "",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "6",
            "team_id": null,
            "telecaller_id": "76",
            "role_id": "13",
            "lead_id": "681",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "76",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-02-24 15:01:22",
            "updated_at": null,
            "deleted_at": null
          },
          {
            "id": "88",
            "name": "Suhail",
            "code": "91",
            "phone": "8089790155",
            "email": "",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "8",
            "team_id": null,
            "telecaller_id": "56",
            "role_id": "13",
            "lead_id": "70",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "56",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-02-25 14:20:19",
            "updated_at": null,
            "deleted_at": null
          },
          {
            "id": "89",
            "name": "ARJUN K",
            "code": "91",
            "phone": "8301058967",
            "email": "",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "6",
            "team_id": null,
            "telecaller_id": "76",
            "role_id": "13",
            "lead_id": "969",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "76",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-02-26 11:49:40",
            "updated_at": null,
            "deleted_at": null
          },
          {
            "id": "91",
            "name": "Afsal Rahman T",
            "code": "91",
            "phone": "9037312852",
            "email": "",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "13",
            "team_id": null,
            "telecaller_id": "60",
            "role_id": "13",
            "lead_id": "1018",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "60",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-02-27 14:55:19",
            "updated_at": null,
            "deleted_at": null
          },
          {
            "id": "92",
            "name": "Thoufeek Rahman N M",
            "code": "91",
            "phone": "7558007656",
            "email": "",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "13",
            "team_id": null,
            "telecaller_id": "60",
            "role_id": "13",
            "lead_id": "1019",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "60",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-02-27 14:58:51",
            "updated_at": null,
            "deleted_at": null
          },
          {
            "id": "94",
            "name": "NAVEEN PRATHEEP",
            "code": "91",
            "phone": "8075778198",
            "email": "",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "6",
            "team_id": null,
            "telecaller_id": "76",
            "role_id": "13",
            "lead_id": "1084",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "76",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-03-01 10:57:19",
            "updated_at": null,
            "deleted_at": null
          },
          {
            "id": "95",
            "name": "Kamarudheen ",
            "code": "91",
            "phone": "9567399149",
            "email": "",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "8",
            "team_id": null,
            "telecaller_id": "56",
            "role_id": "13",
            "lead_id": "871",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "56",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-03-03 10:54:30",
            "updated_at": null,
            "deleted_at": null
          },
          {
            "id": "96",
            "name": "JAISAL MEHABOOB.K ",
            "code": "91",
            "phone": "7034056668",
            "email": "",
            "prev_password": null,
            "profile_picture": null,
            "branch_id": "12",
            "team_id": null,
            "telecaller_id": "59",
            "role_id": "13",
            "lead_id": "1189",
            "is_team_lead": null,
            "is_team_manager": null,
            "current_role": null,
            "otp": null,
            "created_by": "59",
            "updated_by": null,
            "deleted_by": null,
            "created_at": "2025-03-05 11:13:16",
            "updated_at": null,
            "deleted_at": null
          },
        ],
        "branches": [
          {
            "id": "1",
            "title": "HO-KINFRA"
          },
          {
            "id": "2",
            "title": "KASARGOD"
          },
          {
            "id": "3",
            "title": "PAYYANNUR"
          },
          {
            "id": "4",
            "title": "KANNUR"
          },
          {
            "id": "5",
            "title": "VATAKARA"
          },
          {
            "id": "6",
            "title": "CALICUT"
          },
          {
            "id": "7",
            "title": "TIRUR"
          },
          {
            "id": "8",
            "title": "KOTTAKKAL"
          },
          {
            "id": "9",
            "title": "KONDOTTY"
          },
          {
            "id": "10",
            "title": "MANJERI"
          },
          {
            "id": "11",
            "title": "NILAMBUR"
          },
          {
            "id": "12",
            "title": "PERINTHALMANNA"
          },
          {
            "id": "13",
            "title": "OTTAPPALAM"
          },
          {
            "id": "14",
            "title": "THRISSUR"
          },
          {
            "id": "15",
            "title": "ALUVA"
          },
          {
            "id": "16",
            "title": "KOLLAM"
          },
          {
            "id": "17",
            "title": "HYDERABAD"
          },
          {
            "id": "18",
            "title": "GUWAHATTY"
          },
          {
            "id": "19",
            "title": "Delhi"
          },
          {
            "id": "20",
            "title": "DUBAI"
          }
        ],
        "tell_caller": [
          {
            "id": "30",
            "name": "Rajaprasad"
          },
          {
            "id": "31",
            "name": "Shijas"
          },
          {
            "id": "32",
            "name": "Sudheer Cheruvady"
          },
          {
            "id": "33",
            "name": "Noufal"
          },
          {
            "id": "35",
            "name": "Yasir "
          },
          {
            "id": "36",
            "name": "Rilwan"
          },
          {
            "id": "37",
            "name": "Hamza"
          },
          {
            "id": "38",
            "name": "Sudheer "
          },
          {
            "id": "39",
            "name": "Md Saleem"
          },
          {
            "id": "40",
            "name": "Raju Seabastian"
          },
          {
            "id": "41",
            "name": "Sajid"
          },
          {
            "id": "42",
            "name": "Nasrudhin"
          },
          {
            "id": "43",
            "name": "Unnikrishnan Kinavoor"
          },
          {
            "id": "44",
            "name": "Harshana PK"
          },
          {
            "id": "45",
            "name": "Mishab"
          },
          {
            "id": "46",
            "name": "Abdul Azeez"
          },
          {
            "id": "47",
            "name": "Najeeb P"
          },
          {
            "id": "48",
            "name": "Jeeshma "
          },
          {
            "id": "49",
            "name": "Nimisha Raj"
          },
          {
            "id": "51",
            "name": "Bushra"
          },
          {
            "id": "52",
            "name": "Rishfan"
          },
          {
            "id": "53",
            "name": "Abdulla Kutty VP"
          },
          {
            "id": "54",
            "name": "Shafeera"
          },
          {
            "id": "55",
            "name": "Murali"
          },
          {
            "id": "56",
            "name": "Sanoop M"
          },
          {
            "id": "57",
            "name": "Lijo"
          },
          {
            "id": "58",
            "name": "Majid"
          },
          {
            "id": "59",
            "name": "Faisal"
          },
          {
            "id": "60",
            "name": "Jaseela KA"
          },
          {
            "id": "61",
            "name": "Abitha"
          },
          {
            "id": "62",
            "name": "Abdussamad"
          },
          {
            "id": "63",
            "name": "Ameer Trogon"
          },
          {
            "id": "64",
            "name": "Binzy "
          },
          {
            "id": "65",
            "name": "Sreeja AR"
          },
          {
            "id": "66",
            "name": "Velayudhan"
          },
          {
            "id": "67",
            "name": "Muthu V"
          },
          {
            "id": "68",
            "name": "Fayis"
          },
          {
            "id": "74",
            "name": "Ranjith AK"
          },
          {
            "id": "75",
            "name": "Ashir"
          },
          {
            "id": "76",
            "name": "Nisha"
          },
          {
            "id": "77",
            "name": "Ajumi"
          },
          {
            "id": "78",
            "name": "Neethu"
          },
          {
            "id": "79",
            "name": "Gajath JP"
          },
          {
            "id": "80",
            "name": "Ishan Amrutha Prasad"
          },
          {
            "id": "81",
            "name": "Nishad"
          },
          {
            "id": "82",
            "name": "Rajaprasad"
          },
          {
            "id": "87",
            "name": "trogon"
          },
          {
            "id": "90",
            "name": "Syam Prateesh"
          },
          {
            "id": "93",
            "name": "Ameen "
          },
          {
            "id": "101",
            "name": "Nimisha"
          },
          {
            "id": "104",
            "name": "Britco Dubai"
          },
          {
            "id": "116",
            "name": "Fathima Bushra NE"
          },
          {
            "id": "117",
            "name": "Deepika"
          },
          {
            "id": "118",
            "name": "Sebin Sebastian "
          },
          {
            "id": "119",
            "name": "Ranjitha"
          },
          {
            "id": "120",
            "name": "Shaheena"
          },
          {
            "id": "132",
            "name": "Irshad"
          },
          {
            "id": "133",
            "name": "Muhmmed Sinan"
          }
        ]
      }
    ];
    isLoading = false;
  }

  List filt_leadsdata = [];

  void filterleads(value) {
    filt_leadsdata = leads.where((members) => members['name'].toString().toLowerCase().contains(value.toLowerCase()) || members['phone'].toString().toLowerCase().contains(value.toLowerCase())).toList();
    update();
  }

}
