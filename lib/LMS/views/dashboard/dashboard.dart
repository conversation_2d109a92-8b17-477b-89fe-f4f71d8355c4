
import 'package:edutalim/LMS/views/ai_mentor/ai_chat_screen.dart';
import 'package:edutalim/LMS/views/chat/chatlist.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../../components/constants.dart';
import '../../../controllers/commoncontroller.dart';
import 'account/account.dart';
import 'feed/feeds.dart';
import 'home/home.dart';
import 'my_course/my_course.dart';

class dashboard extends StatefulWidget {
  const dashboard({super.key});
  @override
  State<dashboard> createState() => _dashboardState();
}

class _dashboardState extends State<dashboard> {

  int tmp = 0;
  int _selectedIndex = 0;

  base()async{
    setState(() {
      tmp = GetStorage().read('home_index').toString() == 'null' ? 0 : int.parse(GetStorage().read('home_index').toString());
    });
    setState(() {
      _selectedIndex = tmp;
    });
    }

  Future<void> _onItemTapped(int index) async {
    setState(() {
      _selectedIndex = index;
      GetStorage().write('home_index',_selectedIndex);
    });
  }

  empty(){}

  final tabs = [
    home(),
    my_course(),
    // feed(),
    ai_chat_screen(),
    feed(),
    account(),
  ];

  Commoncontroller controller = Get.put(Commoncontroller());

  @override
  void initState() {
    base();
    // controller.get_userdata();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    // SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(statusBarColor: primaryColor))
    // ;
    return WillPopScope(
      onWillPop: (){
        return empty();
      },
      child: GetBuilder<Commoncontroller>(
          builder: (controller){
          return Scaffold(
              backgroundColor: Colors.white,
              body:
              // controller.isloading?Container():
              Container(
                child:
                // controller.userdata[0]['role_id'].toString() =='3'? tabs2[_selectedIndex]:
                tabs[_selectedIndex],
              ),

              bottomNavigationBar:
              // controller.isloading?Container():
              Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.8),
                      spreadRadius: 5,
                      blurRadius: 5,
                      offset: Offset(0, 7), // changes position of shadow
                    ),
                  ],
                ),

                padding: EdgeInsets.all(0),

                child:
                ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(0)),
                  child: BottomNavigationBar(
                    backgroundColor: textwhiteColor,
                    items: <BottomNavigationBarItem>[

                      BottomNavigationBarItem(
                        icon: Container(
                            padding: EdgeInsets.only(bottom: 0),
                            width: 30,
                            child:  Image.asset('assets/icons/home.png',color: _selectedIndex.toString() == "0" ? primaryColor : Color(0xffB6BFCA))),
                        label: "Home",
                      ),

                      BottomNavigationBarItem(
                        icon: Container(
                            padding: EdgeInsets.only(bottom: 4,top: 4),
                            width:30,
                            child:  Image.asset('assets/icons/my_course.png',color: _selectedIndex.toString() == "1" ? primaryColor : Color(0xffB6BFCA), width: Get.width*.05, height: Get.width*.055)),
                        label: "My Course",
                      ),



                      BottomNavigationBarItem(
                        icon: Container(
                            padding: EdgeInsets.only(bottom: 4,top: 4),
                            width:30,
                            child:  Image.asset('assets/images/ai_bot.png', width: Get.width*.05, height: Get.width*.055)),
                        label: "AI Chat",
                      ),



                      BottomNavigationBarItem(
                        icon: Container(
                            padding: EdgeInsets.only(bottom: 0),
                            width:30,
                            child:  Image.asset('assets/icons/feed.png',color: _selectedIndex.toString() == "3" ? primaryColor  : Color(0xffB6BFCA))),
                        label: "Feed",
                      ),

                      BottomNavigationBarItem(
                        icon: Container(
                            padding: EdgeInsets.only(bottom: 0),
                            width:30,
                            child:  Image.asset('assets/icons/profile.png',color: _selectedIndex.toString() == "4" ? primaryColor : Color(0xffB6BFCA))),
                        label: "Profile",
                      ),

                    ],

                    type: BottomNavigationBarType.fixed,
                    currentIndex: _selectedIndex,
                    selectedItemColor: primaryColor,
                    unselectedItemColor: Color(0xffB6BFCA),
                    iconSize: 32,
                    onTap: _onItemTapped,
                    elevation: 0.1,
                    selectedFontSize: 12,
                    unselectedFontSize: 12,

                  ), // 5 tabs -- tab, // 4 tabs -- tab2
                ),
              )
          );
        }
      ),
    );
  }
}
