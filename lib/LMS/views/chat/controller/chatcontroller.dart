import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';


class ChatController extends GetxController{


  bool isChatListLoading = false;
  TextEditingController searchController = TextEditingController();


  List chatList = [];
  List chatFilterList =  [];

  void getChatList(){
    // var data =
  }

  void filterChat(text){
    chatFilterList = [];
    for (var chat in chatList){
      if(chat['chat_name'].toString().toLowerCase().contains(text.toString().toLowerCase())){
        chatFilterList.add(chat);
      }
    }
    update();
  }

  bool isChatPageLoading = false;
  ScrollController chatScrollController = ScrollController();
  TextEditingController msgTextController = TextEditingController();
  bool isText = false;
  bool showAttachmentSection = false;
  File? imageFile;
  List chat = [
    {
      'sender' : '1',
      'send' : 'you',
      'message' : 'How are you',
    },
    {
      'sender' : '2',
      'send' : 'me',
      'message' : 'Am fine',
    }
  ];

  void sendMessage(type,msg){

    if(type == "text"){
      var now = DateTime.now();
      var formattedTime = DateFormat('hh:mm a').format(now);
      var data = {
        "id" : 1,
        "message" : msg,
        "send" : "you",
        "time" : formattedTime.toString(),
        "type" : "text"
      };

      msgTextController.text = "";
      isText = false;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollToBottom();
      });
      chat.add(data);
    }

    if(type == "image"){
      var now = DateTime.now();
      var formattedTime = DateFormat('hh:mm a').format(now);
      var data = {
        "id" : 1,
        "message" : msg,
        "send" : "you",
        "time" : formattedTime.toString(),
        "type" : "image",
        "file" : imageFile
      };

      msgTextController.text = "";
      imageFile = null;
      isText = false;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollToBottom();
      });
      chat.add(data);
    }
    update();
  }

  void scrollToBottom() {
    chatScrollController.animateTo(
      chatScrollController.position.maxScrollExtent, // Scroll to the end
      duration: Duration(milliseconds: 300), // Scroll duration
      curve: Curves.easeOut, // Animation curve
    );
  }





}
