import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;

import '../../components/constants.dart';
import '../../components/utils.dart';
import '../../views/auth/login.dart';
import '../views/tools/error_404.dart';
import '../views/tools/error_500.dart';

class ApiBaseHandler {
  static Future<dynamic> assignmentDetails(assignment_id) async {
    return _handleGetRequest('student/my-course/getAssignmentDetails?assignment_id=$assignment_id', allowBadRequest: true);
  }

  static Future<dynamic> appVersion() async {
    return _handleGetRequest('app-info/check-version', requireAuth: false);
  }

  static Future<dynamic> profile() async {
    return _handleGetRequest('student/profile', allowBadRequest: true);
  }

  static Future<dynamic> feeds() async {
    return _handleGetRequest('feeds');
  }

  static Future<dynamic> studentDashboard() async {
    return _handleGetRequest('student/dashboard');
  }

  static Future<dynamic> courseDetails(String courseId) async {
    return _handleGetRequest('course/details/$courseId');
  }

  static Future<dynamic> universityDetails(String uniId) async {
    return _handleGetRequest('category/university-details/$uniId');
  }

  static Future<dynamic> categoryDetails(String catId) async {
    return _handleGetRequest('category/courses-by-category/$catId');
  }

  static Future<dynamic> myCourse() async {
    return _handleGetRequest('student/my-course');
  }

  static Future<dynamic> userCourses() async {
    return _handleGetRequest('user/course/index', allowBadRequest: true);
  }

  static Future<dynamic> lessonsSection(String sectionId) async {
    return _handleGetRequest('lessons/section/$sectionId');
  }

  static Future<dynamic> lessonFiles(String sectionId) async {
    return _handleGetRequest('lesson-files/lesson/$sectionId');
  }

  static Future<dynamic> live_class(String courseId) async {
    return _handleGetRequest('live-class/course_live_class/19', allowBadRequest: true);
  }

  static Future<dynamic> create_jwt(String meeting_number) async {
    return _handleGetRequest('live-class//create_jwt?meeting_number=$meeting_number', allowBadRequest: true);
  }

  static Future<dynamic> exams(
    String courseId,
  ) async {

    return _handleGetRequest('exams?course_id=$courseId', allowBadRequest: true);
  }

  static Future<dynamic> materials(String courseId, String section_id, String lesson_id) async {
    return _handleGetRequest('lesson-files/materials?course_id=$courseId&section_id=$section_id&lesson_id=$lesson_id', allowBadRequest: true);
  }

  static Future<dynamic> practices(String courseId) async {
    return _handleGetRequest('exams/practice?course_id=$courseId', allowBadRequest: true);
  }

  static Future<dynamic> notificationsList() async {
    return _handleGetRequest('notifications/notifications_list', allowBadRequest: true);
  }

  static Future<dynamic> assignmentsList(course_id) async {
    return _handleGetRequest('student/my-course/assignmentsList?course_id=$course_id&section_id=', allowBadRequest: true);
  }

  static Future<dynamic> update_lesson_file_progress(lesson_file_id) async {
    return _handleGetRequest('lesson-files/update_lesson_file_progress?lesson_file_id=$lesson_file_id', allowBadRequest: true);
  }

  // POST Methods
  static Future<dynamic> autoEnroll(String courseId) async {
    return _handlePostRequest('user/course/auto-enroll', {'course_id': courseId}, allowBadRequest: true);
  }

  static Future<dynamic> switchCourse(String courseId) async {
    return _handlePostRequest('user/course/switch-course', {'course_id': courseId}, allowBadRequest: true);
  }

  static Future<dynamic> analysis() async {
    return _handleGetRequest('student/analysis', allowBadRequest: true);
  }

  static Future<dynamic> uploadAssignemnt (
      {required File assignmentFile, required String assignment_id})  async {
    try {
      final request = http.MultipartRequest("POST", Uri.parse("${api}student/my-course/SubmitAssignment"));

      // Add fields
      request.fields.addAll({
        // "auth_token": _storage.read('auth_token')?.toString() ?? '',
        "assignment_id": assignment_id,
      });


      request.headers.addAll(_authHeaders);

      // Add file if provided
      final pic = await http.MultipartFile.fromPath("file", assignmentFile.path);
      request.files.add(pic);
    
      final response = await request.send();
      final responseData = await response.stream.toBytes();
      final responseString = String.fromCharCodes(responseData);

      log(response.statusCode.toString());

      // log(responseData.toString());

      return jsonDecode(responseString);

    } catch (e) {
      log(': $e');
      return 'failed';
    }
  }

  // Multipart request for profile editing
  static Future<dynamic> editProfile({
    required String name,
    required String code,
    required String phone,
    required String email,
    required String dob,
    required String address,
    required String gender,
    File? filePath,
  }) async {
    try {
      final request = http.MultipartRequest("POST", Uri.parse("${api}profile/update"));

      // Add fields
      request.fields.addAll({
        "auth_token": _storage.read('auth_token')?.toString() ?? '',
        "name": name,
        "code": code,
        "phone": phone,
        "email": email,
        "dob": dob,
        "address": address,
        "gender": gender,
      });

      // Add file if provided
      if (filePath != null) {
        final pic = await http.MultipartFile.fromPath("image", filePath.path);
        request.files.add(pic);
      }

      final response = await request.send();
      final responseData = await response.stream.toBytes();
      final responseString = String.fromCharCodes(responseData);

      return jsonDecode(responseString);
    } catch (e) {
      log('Edit Profile Error: $e');
      return 'failed';
    }
  }

// =============================================================================
// API METHODS
// =============================================================================

  // Cache storage instance for better performance
  static final GetStorage _storage = GetStorage();

  // Common headers for authenticated requests
  static Map<String, String> get _authHeaders => {
        "Content-Type": "application/json",
        "Authorization": "Bearer ${_storage.read('auth_token') ?? ''}",
      };

  // Common headers for non-authenticated requests
  static Map<String, String> get _headers => {
        "Content-Type": "application/json",
      };

  /// Generic method to handle all GET requests
  static Future<dynamic> _handleGetRequest(
    String endpoint, {
    bool requireAuth = true,
    bool allowBadRequest = false,
    String? customErrorMessage,
  }) async {
    final token = _storage.read('auth_token');
    log('Auth Token: ${token?.toString() ?? 'NULL'}');
    log('Require Auth: $requireAuth');
    try {

      final url = Uri.parse('$api$endpoint');
      print('GET Request: $url');

      final response = await http.get(
        url,
        headers: requireAuth ? _authHeaders : _headers,
      );

      log(response.body.toString());

      return _handleResponse(
        response,
        allowBadRequest: allowBadRequest,
        customErrorMessage: customErrorMessage,
      );
    } catch (e) {
      log('GET Request Error: $e');
      return 'failed';
    }
  }

  /// Generic method to handle all POST requests
  static Future<dynamic> _handlePostRequest(
    String endpoint,
    Map<String, String> body, {
    bool allowBadRequest = false,
    String? customErrorMessage,
  }) async {
    try {
      final url = Uri.parse('$api$endpoint');
      log('POST Request: $url');

      final response = await http.post(
        url,
        headers: {"Authorization": "Bearer ${_storage.read('auth_token') ?? ''}"},
        body: body,
      );

      return _handleResponse(
        response,
        allowBadRequest: allowBadRequest,
        customErrorMessage: customErrorMessage,
      );
    } catch (e) {
      log('POST Request Error: $e');
      return 'failed';
    }
  }

  /// Centralized response handling
  static dynamic _handleResponse(
    http.Response response, {
    bool allowBadRequest = false,
    String? customErrorMessage,
  }) {
    log('Response Status: ${response.statusCode}');

    switch (response.statusCode) {
      case 200:
        return jsonDecode(utf8.decode(response.bodyBytes));

      case 400:
        if (allowBadRequest) {
          return jsonDecode(utf8.decode(response.bodyBytes));
        }
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        if (data['message'] != null) {
          toast_error(data['message'].toString());
        }
        return data;

      case 401:
        log('401 Unauthorized - Session Expired');
        log('Current token: ${_storage.read('auth_token')?.toString() ?? 'NULL'}');
        _handleSessionExpired();
        return 'failed';

      case 404:
        _navigateToErrorPage(() => error_404());
        return 'failed';

      case 500:
        _navigateToErrorPage(() => error_500());
        return 'failed';

      default:
        if (customErrorMessage != null) {
          toast_error(customErrorMessage);
        }
        return 'failed';
    }
  }

  /// Handle session expiration
  static void _handleSessionExpired() {
    toast_info("Session Expired");
    Get.offAll(() => Login());
  }

  /// Navigate to error pages
  static void _navigateToErrorPage(Widget Function() errorPage) {
    Get.offAll(errorPage);
  }

// =============================================================================
// API METHODS
// =============================================================================
}
