// import 'dart:async';
// import 'dart:convert';
// import 'dart:developer';
// import 'dart:io';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/components/utils.dart';
// import 'package:prepscale/view/chat/controller/groupchatcontroller.dart';
// import 'package:prepscale/view/chat/group_chat/comment_page.dart';
// import 'package:prepscale/view/chat/group_chat/local_widget/audio_bubble.dart';
// import 'package:prepscale/view/chat/group_chat/local_widget/date_header.dart';
// import 'package:prepscale/view/chat/group_chat/local_widget/delete_message_dialogue.dart';
// import 'package:prepscale/view/chat/group_chat/local_widget/image_preview.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:path_provider/path_provider.dart';
//
// import 'package:path/path.dart' as path;
//
// class group_chat_screen extends StatefulWidget {
//   String title;
//   String chat_id, from;
//   String chatType, chatTypeId;
//   group_chat_screen(
//       {required this.title,
//       required this.chat_id,
//       required this.from,
//       required this.chatType,
//       required this.chatTypeId});
//
//   @override
//   State<group_chat_screen> createState() => _chat_screenState();
// }
//
// class _chat_screenState extends State<group_chat_screen> {
//   GroupchatController controller = Get.put(GroupchatController());
//
//   // final FlutterSoundRecorder _recorder = FlutterSoundRecorder();
//   bool _isRecording = false;
//   bool _isPlaying = false;
//   String? _recordedFilePath;
//   int _recordingTime = 0;
//   Timer? _timer;
//
//   Map<int, bool> showCommentsMap = {};
//   final FocusNode _focusNode = FocusNode();
//
//   @override
//   void initState() {
//     super.initState();
//     _initRecorder();
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       controller.messageListData(widget.chat_id);
//       // controller.messageListData(widget.chat_id).then((_) {
//       //   // log('Message List: ${widget.chat_id}');
//       // });
//       // _scrollToBottom();
//     });
//   }
//
//   Future<void> _initRecorder() async {
//     await Permission.microphone.request();
//     // await _recorder.openRecorder();
//   }
//
//   Future<void> _startRecording() async {
//     Directory tempDir = await getTemporaryDirectory();
//     _recordedFilePath =
//         '${tempDir.path}/audio_${DateTime.now().millisecondsSinceEpoch}.wav';
//
//     // await _recorder.startRecorder(
//     //   toFile: _recordedFilePath,
//     //   // codec: Codec.pcm16WAV,
//     // );
//     setState(() {
//       _isRecording = true;
//       _recordingTime = 0; // Reset recording time
//     });
//
//     // Start the timer
//     _timer = Timer.periodic(Duration(seconds: 1), (Timer t) {
//       setState(() {
//         _recordingTime++;
//       });
//     });
//   }
//
//   @override
//   void dispose() {
//     // _recorder.closeRecorder();
//     // _player.closePlayer();
//
//     super.dispose();
//   }
//
//   void _scrollToBottom() {
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       if (controller.chatScrollController.hasClients) {
//         controller.chatScrollController.jumpTo(
//           controller.chatScrollController.position.maxScrollExtent,
//         );
//       }
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // WidgetsBinding.instance.addPostFrameCallback((_) {
//     //   if (controller.chatScrollController.hasClients) {
//     //     // Immediately jump to the bottom of the chat list
//     //     controller.chatScrollController.jumpTo(
//     //       controller.chatScrollController.position.maxScrollExtent,
//     //     );
//     //   }
//     // });
//
//     return GetBuilder<GroupchatController>(builder: (controller) {
//       final ScrollController _scrollController = ScrollController();
//       return GestureDetector(
//         onTap: () {
//           FocusScope.of(context).unfocus();
//         },
//         child: Scaffold(
//           appBar: AppBar(
//             backgroundColor: textwhiteColor,
//             surfaceTintColor: textwhiteColor,
//             leading: GestureDetector(
//               onTap: () {
//                 Get.back();
//               },
//               child: Container(
//                 padding: const EdgeInsets.all(10),
//                 child: Image.asset('assets/chaticons/close.png'),
//               ),
//             ),
//             title: Container(
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 children: [
//                   Container(
//                       child: Image.asset(
//                     'assets/logo/logo_transp.png',
//                     width: Get.width / 13,
//                   )),
//                   const SizedBox(
//                     width: 10,
//                   ),
//                   Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Container(
//                         alignment: Alignment.centerLeft,
//                         width: Get.width / 1.7,
//                         child: Text(
//                           widget.title.toString(),
//                           style: TextStyle(
//                               color: textblackColor,
//                               fontFamily: font_medium,
//                               fontSize: Get.width / 26),
//                           textAlign: TextAlign.center,
//                         ),
//                       ),
//                       const SizedBox(
//                         height: 3,
//                       ),
//                       Container(
//                         alignment: Alignment.centerLeft,
//                         width: Get.width / 1.5,
//                         child: Text(
//                           'Active',
//                           style: TextStyle(
//                               color: primaryColor,
//                               fontFamily: font_regular,
//                               fontSize: Get.width / 33),
//                           textAlign: TextAlign.center,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//             actions: [
//               // Image.asset(
//               //   'assets/chaticons/more_vert.png',
//               //   height: Get.width / 19,
//               // ),
//               // const SizedBox(
//               //   width: 15,
//               // ),
//             ],
//           ),
//           body: (widget.from == "chat_list" && controller.isMessageListloading)
//               ? loader()
//               : Column(
//                   children: [
//                     Expanded(
//                       child: Container(
//                         color: chatbgColor,
//                         child: Stack(
//                           children: [
//                             if (controller.messageList.isEmpty) const Center(
//                                     child: Text('No messages'),
//                                   ) else SingleChildScrollView(
//                                     controller: controller.chatScrollController,
//                                     child: Container(
//                                       padding: const EdgeInsets.symmetric(
//                                           horizontal: 15),
//                                       child: ListView.builder(
//                                         physics: const ScrollPhysics(),
//                                         controller: _scrollController,
//                                         padding: EdgeInsets.only(
//                                             bottom: Get.height / 10.5),
//                                         shrinkWrap: true,
//                                         itemCount:
//                                             controller.messageList.length,
//                                         itemBuilder:
//                                             (BuildContext context, int index) {
//                                           print(
//                                               'Length: ${controller.messageList.length}');
//                                           var data =
//                                               controller.messageList[index];
//
//                                           final date =
//                                               controller.messageList[index]
//                                                   ['message_date'];
//                                           bool showDateHeader = false;
//                                           if (index > 0) {
//                                             final previousMessage = controller
//                                                 .messageList[index - 1];
//                                             final previousDate =
//                                                 previousMessage['message_date'];
//                                             showDateHeader =
//                                                 previousDate != date;
//                                           }
//
//                                           return Column(
//                                             children: [
//                                               showDateHeader == true
//                                                   ? DateHeader(
//                                                       date: date.toString(),
//                                                     )
//                                                   : index == 0
//                                                       ? DateHeader(
//                                                           date: date.toString(),
//                                                         )
//                                                       : Container(),
//                                               GestureDetector(
//                                                   onTap: () {},
//                                                   onLongPress: () {
//                                                     showModalBottomSheet(
//                                                       context: context,
//                                                       builder: (BuildContext
//                                                           context) {
//                                                         return Padding(
//                                                           padding:
//                                                               const EdgeInsets
//                                                                   .all(10),
//                                                           child: Column(
//                                                             mainAxisSize:
//                                                                 MainAxisSize
//                                                                     .min,
//                                                             crossAxisAlignment:
//                                                                 CrossAxisAlignment
//                                                                     .start,
//                                                             children: [
//                                                               if (controller.messageList[
//                                                                               index]
//                                                                           [
//                                                                           'message_type'] ==
//                                                                       "1" &&
//                                                                   controller.messageList[
//                                                                               index]
//                                                                           [
//                                                                           'sender_name'] ==
//                                                                       "You")
//                                                                 ListTile(
//                                                                   title:
//                                                                       const Text(
//                                                                     'Edit Message',
//                                                                     style: TextStyle(
//                                                                         fontSize:
//                                                                             12),
//                                                                   ),
//                                                                   onTap: () {
//                                                                     showDialog(
//                                                                       context:
//                                                                           context,
//                                                                       builder:
//                                                                           (BuildContext
//                                                                               context) {
//                                                                         TextEditingController
//                                                                             editController =
//                                                                             TextEditingController(
//                                                                           text: controller
//                                                                               .messageList[index]['message']
//                                                                               .toString(),
//                                                                         );
//                                                                         return AlertDialog(
//                                                                           title:
//                                                                               const Text(
//                                                                             'Edit Message',
//                                                                             style:
//                                                                                 TextStyle(fontSize: 15),
//                                                                           ),
//                                                                           content:
//                                                                               TextField(
//                                                                             controller:
//                                                                                 editController,
//                                                                             decoration:
//                                                                                 const InputDecoration(hintText: 'Enter new message', hintStyle: TextStyle(fontSize: 10)),
//                                                                           ),
//                                                                           actions: [
//                                                                             TextButton(
//                                                                               onPressed: () {
//                                                                                 if (editController.text != "") {
//                                                                                   controller.editMessage(
//                                                                                     controller.messageList[index]['id'].toString(),
//                                                                                     editController.text.toString(),
//                                                                                   );
//                                                                                   Navigator.of(context).pop();
//                                                                                   Navigator.of(context).pop();
//                                                                                 }
//                                                                               },
//                                                                               child: Text(
//                                                                                 'Save',
//                                                                                 style: TextStyle(color: primaryColor),
//                                                                               ),
//                                                                             ),
//                                                                             TextButton(
//                                                                               onPressed: () {
//                                                                                 Navigator.of(context).pop(); // Close the dialog
//                                                                               },
//                                                                               child: Text(
//                                                                                 'Cancel',
//                                                                                 style: TextStyle(color: primaryColor),
//                                                                               ),
//                                                                             ),
//                                                                           ],
//                                                                         );
//                                                                       },
//                                                                     );
//                                                                   },
//                                                                 ),
//                                                               if (GetStorage()
//                                                                           .read(
//                                                                               'is_admin')
//                                                                           .toString() ==
//                                                                       "1" ||
//                                                                   controller.messageList[
//                                                                               index]
//                                                                           [
//                                                                           'sender_name'] ==
//                                                                       "You")
//                                                                 ListTile(
//                                                                   title:
//                                                                       const Text(
//                                                                     'Delete Message',
//                                                                     style: TextStyle(
//                                                                         fontSize:
//                                                                             12),
//                                                                   ),
//                                                                   onTap: () {
//                                                                     showDialog(
//                                                                         context:
//                                                                             context,
//                                                                         builder:
//                                                                             (context) {
//                                                                           return DeleteMessageDialogue(
//                                                                             onPressed:
//                                                                                 () {
//                                                                               // Call the delete function
//                                                                               controller.deleteMessage(data['id'].toString());
//                                                                               print('Delete clicked');
//                                                                               Navigator.of(context).pop(); // Close the BottomSheet
//                                                                               Navigator.of(context).pop();
//                                                                             },
//                                                                           );
//                                                                         });
//                                                                   },
//                                                                 ),
//                                                             ],
//                                                           ),
//                                                         );
//                                                       },
//                                                     );
//                                                   },
//                                                   child: Container(
//                                                     alignment: data['sender_name']
//                                                                 .toString() !=
//                                                             "You"
//                                                         ? Alignment.centerLeft
//                                                         : Alignment.centerRight,
//                                                     margin: const EdgeInsets
//                                                         .symmetric(
//                                                         vertical: 12),
//                                                     child: Row(
//                                                       mainAxisSize:
//                                                           MainAxisSize.min,
//                                                       crossAxisAlignment: data[
//                                                                       'sender_name']
//                                                                   .toString() !=
//                                                               "You"
//                                                           ? CrossAxisAlignment
//                                                               .start
//                                                           : CrossAxisAlignment
//                                                               .end,
//                                                       children: [
//                                                         if (data['sender_name']
//                                                                 .toString() !=
//                                                             "You")
//                                                           Container(
//                                                             margin:
//                                                                 const EdgeInsets
//                                                                     .only(
//                                                                     right: 13),
//                                                             child: ClipRRect(
//                                                                 borderRadius:
//                                                                     BorderRadius
//                                                                         .circular(
//                                                                             1000),
//                                                                 child:
//                                                                     CachedNetworkImage(
//                                                                   height:
//                                                                       Get.width /
//                                                                           10,
//                                                                   width:
//                                                                       Get.width /
//                                                                           10,
//                                                                   progressIndicatorBuilder:
//                                                                       (context,
//                                                                           url,
//                                                                           progress) {
//                                                                     return loader();
//                                                                   },
//                                                                   imageUrl: data[
//                                                                           'sender_image']
//                                                                       .toString(),
//                                                                   errorWidget: (context,
//                                                                           url,
//                                                                           error) =>
//                                                                       const Icon(
//                                                                           Icons
//                                                                               .error),
//                                                                 )),
//                                                           ),
//                                                         Container(
//                                                           decoration:
//                                                               BoxDecoration(
//                                                             color: data['sender_name']
//                                                                         .toString() ==
//                                                                     "You"
//                                                                 ? primaryColor
//                                                                     .withOpacity(
//                                                                         0.7)
//                                                                 // ? secondaryColor2.withOpacity(0.8)
//                                                                 : textwhiteColor,
//                                                             borderRadius:
//                                                                 BorderRadius
//                                                                     .only(
//                                                               topRight:
//                                                                   const Radius
//                                                                       .circular(
//                                                                       20),
//                                                               bottomRight: Radius
//                                                                   .circular(
//                                                                       data['sender_name'].toString() !=
//                                                                               "You"
//                                                                           ? 20
//                                                                           : 0),
//                                                               topLeft: Radius.circular(
//                                                                   data['sender_name']
//                                                                               .toString() !=
//                                                                           "You"
//                                                                       ? 0
//                                                                       : 20),
//                                                               bottomLeft:
//                                                                   const Radius
//                                                                       .circular(
//                                                                       20),
//                                                             ),
//                                                           ),
//                                                           constraints:
//                                                               BoxConstraints(
//                                                             maxWidth:
//                                                                 Get.width /
//                                                                     1.35,
//                                                           ),
//                                                           padding:
//                                                               const EdgeInsets
//                                                                   .only(
//                                                                   left: 15,
//                                                                   right: 15,
//                                                                   top: 10,
//                                                                   bottom: 10),
//                                                           //Intrinsicwidth
//                                                           child: IntrinsicWidth(
//                                                             child: Column(
//                                                               crossAxisAlignment:
//                                                                   data['sender_name']
//                                                                               .toString() ==
//                                                                           'You'
//                                                                       ? CrossAxisAlignment
//                                                                           .end
//                                                                       : CrossAxisAlignment
//                                                                           .start,
//                                                               children: [
//                                                                 // if(index.isOdd)
//                                                                 Row(
//                                                                   children: [
//                                                                     Text(
//                                                                       data['sender_name']
//                                                                           .toString(),
//                                                                       style: TextStyle(
//                                                                           color: data['sender_name'].toString() == 'You'
//                                                                               ? Colors.white
//                                                                               : data['is_highlight'].toString() == "1"
//                                                                                   ? primaryColor
//                                                                                   : Colors.black,
//                                                                           fontFamily: font_medium,
//                                                                           fontSize: data['is_highlight'].toString() == "1" ? Get.width / 35 : Get.width / 40),
//                                                                       textAlign:
//                                                                           TextAlign
//                                                                               .end,
//                                                                     ),
//                                                                     if (data['is_highlight']
//                                                                             .toString() ==
//                                                                         "1")
//                                                                       Row(
//                                                                         children: [
//                                                                           const SizedBox(
//                                                                             width:
//                                                                                 3,
//                                                                           ),
//                                                                           Container(
//                                                                               margin: const EdgeInsets.only(top: 4),
//                                                                               child: Icon(
//                                                                                 Icons.verified,
//                                                                                 color: Colors.blue,
//                                                                                 size: Get.width / 28,
//                                                                               )),
//                                                                         ],
//                                                                       ),
//                                                                   ],
//                                                                 ),
//                                                                 const SizedBox(
//                                                                   height: 3,
//                                                                 ),
//
//                                                                 if (data['file']
//                                                                         .toString() !=
//                                                                     "null")
//                                                                   Column(
//                                                                     children: [
//                                                                       const SizedBox(
//                                                                         height:
//                                                                             5,
//                                                                       ),
//                                                                       GestureDetector(
//                                                                         onTap:
//                                                                             () {
//                                                                           // controller.photo_view(context, data['file']);
//                                                                         },
//                                                                         child:
//                                                                             ClipRRect(
//                                                                           borderRadius:
//                                                                               BorderRadius.circular(15),
//                                                                           child:
//                                                                               Image.file(
//                                                                             data['file'],
//                                                                             height:
//                                                                                 Get.width / 2,
//                                                                             width:
//                                                                                 Get.width / 2,
//                                                                             fit:
//                                                                                 BoxFit.cover,
//                                                                           ),
//                                                                         ),
//                                                                       ),
//                                                                       const SizedBox(
//                                                                         height:
//                                                                             5,
//                                                                       ),
//                                                                     ],
//                                                                   ),
//                                                                 Row(
//                                                                   mainAxisAlignment: data['sender_name']
//                                                                               .toString() ==
//                                                                           'You'
//                                                                       ? MainAxisAlignment
//                                                                           .end
//                                                                       : MainAxisAlignment
//                                                                           .start,
//                                                                   children: [
//                                                                     if (data[
//                                                                             'message_type'] ==
//                                                                         "1")
//                                                                       Flexible(
//                                                                         child:
//                                                                             Text(
//                                                                           data['message']
//                                                                               .toString(),
//                                                                           style:
//                                                                               TextStyle(
//                                                                             color: data['sender_name'].toString() != 'You'
//                                                                                 ? Colors.black
//                                                                                 : Colors.white,
//                                                                             fontFamily:
//                                                                                 font_regular,
//                                                                             fontSize:
//                                                                                 Get.width / 30,
//                                                                           ),
//                                                                           softWrap:
//                                                                               true, // Allows the text to wrap
//                                                                           maxLines:
//                                                                               null, // Allows unlimited lines, expanding as needed
//                                                                         ),
//                                                                       ),
//                                                                     if (data[
//                                                                             'message_type'] ==
//                                                                         "2")
//                                                                       GestureDetector(
//                                                                         onTap:
//                                                                             () {
//                                                                           Get.to(
//                                                                               ImagePreviewPage(imageUrl: data['message_file']));
//                                                                         },
//                                                                         child: Image
//                                                                             .network(
//                                                                           height:
//                                                                               Get.width - 125,
//                                                                           width:
//                                                                               Get.width - 150,
//                                                                           data[
//                                                                               'message_file'],
//                                                                           fit: BoxFit
//                                                                               .contain,
//                                                                         ),
//                                                                       ),
//                                                                     if (data[
//                                                                             'message_type'] ==
//                                                                         "4")
//                                                                       Expanded(
//                                                                         child: Text('audio player'),
//                                                                       )
//                                                                   ],
//                                                                 ),
//
//                                                                 const SizedBox(
//                                                                     height: 3),
//
//                                                                 Row(
//                                                                   mainAxisAlignment:
//                                                                       MainAxisAlignment
//                                                                           .spaceBetween,
//                                                                   children: [
//                                                                     Text(
//                                                                       "${data['message_time'].toString()} ",
//                                                                       style: TextStyle(
//                                                                           color: data['sender_name'].toString() != 'You'
//                                                                               ? Colors.black.withOpacity(
//                                                                                   0.5)
//                                                                               : Colors.white.withOpacity(
//                                                                                   0.5),
//                                                                           fontFamily:
//                                                                               font_regular,
//                                                                           fontSize:
//                                                                               Get.width / 45),
//                                                                     ),
//                                                                     const SizedBox(
//                                                                       width: 10,
//                                                                     ),
//                                                                     if (data[
//                                                                             'message_type'] !=
//                                                                         "4")
//                                                                       GestureDetector(
//                                                                         onTap:
//                                                                             () {
//                                                                           controller.commentId =
//                                                                               data['id'].toString();
//                                                                           Navigator.of(context)
//                                                                               .push(MaterialPageRoute(
//                                                                             builder:
//                                                                                 (context) {
//                                                                               return CommentPage(
//                                                                                 from: "chat",
//                                                                                 chatId: data['id'],
//                                                                                 chatType: data['chat_type'],
//                                                                                 chatTypeId: data['chat_type_id'],
//                                                                               );
//                                                                             },
//                                                                           ));
//                                                                           print(
//                                                                               'pressed');
//                                                                         },
//                                                                         child:
//                                                                             Container(
//                                                                           height:
//                                                                               15,
//                                                                           color:
//                                                                               Colors.transparent,
//                                                                           child:
//                                                                               Text(
//                                                                             'comment',
//                                                                             style: TextStyle(
//                                                                                 fontSize: 10,
//                                                                                 fontWeight: FontWeight.bold,
//                                                                                 color: data['sender_name'].toString() == 'You' ? textwhiteColor : textblackColor),
//                                                                           ),
//                                                                         ),
//                                                                       )
//                                                                     // : const SizedBox()
//                                                                   ],
//                                                                 ),
//                                                               ],
//                                                             ),
//                                                           ),
//                                                         ),
//                                                         if (data['sender_name']
//                                                                 .toString() ==
//                                                             "You")
//                                                           Container(
//                                                             height:
//                                                                 Get.width / 10,
//                                                             width:
//                                                                 Get.width / 10,
//                                                             margin:
//                                                                 const EdgeInsets
//                                                                     .only(
//                                                                     left: 13),
//                                                             child: ClipRRect(
//                                                                 borderRadius:
//                                                                     BorderRadius
//                                                                         .circular(
//                                                                             1000),
//                                                                 child:
//                                                                     CachedNetworkImage(
//                                                                   fit: BoxFit
//                                                                       .cover,
//                                                                   imageUrl: data[
//                                                                           'sender_image']
//                                                                       .toString(),
//                                                                   errorWidget: (context,
//                                                                           url,
//                                                                           error) =>
//                                                                       Icon(Icons
//                                                                           .error),
//                                                                   progressIndicatorBuilder:
//                                                                       (context,
//                                                                               url,
//                                                                               progress) =>
//                                                                           loader(),
//                                                                 )),
//                                                           ),
//                                                       ],
//                                                     ),
//                                                   )),
//                                             ],
//                                           );
//                                         },
//                                       ),
//                                     ),
//                                   ),
//                             // controller.isChatListloading
//                             //     ? const Positioned(
//                             //         bottom: 60,
//                             //         left: 0,
//                             //         right: 0,
//                             //         child: Center(
//                             //             child: CircularProgressIndicator()))
//                             //     :  SizedBox(),
//                             Positioned(
//                               bottom: 0,
//                               left: 0,
//                               right: 0,
//                               child: Column(
//                                 children: [
//                                   if (controller.showAttachmentSection)
//                                     Stack(
//                                       children: [
//                                         Container(
//                                           decoration: BoxDecoration(
//                                               color: Colors.transparent,
//                                               borderRadius:
//                                                   const BorderRadius.all(
//                                                       Radius.circular(10)),
//                                               boxShadow: [
//                                                 BoxShadow(
//                                                     color: Colors.black
//                                                         .withOpacity(0.1),
//                                                     blurRadius: 1)
//                                               ]),
//                                           margin: const EdgeInsets.symmetric(
//                                               horizontal: 20),
//                                           padding: const EdgeInsets.only(
//                                               left: 20,
//                                               right: 20,
//                                               top: 20,
//                                               bottom: 10),
//                                           child: Row(
//                                             mainAxisAlignment:
//                                                 MainAxisAlignment.center,
//                                             children: [
//                                               GestureDetector(
//                                                 onTap: () {
//                                                   controller.getFromGallery();
//                                                 },
//                                                 child: Column(
//                                                   children: [
//                                                     Container(
//                                                       padding:
//                                                           const EdgeInsets.all(
//                                                               15),
//                                                       decoration: BoxDecoration(
//                                                         color: primaryColor
//                                                             .withOpacity(0.5),
//                                                         borderRadius:
//                                                             BorderRadius
//                                                                 .circular(15),
//                                                       ),
//                                                       child: const Icon(
//                                                         Icons.image_rounded,
//                                                         size: 25,
//                                                         color: Colors.white,
//                                                       ),
//                                                     ),
//                                                     const SizedBox(
//                                                       height: 5,
//                                                     ),
//                                                     Text(
//                                                       'Image',
//                                                       style: TextStyle(
//                                                           fontSize:
//                                                               Get.width / 38),
//                                                     )
//                                                   ],
//                                                 ),
//                                               ),
//                                               const SizedBox(
//                                                 width: 20,
//                                               ),
//                                               GestureDetector(
//                                                 onTap: () {
//                                                   controller.getFromCamera();
//                                                 },
//                                                 child: Column(
//                                                   children: [
//                                                     Container(
//                                                       padding:
//                                                           const EdgeInsets.all(
//                                                               15),
//                                                       decoration: BoxDecoration(
//                                                         color: primaryColor
//                                                             .withOpacity(0.5),
//                                                         borderRadius:
//                                                             BorderRadius
//                                                                 .circular(15),
//                                                       ),
//                                                       child: const Icon(
//                                                         CupertinoIcons
//                                                             .camera_circle_fill,
//                                                         size: 25,
//                                                         color: Colors.white,
//                                                       ),
//                                                     ),
//                                                     const SizedBox(
//                                                       height: 5,
//                                                     ),
//                                                     Text(
//                                                       'Camera',
//                                                       style: TextStyle(
//                                                           fontSize:
//                                                               Get.width / 38),
//                                                     )
//                                                   ],
//                                                 ),
//                                               ),
//                                             ],
//                                           ),
//                                         ),
//                                         Positioned(
//                                             top: 2,
//                                             right: 22,
//                                             child: GestureDetector(
//                                               onTap: () {
//                                                 setState(() {
//                                                   controller
//                                                           .showAttachmentSection =
//                                                       false;
//                                                 });
//                                               },
//                                               child: Container(
//                                                 padding:
//                                                     const EdgeInsets.all(5),
//                                                 child: const Icon(
//                                                     Icons.close_rounded),
//                                               ),
//                                             ))
//                                       ],
//                                     ),
//                                   if (controller.imageFile != null)
//                                     Container(
//                                       decoration: const BoxDecoration(
//                                         color: Colors.white,
//                                         borderRadius: BorderRadius.all(
//                                             Radius.circular(15)),
//                                       ),
//                                       padding: const EdgeInsets.all(10),
//                                       margin: const EdgeInsets.all(10),
//                                       child: Stack(
//                                         children: [
//                                           ClipRRect(
//                                             borderRadius:
//                                                 BorderRadius.circular(10),
//                                             child: Image.file(
//                                               controller.imageFile!,
//                                               height: Get.width,
//                                               width: Get.width,
//                                             ),
//                                           ),
//                                           Positioned(
//                                             bottom: 55,
//                                             right: 10,
//                                             child: GestureDetector(
//                                               onTap: () {
//                                                 setState(() {
//                                                   controller.imageFile = null;
//                                                 });
//                                               },
//                                               child: Container(
//                                                 decoration: BoxDecoration(
//                                                     shape: BoxShape.circle,
//                                                     color: Colors.white,
//                                                     boxShadow: [
//                                                       BoxShadow(
//                                                           color: Colors.black
//                                                               .withOpacity(0.3),
//                                                           blurRadius: 5)
//                                                     ]),
//                                                 padding:
//                                                     const EdgeInsets.all(6),
//                                                 child: Icon(Icons.close_rounded,
//                                                     color: textblackColor),
//                                               ),
//                                             ),
//                                           ),
//                                           Positioned(
//                                             bottom: 10,
//                                             right: 10,
//                                             child: GestureDetector(
//                                               onTap: () async {
//                                                 widget.from = "";
//
//                                                 //send message
//                                                 await controller.sendMessage(
//                                                     chatType: widget.chatType,
//                                                     chatTypeId:
//                                                         widget.chatTypeId,
//                                                     messageType: "2",
//                                                     message: "",
//                                                     isTaggedMessage: "0",
//                                                     taggedMsgId: "0",
//                                                     filePath:
//                                                         controller.imageFile);
//
//                                                 setState(() {
//                                                   controller.chatId = widget.chat_id.toString();
//                                                 });
//                                                 await controller.messageListData(widget.chat_id);
//
//                                                 setState(() {
//                                                   controller.imageFile = null;
//                                                 });
//                                               },
//                                               child: Container(
//                                                 decoration: BoxDecoration(
//                                                     shape: BoxShape.circle,
//                                                     color: Colors.white,
//                                                     boxShadow: [
//                                                       BoxShadow(
//                                                           color: Colors.black
//                                                               .withOpacity(0.3),
//                                                           blurRadius: 5)
//                                                     ]),
//                                                 padding:
//                                                     const EdgeInsets.all(6),
//                                                 child: Icon(Icons.check_rounded,
//                                                     color: textblackColor),
//                                               ),
//                                             ),
//                                           )
//                                         ],
//                                       ),
//                                     ),
//                                   controller.isMessageListloading ||
//                                           controller.commonLoader
//                                       ? loader()
//                                       : const SizedBox(),
//
//
//
//
//                                        Container(
//                                          padding: EdgeInsets.symmetric(vertical: 20,horizontal: 10),
//                                          child: Container(
//                                            width: Get.width,
//                                            decoration: BoxDecoration(
//                                                borderRadius:
//                                                BorderRadius.circular(20),
//                                                color: Colors.white,
//                                                boxShadow: [
//                                                  BoxShadow(
//                                                      color: Colors.black
//                                                          .withOpacity(0.1),
//                                                      blurRadius: 5)
//                                                ]),
//                                            margin: const EdgeInsets.symmetric(
//                                                horizontal: 10, vertical: 5),
//                                            padding: const EdgeInsets.symmetric(
//                                                horizontal: 5),
//                                            child: Row(
//                                              children: [
//                                                Expanded(
//                                                  child: TextField(
//                                                    focusNode: _focusNode,
//                                                    onTap: () {
//                                                      setState(() {
//                                                        controller
//                                                            .showAttachmentSection =
//                                                        false;
//                                                      });
//                                                    },
//                                                    onChanged: (text) {
//                                                      setState(() {
//                                                        controller.isText =
//                                                        text.isEmpty
//                                                            ? false
//                                                            : true;
//                                                      });
//                                                    },
//                                                    maxLines: null,
//                                                    keyboardType:
//                                                    TextInputType.multiline,
//                                                    textInputAction:
//                                                    TextInputAction.newline,
//                                                    controller: controller
//                                                        .msgTextController,
//                                                    style: TextStyle(
//                                                        fontSize: Get.width / 28,
//                                                        color: Colors.black),
//                                                    decoration: InputDecoration(
//                                                      focusedBorder:
//                                                      OutlineInputBorder(
//                                                        borderSide:
//                                                        const BorderSide(
//                                                            color: Colors
//                                                                .transparent,
//                                                            width: 1),
//                                                        borderRadius:
//                                                        BorderRadius.circular(
//                                                            10.0),
//                                                      ),
//                                                      enabledBorder:
//                                                      OutlineInputBorder(
//                                                        borderSide:
//                                                        const BorderSide(
//                                                            color: Colors
//                                                                .transparent,
//                                                            width: 1),
//                                                        borderRadius:
//                                                        BorderRadius.circular(
//                                                            10.0),
//                                                      ),
//                                                      filled: true,
//                                                      hintStyle: TextStyle(
//                                                          fontFamily:
//                                                          font_regular,
//                                                          color: Colors.black38,
//                                                          fontSize:
//                                                          Get.width / 28),
//                                                      hintText:
//                                                      "Type your message here",
//                                                      fillColor:
//                                                      Colors.transparent,
//                                                      contentPadding:
//                                                      const EdgeInsets
//                                                          .symmetric(
//                                                          vertical: 15.0,
//                                                          horizontal: 20),
//                                                    ),
//                                                  ),
//                                                ),
//                                                // GestureDetector(
//                                                //   onTap: () {
//                                                //     setState(() {
//                                                //       controller
//                                                //               .showAttachmentSection =
//                                                //           controller
//                                                //                   .showAttachmentSection
//                                                //               ? false
//                                                //               : true;
//                                                //     });
//                                                //   },
//                                                //   child: SizedBox(
//                                                //     height: 50,
//                                                //     width: 40,
//                                                //     child: Padding(
//                                                //       padding: const EdgeInsets
//                                                //           .symmetric(
//                                                //           horizontal: 5.0),
//                                                //       child: GestureDetector(
//                                                //         child: const Icon(
//                                                //           Icons
//                                                //               .attachment_rounded,
//                                                //           size: 25,
//                                                //         ),
//                                                //       ),
//                                                //     ),
//                                                //   ),
//                                                // ),
//                                                GestureDetector(
//                                                  onTap: () {
//                                                    widget.from = "";
//
//                                                    if (controller.msgTextController.text != "") {
//                                                      controller.sendMessage(
//                                                        chatType: widget.chatType,
//                                                        chatTypeId: widget.chatTypeId,
//                                                        messageType: "1",
//                                                        message: controller.msgTextController.text.toString(),
//                                                        isTaggedMessage: "",
//                                                        taggedMsgId: "0",
//                                                      );
//
//
//                                                      controller.messageListData(widget.chat_id);
//                                                    }
//
//                                                  },
//                                                  child: SizedBox(
//                                                    height: 50,
//                                                    width: 40,
//                                                    child: Padding(
//                                                      padding:
//                                                      const EdgeInsets
//                                                          .symmetric(
//                                                          horizontal:
//                                                          5.0),
//                                                      child:
//                                                      GestureDetector(
//                                                        child: const Icon(
//                                                          Icons
//                                                              .send_rounded,
//                                                          size: 25,
//                                                        ),
//                                                      ),
//                                                    ),
//                                                  ),
//                                                )
//
//                                              ],
//                                            ),
//                                          ),
//                                        ),
//
//
//
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//         ),
//       );
//     });
//   }
// }
//
// // Custom painter for visualizing audio waveform
// class WaveformPainter extends CustomPainter {
//   @override
//   void paint(Canvas canvas, Size size) {
//     final Paint paint = Paint()
//       ..color = Colors.blue
//       ..style = PaintingStyle.fill;
//
//     // Create a simple waveform shape (for demonstration)
//     Path path = Path();
//     path.moveTo(0, size.height / 2);
//
//     // Simulate waveform with random values for illustration
//     for (double x = 0; x < size.width; x++) {
//       double y = size.height / 2 +
//           (size.height / 4) *
//               (0.5 - (x / size.width)); // Replace this with actual data
//       path.lineTo(x, y);
//     }
//
//     path.lineTo(size.width, size.height / 2);
//     path.lineTo(size.width, size.height);
//     path.lineTo(0, size.height);
//     path.close();
//
//     canvas.drawPath(path, paint);
//   }
//
//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) {
//     return true; // Update on every repaint
//   }
// }
