//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:eduthalim/components/constants.dart';
// import 'package:eduthalim/views/dashboard/chat/controller/groupchatcontroller.dart';
//
// class ChatListPage extends StatefulWidget {
//   final String from; // Make title final if it's not meant to be modified
//   ChatListPage({super.key, required this.from});
//
//   @override
//   State<ChatListPage> createState() => _ChatListPage(); // Specify the type here
// }
//
// class _ChatListPage extends State<ChatListPage> {
//   // Change this to ChatListPage
//   GroupchatController controller = Get.put(GroupchatController());
//   // AccountController acountController = Get.put(AccountController());
//
//   @override
//   void initState() {
//     controller.chatListData();
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<GroupchatController>(builder: (controller) {
//       return Scaffold(
//         backgroundColor: textwhiteColor,
//         appBar: AppBar(
//           backgroundColor: textwhiteColor,
//           surfaceTintColor: textwhiteColor,
//           leading: Padding(
//             padding: const EdgeInsets.all(12.0),
//             child: GestureDetector(
//               onTap: () {
//                 if (widget.from == 'home') {
//                   GetStorage().write('home_index', '0');
//                   Get.offAll(dashboard(),transition: Transition.noTransition);
//                 } else {
//                   Get.back();
//                 }
//                 // Navigator.of(context).pop();
//               },
//               child: Container(
//                 decoration: BoxDecoration(
//                   color: secondaryColor,
//                   borderRadius: BorderRadius.circular(1000),
//                 ),
//                 child: Icon(Icons.keyboard_arrow_left,color: textwhiteColor,),
//               ),
//             ),
//           ),
//           centerTitle: true,
//           title: Text(
//             'Chats',
//             style: TextStyle(
//                 color: textblackColor,
//                 fontWeight: FontWeight.w700,
//                 fontSize: Get.width / 17),
//             textAlign: TextAlign.center,
//           ),
//         ),
//         body: controller.isChatListloading
//             ? loader()
//             : Column(
//                 children: [
//                   const SizedBox(
//                     height: 10,
//                   ),
//                   Container(
//                     margin: const EdgeInsets.symmetric(horizontal: 20),
//                     width: Get.width,
//                     child: TextField(
//                       controller: controller.searchController,
//                       keyboardType: TextInputType.emailAddress,
//                       style: TextStyle(
//                           fontSize: Get.width / 28,
//                           color: Colors.black,
//                           fontFamily: font_regular),
//                       decoration: InputDecoration(
//                         focusedBorder: OutlineInputBorder(
//                           borderSide: const BorderSide(
//                               color: Colors.transparent, width: 1),
//                           borderRadius: BorderRadius.circular(15.0),
//                         ),
//                         enabledBorder: OutlineInputBorder(
//                           borderSide: const BorderSide(
//                               color: Colors.transparent, width: 1),
//                           borderRadius: BorderRadius.circular(15.0),
//                         ),
//                         filled: true,
//                         contentPadding:
//                             const EdgeInsets.symmetric(horizontal: 5),
//                         hintStyle: TextStyle(
//                             fontFamily: font_regular,
//                             color: Colors.black38,
//                             fontSize: Get.width / 28),
//                         hintText: "Search",
//                         fillColor: textblackColor.withOpacity(0.04),
//                         prefixIcon: Container(
//                           padding: const EdgeInsets.all(14),
//                           child: Image.asset(
//                             'assets/icons/search.png',
//                             width: 0,
//                             color: Colors.black54,
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                   const SizedBox(
//                     height: 10,
//                   ),
//                   Expanded(
//                       child: ListView.separated(
//                     padding: const EdgeInsets.only(bottom: 50, top: 10),
//                     itemCount: controller.chatList.length,
//                     itemBuilder: (_, index) {
//                       var data = controller.chatList;
//                       var listItem = controller.chatList[index];
//
//                       return InkWell(
//                         onTap: () {
//                           // acountController.getUserData();
//                           // controller.chatId.value = controller.chatList[index]['id'].toString();
//                           Get.to(
//                             () =>
//                                 //  GroupChatScreen(
//                                 //   chatId:
//                                 //       controller.chatList[index]['id'].toString(),
//                                 //   chatType: controller.chatList[index]['chat_type']
//                                 //       .toString(),
//                                 //   chatTypeId: controller.chatList[index]
//                                 //           ['chat_type_id']
//                                 //       .toString(),
//                                 //   from: "chat_list",
//                                 // ),
//                                 group_chat_screen(
//                               chat_id: controller.chatList[index]['id'].toString(),
//                               title: controller.chatList[index]['title'].toString(),
//                               from: "chat_list",
//                               chatType: controller.chatList[index]['chat_type'].toString(),
//                               chatTypeId: controller.chatList[index]['chat_type_id'].toString(),
//                             ),
//                             transition: Transition.downToUp,
//                             duration: const Duration(milliseconds: 500),
//                           );
//                         },
//                         child: Container(
//                           padding: const EdgeInsets.only(
//                               left: 20, right: 20, top: 12, bottom: 12),
//                           child: Column(
//                             children: [
//                               IntrinsicHeight(
//                                 child: Row(
//                                   children: [
//                                     ClipRRect(
//                                         borderRadius:
//                                             BorderRadius.circular(1000),
//                                         child: Image.network(
//                                           listItem['thumbnail'].toString(),
//                                           width: Get.width / 8.8,
//                                           height: Get.width / 8.8,
//                                           fit: BoxFit.cover,
//                                           errorBuilder: (a, b, c) {
//                                             return Image.asset(
//                                                 "assets/img/placeholder_square.png",
//                                                 width: Get.width / 8.8,
//                                                 height: Get.width / 8.8);
//                                           },
//                                         )),
//                                     const SizedBox(
//                                       width: 15,
//                                     ),
//                                     Expanded(
//                                         child: Column(
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       children: [
//                                         Text(
//                                           controller.chatList[index]['title']
//                                               .toString(),
//                                           style: TextStyle(
//                                               fontSize: Get.width / 30,
//                                               fontFamily: font_medium),
//                                         ),
//                                         const SizedBox(
//                                           height: 5,
//                                         ),
//                                         if (listItem["last_message_sender"]
//                                                 .toString()
//                                                 .isNotEmpty ||
//                                             listItem["last_message"]
//                                                 .toString()
//                                                 .isNotEmpty)
//                                           Row(
//                                             children: [
//                                               Text(
//                                                 "${controller.chatList[index]['last_message_sender'].toString()} : ",
//                                                 style: TextStyle(
//                                                     fontSize: Get.width / 40,
//                                                     fontFamily: font_regular,
//                                                     color: textblackColor
//                                                         .withOpacity(0.5)),
//                                               ),
//                                               listItem['last_message']
//                                                           .toString()
//                                                           .isEmpty &&
//                                                       controller.chat[controller
//                                                                       .chat
//                                                                       .length -
//                                                                   1]['type']
//                                                               .toString() ==
//                                                           "image"
//                                                   ? Icon(
//                                                       Icons.image,
//                                                       size: 12,
//                                                       color: Colors.black45,
//                                                     )
//                                                   : SizedBox(
//                                                       width: Get.width / 2.8,
//                                                       child: Text(
//                                                         listItem['last_message']
//                                                             .toString(),
//                                                         maxLines: 1,
//                                                         overflow: TextOverflow
//                                                             .ellipsis,
//                                                         style: TextStyle(
//                                                           fontSize:
//                                                               Get.width / 40,
//                                                           fontFamily:
//                                                               font_regular,
//                                                           color: textblackColor
//                                                               .withOpacity(0.5),
//                                                         ),
//                                                       ),
//                                                     ),
//                                             ],
//                                           ),
//                                       ],
//                                     )),
//                                     Column(
//                                       mainAxisAlignment:
//                                           MainAxisAlignment.start,
//                                       crossAxisAlignment:
//                                           CrossAxisAlignment.start,
//                                       mainAxisSize: MainAxisSize.max,
//                                       children: [
//                                         Text(
//                                           listItem['last_message_on']
//                                               .toString(),
//                                           style: TextStyle(
//                                               fontSize: Get.width / 40,
//                                               color: textblackColor
//                                                   .withOpacity(0.7)),
//                                         ),
//                                       ],
//                                     )
//                                   ],
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                       );
//                     },
//                     separatorBuilder: (BuildContext context, int index) {
//                       return Padding(
//                         padding: EdgeInsets.only(
//                             left: Get.width / 5, right: 10, bottom: 0),
//                         child: Container(
//                           color: primaryColor.withOpacity(0.5),
//                           height: 0.5,
//                         ),
//                       );
//                     },
//                   ))
//                 ],
//               ),
//       );
//     });
//   }
// }
