
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../components/constants.dart';

class StorageInfoCard extends StatelessWidget {
   StorageInfoCard({
    Key? key,
    required this.title,
    required this.svgSrc,
    required this.icon,
    required this.numOfFiles,
  }) : super(key: key);

  final String title, svgSrc,numOfFiles;
  Icon icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: defaultPadding),
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        border: Border.all(width: 2, color: primaryColor.withValues(alpha: 0.15)),
        borderRadius: const BorderRadius.all(
          Radius.circular(defaultPadding),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            height: 20,
            width: 20,
            child: svgSrc.toString().split('.')[1].toString() == "png" ?
            Image.asset(svgSrc)
                :
            SvgPicture.asset(svgSrc,color: title == 'Deposit' ? Colors.green : title == 'Loan' ? Colors.redAccent : Colors.blue,),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: commontextColor),
                  ),
                ],
              ),
            ),
          ),
          icon,
        ],
      ),
    );
  }
}
