import 'package:edutalim/components/constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PlayerErrorWidget extends StatelessWidget{
  const PlayerErrorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
        aspectRatio: 16/9,
        child: Container(
          color: Colors.black87,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              
              // CircularProgressIndicator(color: textwhiteColor,),

              Icon(Icons.error,color: Colors.red.withOpacity(0.8),),
              const SizedBox(height: 10,),
              Text("Error Playing Video",style: TextStyle(color: Colors.amber,fontSize: Get.width/30),)

            ],
          ),
        ),
    );
  }
}
