import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ImagePreviewPage extends StatelessWidget {
  final String imageUrl;

  ImagePreviewPage({required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          // title: Text('Image Preview'),
          ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Displaying the network image
            Image.network(
              imageUrl,
              height: Get.height - 122,
              width: Get.width,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Text('Error loading image');
              },
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
