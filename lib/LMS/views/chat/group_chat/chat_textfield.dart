
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:edutalim/LMS/views/chat/controller/discussionforumcontroller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';



class ChatTextField2 extends StatefulWidget {
  ChatTextField2({super.key, required this.discussionType, required this.discussionId});
  final String discussionType, discussionId;

  @override
  State<ChatTextField2> createState() => _ChatTextField2State();
}

class _ChatTextField2State extends State<ChatTextField2>
    with SingleTickerProviderStateMixin {
  File? _selectedImage;
  DiscussionForumController controller = Get.put(DiscussionForumController());

  late AnimationController _blinkController;

  @override
  void initState() {
    super.initState();
    _blinkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..repeat(reverse: true); // Blinks by fading in and out
  }


  @override
  Widget build(BuildContext context) {
    return GetBuilder<DiscussionForumController>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: textwhiteColor,
          appBar: AppBar(
            backgroundColor: textwhiteColor,
            leadingWidth: Get.width/5,
            leading: TextButton(
              onPressed: () {
                Get.back();
              },
              child: Text("Cancel",style: TextStyle(color: textblackColor),),
            ),
            actions: [
              GestureDetector(
                onTap: () {
                  if(controller.contentController.text!=""||_selectedImage!.path!=""){
                    controller.addDiscussion(widget.discussionType,widget.discussionId,_selectedImage);
                  }else{
                    print("Enter something");
                    toast_info("Input something");
                  }
                },
                child: Container(
                  height: Get.height/25,width: Get.width/5,
                  alignment: Alignment.center,
                  margin: EdgeInsets.symmetric(horizontal: 20),
                  padding: EdgeInsets.symmetric(horizontal: 15,vertical: 5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: primaryColor
                  ),
                  child:
                  controller.isAddDiscussLoading?
                  SizedBox(height: Get.height/50,width: Get.width/25, child: CircularProgressIndicator(color: textwhiteColor,strokeWidth: 2,)):
                    Text("Post",style: TextStyle(
                    color: textwhiteColor
                  ),)
                ),
              )
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(1000),
                        child: Image.asset("assets/img/user_placeholder.png".toString(),width: Get.width/10,height: Get.width/10,),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: TextField(
                            controller: controller.contentController,
                            maxLines: null,
                            keyboardType: TextInputType.multiline,
                            decoration: InputDecoration(
                              hintText: "Enter your message...",
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if (_selectedImage != null)
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.file(
                      _selectedImage!,
                      height: 150,
                      fit: BoxFit.cover,
                    ),
                  ),
              ],
            ),
          ),
          bottomNavigationBar: BottomNavigationBar(
            onTap: (index) {

            },
            items: [
              BottomNavigationBarItem(
                icon: Icon(Icons.photo),
                label: "Gallery",
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.camera_alt),
                label: "Camera",
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    controller.contentController.clear();
    // _blinkController.dispose();
    super.dispose();
  }
}
