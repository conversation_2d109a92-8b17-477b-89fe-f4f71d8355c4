import 'package:edutalim/CRM/api/apis.dart';
import 'package:edutalim/LMS/views/InteractivePlayer/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class Todocontroller extends GetxController {
  bool istodoloading = false;
  bool istggleloading = false;

  var todo;
  List completed = [];
  List pending = [];
  bool isCompleted = false;
  call_todo() async {
    istodoloading = true;

    var data = await CRMApiBaseHandler.fet_todos_api();
    todo = data['data'];
    completed = todo['completed'];
    pending = todo['pending'];
    print(data
        .toString()); // dueleadsdata = filt_dueleadsdata = alldueleadsdata['due_leads'];
    istodoloading = false;
    update();
  }

  mark_complete(Todoid) async {
    istggleloading = true;

    var data = await CRMApiBaseHandler.mark_completed(Todoid);
    if (data['status'] == true) {
      call_todo();
      toast_success(data['message'].toString());
    } else {
      toast_error(data['message'].toString());
    }
    // dueleadsdata = filt_dueleadsdata = alldueleadsdata['due_leads'];
    istggleloading = false;
    update();
  }

  bool isteLoading = false;
  var telecallers;
  String Categoryid = '';
  call_telecallers() async {
    isteLoading = true;

    var data = await CRMApiBaseHandler.fet_leaad_form(Categoryid);
    telecallers = data['data']['telecallers'];
    print(data
        .toString()); // dueleadsdata = filt_dueleadsdata = alldueleadsdata['due_leads'];
    isteLoading = false;
    update();
  }

  TextEditingController titleController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  String? selectedTelecallerId;
  DateTime? selectedDate;

  bool isaddtodoloading = false;

  Future<void> add_todo() async {
    isaddtodoloading = true;
    update();

    final response = await CRMApiBaseHandler.add_todo(
      description: descriptionController.text.trim(),
      title: titleController.text.trim(),
      dueDate: selectedDate != null
          ? DateFormat('d-M-yyyy').format(selectedDate!)
          : '', // or null if API accepts
      telecallerId: selectedTelecallerId ?? '', // or null if API accepts
    );

    isaddtodoloading = false;

    if (response['status'] == true) {
      call_todo();
      toast_success(response['message'].toString());
    } else {
      toast_error(response['message'].toString());
    }

    update();
  }
}
