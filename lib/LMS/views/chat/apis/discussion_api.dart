
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:edutalim/components/constants.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;


class DiscussionApihandler{

  static Future get_discussion_list(course_id)async{
    var  url = Uri.parse('${api}discussion/index?course_id=$course_id&auth_token=${GetStorage().read('auth_token').toString()}');
    log(url.toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        // ApiBasehandler.authError();
        // ApiBasehandler.authError();
      }
    } catch (e) {
      // ApiBasehandler.authError();
    }
  }

  static Future get_sub_discussion_list(course_id,discussion_id)async{
    var  url = Uri.parse('${api}discussion/sub_discussions?course_id=$course_id&discussion_id=$discussion_id&auth_token=${GetStorage().read('auth_token').toString()}');
    log(url.toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        // ApiBasehandler.authError();
        // ApiBasehandler.authError();
      }
    } catch (e) {
      // ApiBasehandler.authError();
    }
  }

  static Future add_discussion({
  required String course_id,
  required String content,
  required String discussion_type,
  required String discussion_id,
  File? filePath
}) async {
    var request = http.MultipartRequest(
        "POST", Uri.parse("${api}discussion/add_content"));
    request.fields["auth_token"] = GetStorage().read('auth_token').toString();
    request.fields["course_id"] = course_id.toString();
    request.fields["content"] = content.toString();
    request.fields["discussion_type"] = discussion_type.toString();
    request.fields["discussion_id"] = discussion_id.toString();
    if (filePath != null) {
      print('a');
      var pic =
      await http.MultipartFile.fromPath("image", filePath.path.toString());
      print('b');
      //add multipart to request
      request.files.add(pic);
      print('c');
    }
    var response = await request.send();
    var responseData = await response.stream.toBytes();
    var responseString = utf8.decode(responseData); // Ensure correct decoding

    print(responseString);
    return jsonDecode(responseString);
  }
}