import 'package:get/get.dart';

class Liveclasscontroller extends GetxController{

  List liveclassdata = [],courses = []  ,jwt = [];
  bool isloading = true,isLoading = true,isJwtLoading = true;

  String jwt_token = '';

  get_jwt_token(meeting_number)async{
    isJwtLoading = true;
    // var data = await ApiBasehandler.generate_jwt(meeting_number);
    // jwt = data;
    // liveclassdata = data['data']['zoom_lists'];
    isJwtLoading = false;
    update();
  }


  get_live_class(date,subject_id)async{
    isloading = true;
    // var data = await ApiBasehandler.live_class(date,subject_id);
    // // liveclassdata = data['data'];
    // liveclassdata = [data['data']];
    // liveclassdata = data['data']['zoom_lists'];
    isloading = false;
    update();
  }

  get_course()async{
    isloading = true;
    // var data = await ApiBasehandler.instructor_enroled_courses();
    // courses = data['data'];
    isLoading = false;
    update();
  }



  @override
  void onInit() {
    super.onInit();
    // get_live_class();
  }

}