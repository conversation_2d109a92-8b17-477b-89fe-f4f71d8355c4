// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
//
// import '../../../components/constants.dart';
// import 'package:edutalim/components/utils.dart';
// import '../../../controllers/mycoursecontroller.dart';
//
// class exams extends StatefulWidget {
//   // String course_id,section_id,lesson_id;
//   // model_exams({required this.course_id,required this.section_id,required this.lesson_id});
//
//   String subject_id,lesson_id;
//   exams({required this.subject_id,required this.lesson_id});
//
//   @override
//   State<exams> createState() => _sessionsState();
// }
//
// class _sessionsState extends State<exams> {
//
//
//   Mycoursecontroller controller = Get.put(Mycoursecontroller());
//   int? sel_index;
//
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     // controller.get_tests(course_id, section_id, lesson_id);
//     // controller.get_exams(widget.subject_id.toString(),widget.lesson_id.toString());
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<Mycoursecontroller>(
//       builder: (controller) {
//         return Scaffold(
//           backgroundColor: textwhiteColor,
//           appBar: AppBar(
//             backgroundColor: textwhiteColor,
//             surfaceTintColor: textwhiteColor,
//             elevation: 5,
//             centerTitle: true,
//             leading:  GestureDetector(
//               onTap: (){
//                 Get.back();
//               },
//               child: Container(
//                 // decoration: BoxDecoration(
//                 //     color: primaryColor,
//                 //     borderRadius: BorderRadius.circular(100)
//                 // ),
//                 margin: EdgeInsets.all(10),
//                 child: Icon(Icons.arrow_back,color: textblackColor,),
//               ),
//             ),
//             title: Container(
//               child: Text("Exam",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w700,fontFamily: font_regular,fontSize: Get.width/24),),
//             ),
//           ),
//           body: Container(
//             height: Get.height,
//             width: Get.width,
//             color: textwhiteColor,
//             child: Column(
//               children: [
//
//                 // controller.isexamsloading?loader():
//                 // controller.exams.isEmpty?
//                 // Container(
//                 //   height: 200,
//                 //   child: Center(child: Text("No Data Found",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.black,fontSize: 16),)),
//                 // ):
//                 Expanded(
//                   child: Container(
//                     padding: EdgeInsets.symmetric(horizontal: 15),
//                     child: ListView.builder(
//                       physics: ScrollPhysics(),
//                       padding: EdgeInsets.symmetric(vertical: 20),
//                       shrinkWrap: true,
//                       // itemCount: 6,
//                       itemCount: controller.exams.length,
//                       itemBuilder: (BuildContext context, int index) {
//                         Map data = controller.exams[index];
//                         return GestureDetector(
//                           onTap: (){
//                             // if(data['free'].toString() == 'off'){
//                             //   Get.to(()=> plans(course_id: GetStorage().read('course_id').toString()));
//                             // }
//                             // else{
//                             //   data['exam_link'].toString() == ''?toast_info('Something wrong'):
//                             //   Get.to(()=> webview_page(url: data['exam_link'].toString()));
//                             //  print(data['exam_link'].toString());
//                             // }
//                           },
//                           child: Stack(
//                             children: [
//                               Container(
//                                 margin: EdgeInsets.only(bottom: 20),
//                                 padding: EdgeInsets.symmetric(horizontal: 14,vertical: 15),
//                                 decoration: BoxDecoration(
//                                   color: sel_index == index ? primaryColor.withOpacity(0.2) : textwhiteColor,
//                                   borderRadius: BorderRadius.circular(10),
//                                   border: Border.all(color: sel_index == index ? primaryColor.withOpacity(1) : textblackColor.withOpacity(0.3),width: sel_index == index ? 1.2 : 0.8)
//                                 ),
//                                 child: Row(
//                                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                   children: [
//                                     Container(
//                                       width: Get.width/1.8,
//                                       // color: Colors.yellow,
//                                       // child: Text("Biological Classification ${index + 1 }",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w600,fontSize: Get.width/24),),
//                                       child: Text(data['title'].toString(),style: TextStyle(color: textblackColor,fontWeight: FontWeight.w600,fontSize: Get.width/24),),
//                                     ),
//
//                                     // Container(
//                                     //   decoration: BoxDecoration(
//                                     //     border: Border.all(color: sel_index == index ? primaryColor : Colors.black38,width: 2),
//                                     //     borderRadius: BorderRadius.circular(1000),
//                                     //   ),
//                                     //   padding: EdgeInsets.all(2),
//                                     //   child: Icon(Icons.circle,color: sel_index == index ? primaryColor : Colors.black38,size: 15,),
//                                     // ),
//                                     // data['free'].toString() == 'off'?Icon(Icons.lock,color: textblackColor.withOpacity(0.2),size:30,):
//                                     Icon(Icons.keyboard_arrow_right_outlined,color: textblackColor.withOpacity(0.2),),
//
//                                   ],
//                                 ),
//                               ),
//
//                             ],
//                           ),
//                         );
//                       },
//                     ),
//                   ),
//                 ),
//
//               ],
//             ),
//           ),
//           // floatingActionButton: FloatingActionButton(
//           //   shape: RoundedRectangleBorder(
//           //     borderRadius: BorderRadius.circular(1000),
//           //   ),
//           //   backgroundColor: primaryColor,
//           //   onPressed: (){
//           //     Get.to(()=> webview_page(url: 'https://trogon.info/tutorpro/skyil/home/<USER>/55/3'));
//           //   },
//           //   child: Icon(Icons.arrow_forward_ios_rounded,color: textwhiteColor,),
//           // ),
//         );
//       }
//     );
//   }
// }
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:edutalim/LMS/controllers/mycoursecontroller.dart';
import 'package:edutalim/LMS/views/tools/web_view_pg.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'leaderboard.dart';

class ExamScreen extends StatefulWidget {
  String course_id;

  ExamScreen({required this.course_id});

  @override
  State<ExamScreen> createState() => _ExamScreenState();
}

class _ExamScreenState extends State<ExamScreen> {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());

  @override
  void initState() {
    controller.getExam(widget.course_id);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: GetBuilder<Mycoursecontroller>(builder: (controller) {
        return Scaffold(
          backgroundColor: textwhiteColor,
          appBar: PreferredSize(
            preferredSize: Size.fromHeight(120),
            child: Column(
              children: [
                AppBar(
                  backgroundColor: textwhiteColor,
                  surfaceTintColor: textwhiteColor,
                  elevation: 5,
                  centerTitle: true,
                  leading: GestureDetector(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      // decoration: BoxDecoration(
                      //     color: primaryColor,
                      //     borderRadius: BorderRadius.circular(100)
                      // ),
                      margin: EdgeInsets.all(10),
                      child: Icon(
                        Icons.arrow_back,
                        color: textblackColor,
                      ),
                    ),
                  ),
                  title: Container(
                    child: Text(
                      "Exam",
                      style: TextStyle(color: textblackColor, fontWeight: FontWeight.w700, fontFamily: font_regular, fontSize: Get.width / 24),
                    ),
                  ),
                ),
                SizedBox(height: 10),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 4)],
                  ),
                  child: TabBar(
                    indicatorColor: Color(0xFFd2a83d), // gold
                    labelColor: Colors.black,
                    unselectedLabelColor: Colors.black45,
                    tabs: [
                      Tab(text: 'Current'),
                      Tab(text: 'Completed'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          body: controller.isExamLoading
              ? loader()
              : TabBarView(
                  children: [
                    buildExamList(controller.exams['not_attempted'], "Current"),
                    buildExamList(controller.exams['attempted'], "Completed"),
                  ],
                ),
        );
      }),
    );
  }

  Widget buildExamList(List exams, type) {
    if (exams.isEmpty) {
      return Container(
        padding: EdgeInsets.all(80),
        alignment: Alignment.topCenter,
        child: Text(
          "No $type Exam's Found......",
          style: TextStyle(color: Colors.black),
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: exams.length,
      itemBuilder: (context, index) {
        final exam = exams[index];

        return ExamCard(exam,type.toString());
      },
    );
  }
}

class ExamCard extends StatelessWidget {
  var data;
  String type;
  ExamCard(this.data,this.type);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: textwhiteColor,
      surfaceTintColor: textwhiteColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12), side: BorderSide(color: Color(0xFFE0E0E0))),
      elevation: 4,
      margin: EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(data['title'].toString(), style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Color(0xFF2F4F4F))),
            SizedBox(height: 8),
            Text(
              data['short_description'].toString(),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: Colors.grey[600]),
            ),
            SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16),
                SizedBox(width: 4),
                Text(data['exam_date'].toString()),
                SizedBox(width: 16),
                Icon(Icons.access_time, size: 16),
                SizedBox(width: 4),
                Text(data['exam_time'].toString()),
              ],
            ),
            SizedBox(height: 12),


            type.toString() == "Current" ?
            Row(
              children: [

                Expanded(
                  child: Container(
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Get.to(() => webview_page(url: data['exam_url'].toString()));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFFD4A437), // golden yellow
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                    child: Text("Attempt Now", style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontFamily: font_semibold, fontSize: Get.width / 32)),
                  ),
                ),

              ],
            ) :
            Row(
              children: [

                Expanded(child: Container()),

                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Get.to(() => webview_page(url: data['exam_url'].toString()));
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFFD4A437), // golden yellow
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                    child: Text("View Result", style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontFamily: font_semibold, fontSize: Get.width / 32)),
                  ),
                ),
                SizedBox(width: 12),


                // Expanded(
                //   child: OutlinedButton(
                //     onPressed: () {
                //       Get.to(() => LeaderBoardScreen(
                //         data: data['name'].toString(),
                //       ));
                //     },
                //     style: OutlinedButton.styleFrom(
                //       foregroundColor: Colors.black,
                //       side: BorderSide(color: Color(0xFFD4A437)),
                //       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                //     ),
                //     child: Text("View Leaderboard", style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 32)),
                //   ),
                // ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
