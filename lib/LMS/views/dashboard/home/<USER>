import 'package:cached_network_image/cached_network_image.dart';
import 'package:edutalim/LMS/views/dashboard/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/homecontroller.dart';

class university_details extends StatefulWidget {
  String university_id;

  university_details({required this.university_id});

  @override
  State<university_details> createState() => _university_detailsState();
}

class _university_detailsState extends State<university_details> {
  Homecontroller controller = Get.put(Homecontroller());

  @override
  void initState() {
    super.initState();
    controller.getUniversityDetails(widget.university_id);
  }

  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    int previewLength = 150;

    return Builder(builder: (context) {
      return GetBuilder<Homecontroller>(builder: (controller) {
        return Scaffold(
          backgroundColor: textwhiteColor,
          body: controller.isUniversityLoading
              ? loader()
              : Container(
            width: Get.width,
            height: Get.height,
            color: textwhiteColor,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [

                        AppBar(toolbarHeight: 0, backgroundColor: appbarwhiteColor, surfaceTintColor: appbarwhiteColor),
                        GestureDetector(
                          onTap: () {},
                          child: Stack(
                            clipBehavior: Clip.none,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                child: Stack(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 0),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(20),
                                        child: CachedNetworkImage(
                                          imageUrl: controller.universitPage['banner_image'].toString(),
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                          errorWidget: (a, b, c) {
                                            return Image.asset('assets/images/placeholder_banner.webp', fit: BoxFit.cover, height: Get.height / 4.1, width: Get.width);
                                          },
                                          height: Get.height / 4.1,
                                        ),
                                      ),
                                    ),
                                    Positioned(
                                      bottom: 0,
                                      left: 0,
                                      right: 0,
                                      child: Container(
                                        height: 150,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(20),
                                          gradient: LinearGradient(
                                              colors: [primarylightColor, primarylightColor.withOpacity(0.3), Colors.transparent],
                                              begin: AlignmentDirectional.bottomCenter,
                                              end: AlignmentDirectional.topCenter,
                                              stops: [0, 0.5, 1]),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              Positioned(
                                top: 20,
                                left: 20,
                                child: GestureDetector(
                                  onTap: () {
                                    Get.back();
                                  },
                                  child: Image.asset('assets/icons/arrow_back.png', width: Get.width / 12, color: textwhiteColor),
                                ),
                              ),
                              Positioned(
                                bottom: 40,
                                left: 20,
                                right: 20,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                      child: Text(
                                        controller.universitPage["title"].toString(),
                                        style: TextStyle(fontSize: Get.width / 20, fontWeight: FontWeight.bold, color: Colors.white, fontFamily: font_semibold),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 20),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 15),
                          width: Get.width,
                          child: Row(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(30),
                                child: CachedNetworkImage(
                                  imageUrl: controller.universitPage['thumbnail'].toString(),
                                  width: Get.width / 9.9,
                                  height: Get.width / 9.9,
                                  fit: BoxFit.cover,
                                  errorWidget: (a, b, c) {
                                    return Image.asset('assets/images/placeholder_square.png', width: Get.width / 9, height: Get.width / 9);
                                  },
                                ),
                              ),
                              SizedBox(width: 10),
                              Expanded(
                                child: Text(controller.universitPage['short_description'].toString(), style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 30, fontFamily: font_semibold)),
                              ),
                            ],
                          ),
                        ),

                        if(controller.universitPage['location'].toString().isNotEmpty && controller.universitPage['location'].toString() != "null")
                          Column(
                            children: [
                              SizedBox(height: 20),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 15),
                                width: Get.width,
                                child: Row(
                                  children: [
                                    const SizedBox(width: 5),
                                    Icon(Icons.location_on_outlined, color: textblackColor, size: Get.width / 15),
                                    SizedBox(width: 10),
                                    Expanded(
                                      child: Text(controller.universitPage['location'].toString(), style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 30, fontFamily: font_semibold)),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        SizedBox(height: 30),

                        if(controller.universitPage['description'].toString().isNotEmpty && controller.universitPage['description'].toString() != "null")
                        Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 15),
                              child: Text("About", style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 24, fontFamily: font_bold)),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: 5),
                                  GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        isExpanded = !isExpanded;
                                      });
                                    },
                                    child: Html(
                                      data: isExpanded
                                          ? controller.universitPage['description'] + "..... <b style = 'font-size: 13px; color : #BC8017'>View less</b>" // Full description when expanded
                                          : controller.universitPage['description'].length > previewLength
                                          ? controller.universitPage['description'].substring(0, previewLength) + "..." + "<b style = 'font-size: 13px; color : #BC8017'>View more</b>"
                                          : controller.universitPage['description'], // Show limited content
                                      shrinkWrap: true,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          ],
                        ),


                        SizedBox(height: 20),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Text("Courses", style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 24, fontFamily: font_bold)),
                        ),
                        const SizedBox(height: 10),
                        ListView.builder(
                          physics: ScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: controller.universitPage['categories'].length,
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          itemBuilder: (BuildContext context, int index) {
                            var data = controller.universitPage['categories'][index];
                            return GestureDetector(
                                onTap: () {
                                  Get.to(() => category_details(category_id: data['id'].toString()));
                                },
                                child: CategoryList(data));
                          },
                        ),
                        SizedBox(height: 15),

                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      });
    });
  }
}

class CategoryList extends StatelessWidget {
  var data;

  CategoryList(this.data);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 8, offset: Offset(0, 4))],
      ),
      width: Get.width,
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: data['thumbnail'].toString(),
                    width: 120,
                    height: 90,
                    fit: BoxFit.cover,
                    errorWidget: (_, __, ___) {
                      return Image.asset("assets/images/placeholder_square.png", width: 90, height: 90);
                    },
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(data['title'].toString(), style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                const SizedBox(height: 3),
                Text(data['short_description'].toString(), style: TextStyle(fontSize: Get.width / 34, color: textblackColor), maxLines: 2, overflow: TextOverflow.ellipsis),
                SizedBox(height: 10),
                common_button_gradient_secondary(
                  onPressed: null,
                  borderRadius: BorderRadius.circular(8),
                  width: Get.width * .9,
                  height: 40,
                  child: Text("View Details", style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontFamily: font_regular, fontSize: Get.width / 32), textAlign: TextAlign.center),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}