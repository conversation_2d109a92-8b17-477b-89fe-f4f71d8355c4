import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

import '../../components/constants.dart';

class CRMApiBaseHandler {
  static fet_duelaads_api() async {
    var url = Uri.parse('${api}crm/leads/due-leads');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        "content-type": "application/json"
      },
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        print("helooooooooo-------------------------");
        print(response.statusCode.toString());
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_team_memberss(team_id) async {
    var url = Uri.parse('${api}crm/teams/team_members/$team_id');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        "content-type": "application/json"
      },
    );
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_teams() async {
    var url = Uri.parse('${api}crm/teams');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        "content-type": "application/json"
      },
    );
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_leaad_form(category_id) async {
    var url = Uri.parse('${api}crm/leads/lead-form?category_id=$category_id');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        "content-type": "application/json"
      },
    );
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_add_edit_lead(
    name,
    code,
    phone,
    whatsapp,
    email,
    telecaller_id,
    interest_id,
    lead_status_id,
    lead_source_id,
    followup_date,
    remarks,
    category_id,
    subcat_id,
    course_id,
    lead_id,
    action,
  ) async {
    final json = {
      "name": name.toString(),
      "code": code.toString(),
      "phone": phone.toString(),
      "secondary_phone": whatsapp.toString(),
      "email": email.toString(),
      "telecaller_id": telecaller_id.toString(),
      "interest_status": interest_id.toString(),
      "lead_status_id": lead_status_id.toString(),
      "lead_source_id": lead_source_id.toString(),
      "followup_date": followup_date.toString(),
      "remarks": remarks.toString(),
      "category_id": category_id.toString(),
      "subcategory_id": subcat_id.toString(),
      "course_id": course_id.toString(),
      "register_from": Platform.isAndroid ? "Android" : "iOS",
    };

    var url;

    if (action == "edit") {
      url = Uri.parse('${api}crm/leads/update/$lead_id');
    } else if (action == "update") {
      url = Uri.parse('${api}crm/leads/update_status/$lead_id');
    } else {
      url = Uri.parse('${api}crm/leads/add');
    }

    http.Response response = await http.post(
      url,
      body: json,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        // "content-type": "application/json"
      },
    );
    print(url);
    try {
      if (response.statusCode == 200) {
        String data = response.body;
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_laad_history_api(lead_id) async {
    var url = Uri.parse('${api}crm/leads/history/$lead_id');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        "content-type": "application/json"
      },
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        print("helooooooooo-------------------------");
        print(response.statusCode.toString());
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_laads_api(status_id) async {
    var url = Uri.parse(
        '${api}crm/leads?lead_status_id=$status_id&lead_source_id=&country_id=&telecaller_id=');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        "content-type": "application/json"
      },
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        print("helooooooooo-------------------------");
        print(response.statusCode.toString());
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_homepage_data() async {
    var url = Uri.parse(
        '${api}crm/home?auth_token=${GetStorage().read('auth_token')}');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        "content-type": "application/json"
      },
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        print("helooooooooo-------------------------");
        print(response.statusCode.toString());
      }
    } catch (e) {
      return 'failed';
    }
  }

  // ---------------------------------------------------------------------------------------

  static fet_delete_lead(lead_id) async {
    var url = Uri.parse(
        '${baseurl}lead/delete?lead_id=$lead_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_telecaller_leads(
      member_id,
      f_date,
      t_date,
      status_id,
      country_id,
      university_id,
      source_id,
      team_id,
      course_id,
      telecaller_id,
      branch_id,
      interest_status) async {
    // var url = Uri.parse('${baseurl}lead/index?branch_id=$branch_id&telecaller_id=$telecaller_id&course_id=$course_id&member_id=$member_id&from_date=$f_date&to_date=$t_date&status_id=$status_id&country_id=$country_id&university_id=$university_id&source_id=$source_id&team_id=$team_id&interest_status=$interest_status&auth_token=${GetStorage().read('auth_token')}');
    var url = Uri.parse(
        '${baseurl}tele_caller_leads/index?branch_id=$branch_id&telecaller_id=$telecaller_id&course_id=$course_id&member_id=$member_id&from_date=$f_date&to_date=$t_date&status_id=$status_id&country_id=$country_id&university_id=$university_id&source_id=$source_id&team_id=$team_id&interest_status=$interest_status&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_transfer_lead(lead_id, branch_id, from_branch_id) async {
    var url = Uri.parse(
        '${baseurl}lead/transfer_lead?lead_id=$lead_id&branch_id=$branch_id&from_branch_id=$from_branch_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_filtered_branches(branch_id) async {
    var url = Uri.parse(
        '${baseurl}branches/branches?branch_id=$branch_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_batches(branch_id, course_id) async {
    var url = Uri.parse(
        '${baseurl}batch/index?branch_id=$branch_id&course_id=$course_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_enrolled_courses(user_id) async {
    var url = Uri.parse(
        '${baseurl}course/enrolled_courses?user_id=$user_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_telecaller(branch_id) async {
    var url = Uri.parse(
        '${baseurl}academic_counselor/index?branch_id=$branch_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_enroll_course(branch_id, course_id, user_id, batch_id) async {
    var url = Uri.parse(
        '${baseurl}course/enroll_course?branch_id=$branch_id&course_id=$course_id&batch_id=$batch_id&user_id=$user_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_branches() async {
    var url = Uri.parse(
        '${baseurl}branches/index?auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_courses() async {
    var url = Uri.parse(
        '${baseurl}course/index?auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_convert_lead(lead_id, branch_id, course_id, batch_id) async {
    var url = Uri.parse(
        '${baseurl}lead/convert_lead?lead_id=$lead_id&branch_id=$branch_id&course_id=$course_id&batch_id=$batch_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_my_team_leads(member_id) async {
    var url = Uri.parse(
        '${baseurl}lead/leads_by_user?member_id=$member_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_my_team() async {
    var url = Uri.parse(
        '${baseurl}team/my_team?auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_edit_user(name, phone, code, imagefile) async {
    var request =
        http.MultipartRequest("POST", Uri.parse('${baseurl}user/edit_user'));
    //add text fields
    request.fields["auth_token"] = GetStorage().read('auth_token').toString();
    request.fields["name"] = name.toString();
    request.fields["phone"] = phone.toString();
    request.fields["code"] = code.toString();
    request.fields["user_id"] = GetStorage().read('user_id').toString();

    if (imagefile != null) {
      var pic = await http.MultipartFile.fromPath(
          "profile_picture", imagefile.path.toString()); //file.path
      //add multipart to request
      request.files.add(pic);
    }

    var response = await request.send();
    //Get the response from the server
    var responseData = await response.stream.toBytes();
    var responseString = String.fromCharCodes(responseData);

    print(responseString);
    return jsonDecode(responseString);
  }

  static fet_userdata() async {
    var url = Uri.parse(
        '${baseurl}user/userdata?auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_add_user(name, phone, code, email, role_id, team_id, is_team_lead,
      is_team_manager, password, imagefile, user_id, action) async {
    var request;

    if (action == 'edit') {
      request =
          http.MultipartRequest("POST", Uri.parse('${baseurl}user/edit_user'));

      request.fields["user_id"] = user_id;
    } else {
      request =
          http.MultipartRequest("POST", Uri.parse('${baseurl}user/add_user'));

      request.fields["role_id"] = role_id.toString();
      request.fields["is_team_lead"] = is_team_lead.toString();
      request.fields["is_team_manager"] = is_team_manager.toString();
      request.fields["password"] = password.toString();
    }

    //add text fields
    request.fields["auth_token"] = GetStorage().read('auth_token').toString();
    request.fields["name"] = name.toString();
    request.fields["phone"] = phone.toString();
    request.fields["code"] = code.toString();
    request.fields["email"] = email.toString();
    if (role_id.toString() == '6') {
      request.fields["team_id"] = team_id.toString();
    }

    if (imagefile != null) {
      var pic = await http.MultipartFile.fromPath(
          "profile_picture", imagefile.path.toString()); //file.path
      //add multipart to request
      request.files.add(pic);
    }

    var response = await request.send();
    //Get the response from the server
    var responseData = await response.stream.toBytes();
    var responseString = String.fromCharCodes(responseData);

    print(responseString);
    return jsonDecode(responseString);
  }

  static fet_users(role_id, is_tl, is_tm) async {
    var url = Uri.parse(baseurl +
        'user/users_by_roleid?role_id=$role_id&is_team_lead=$is_tl&is_team_manager=$is_tm&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_enrollment(member_id, f_date, t_date, status_id, country_id,
      university_id, source_id, team_id) async {
    var url = Uri.parse(
        '${baseurl}enrollments/index?member_id=$member_id&from_date=$f_date&to_date=$t_date&status_id=$status_id&country_id=$country_id&university_id=$university_id&source_id=$source_id&team_id=$team_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_call_details(member_id, f_date, t_date) async {
    var url = Uri.parse(
        '${baseurl}app/call_history?member_id=$member_id&from_date=$f_date&to_date=$t_date&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_cadidates_new(branch_id, member_id, f_date, t_date, status_id,
      country_id, university_id, source_id, team_id) async {
    // var url = Uri.parse('${baseurl}candidate/index?member_id=$member_id&from_date=$f_date&to_date=$t_date&status_id=$status_id&country_id=$country_id&university_id=$university_id&source_id=$source_id&team_id=$team_id&auth_token=${GetStorage().read('auth_token')}');
    var url = Uri.parse(
        '${baseurl}candidate/students?branch_id=$branch_id&member_id=$member_id&from_date=$f_date&to_date=$t_date&status_id=$status_id&country_id=$country_id&university_id=$university_id&source_id=$source_id&team_id=$team_id&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_call_log(call_data) async {
    final json = {
      'call_data': jsonEncode(call_data),
      'auth_token': GetStorage().read('auth_token')
    };

    var url = Uri.parse('${baseurl}app/call_log');
    http.Response response = await http.post(
      url,
      body: json,
    );
    print(url);
    try {
      if (response.statusCode == 200) {
        String data = response.body;
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_create_invoice(courseid, feetypeid, candid_id, ttlamt, payableamt,
      invdate, duedate, remark) async {
    print("called-------------uuu-----");
    var url = Uri.parse(
        '${baseurl}invoice/create_invoice?course_id=$courseid&fee_type=$feetypeid&canditate_id=$candid_id&total_amount=$ttlamt&payable_amount=$payableamt&inv_date=$invdate&due_date=$duedate&remarks=$remark&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_form_data(course_id) async {
    print("called-------------uuu-----");
    var url = Uri.parse(baseurl +
        'invoice/view_course_canditates?course_id=$course_id&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_collect_payment(inv_id, paid_amount, payment_date, payment_type,
      reference_no, remark) async {
    print("called-------------uuu-----");
    var url = Uri.parse(baseurl +
        'invoice/add_payment?invoice_id=$inv_id&paid_amount=$paid_amount&payment_date=$payment_date&payment_type=$payment_type&reference_no=$reference_no&remark=$remark&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_peyments(fdate, todate) async {
    print("called-------------uuu-----");
    var url = Uri.parse(baseurl +
        'invoice/list_invoice?from=$fdate&to=$todate&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static toggle_role(role_text) async {
    print("called-------------uuu-----");
    var url = Uri.parse(
        '${baseurl}user/switch_role?current_role=$role_text&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_candidates(member_id) async {
    var url = Uri.parse(baseurl +
        'candidate/index?member_id=$member_id&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_lead_history(lid) async {
    var url = Uri.parse(
        '${baseurl}lead/history?lead_id=$lid&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_followups(member_id, f_date, t_date, country_id, university_id,
      source_id, team_id) async {
    var url = Uri.parse(baseurl +
        'lead/followups?member_id=$member_id&from_date=$f_date&to_date=$t_date&country_id=$country_id&university_id=$university_id&source_id=$source_id&team_id=$team_id&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_leads(
      member_id,
      f_date,
      t_date,
      status_id,
      country_id,
      university_id,
      source_id,
      team_id,
      course_id,
      telecaller_id,
      branch_id,
      interest_status) async {
    // var url = Uri.parse('${baseurl}lead/index?branch_id=$branch_id&telecaller_id=$telecaller_id&course_id=$course_id&member_id=$member_id&from_date=$f_date&to_date=$t_date&status_id=$status_id&country_id=$country_id&university_id=$university_id&source_id=$source_id&team_id=$team_id&interest_status=$interest_status&auth_token=${GetStorage().read('auth_token')}');
    var url = Uri.parse(
        '${baseurl}lead/index?branch_id=$branch_id&telecaller_id=$telecaller_id&course_id=$course_id&member_id=$member_id&from_date=$f_date&to_date=$t_date&status_id=$status_id&country_id=$country_id&university_id=$university_id&source_id=$source_id&team_id=$team_id&interest_status=$interest_status&auth_token=${GetStorage().read('auth_token')}');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_member_details(memb_id) async {
    var url = Uri.parse(baseurl +
        'member/details?member_id=$memb_id&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_members(team_id) async {
    var url = Uri.parse(baseurl +
        'member/index?team_id=$team_id&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static toggle_team_status(team_id) async {
    print("called-------------uuu-----");
    var url = Uri.parse(baseurl +
        'team/toggle_status?team_id=$team_id&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_report(f_date, to_date, status, team, source) async {
    var url = Uri.parse(baseurl +
        'report/index?from_date=$f_date&to_date=$to_date&status=$status&team=$team&source=$source&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static assign_documentor(documentation_id, docmentor_id) async {
    var url = Uri.parse(baseurl +
        'assign_documentation?documentation_id=$documentation_id&documentor_id=$docmentor_id&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_documentations() async {
    var url = Uri.parse(baseurl +
        'documentations?auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_documentor_details(doc_id) async {
    var url = Uri.parse(baseurl +
        'documentor_details?documentor_id=$doc_id&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_documentors() async {
    var url = Uri.parse(baseurl +
        'documentor_list?auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static update_documentataion_status(
      doc_id, doc_status, follow_date, remark) async {
    var url = Uri.parse(baseurl +
        'documentation_status_change?documentation_id=$doc_id&documentation_status=$doc_status&follow_date=$follow_date&remarks=$remark&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_documnet_status() async {
    var url = Uri.parse(baseurl +
        'document_status?auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_documentation_by_docmntor(doc_status) async {
    var url = Uri.parse(baseurl +
        'documentations_by_documentor?document_status=$doc_status&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }
// -------------------documentation END----------------------

  static fet_visa_types() async {
    var url = Uri.parse(baseurl +
        'visa_type?auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_followups_by_cre(cre_id, search, from_date, to_date) async {
    var url = Uri.parse(
        '${baseurl}cre_today_followups?search=$search&cre_id=$cre_id&auth_token=${GetStorage().read('auth_token')}&from_date=$from_date&to_date=$to_date');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_country_list() async {
    var url = Uri.parse(baseurl +
        'countries?auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_lead_status() async {
    var url = Uri.parse(baseurl +
        'lead_status?auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_app_version() async {
    var url = Uri.parse(
        'https://project.trogon.info/aimbridge/api/app/app_version?auth_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************.6u8C9gFT43d-qnurK-ErY9NrKahAGBXUWzyA5CZgEQ4');
    print(url);
    Map<String, String> requestHeaders = {
      'Content-type': 'application/json; charset=UTF-8',
      'Accept': 'application/json',
      'auth_token': GetStorage().read('auth_token').toString(),
    };
    http.Response response = await http.get(
      url,
      headers: requestHeaders,
    );

    print("heloooo-------");
    print(response.statusCode.toString() + "-------");

    try {
      if (response.statusCode == 200) {
        String data = response.body;
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static update_candidatestatus(sel_date, lid, l_status, remark) async {
    String fnlDate = "";
    if (sel_date.toString() == "" || sel_date.toString() == "null") {
      fnlDate = DateTime.now().year.toString() +
          "-" +
          DateTime.now().month.toString() +
          "-" +
          DateTime.now().day.toString();
    } else {
      fnlDate = sel_date;
    }

    // else{
    //   DateTime parseDate = DateFormat("yyyy-MM-dd").parse(sel_date);
    //   var inputDate = DateTime.parse(parseDate.toString());
    //   var outputFormat = DateFormat('yyyy-MM-dd');
    //   var outputDate = outputFormat.format(inputDate);
    //   print("out is-------------------------"+outputDate);
    //   fnlDate = outputDate;
    // }

    var url = Uri.parse(baseurl +
        'candidate_status_change?candidate_id=$lid&candidate_status=$l_status&follow_date=$fnlDate&remarks=$remark&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_status() async {
    var url = Uri.parse(baseurl +
        'candidate_status?auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_candidate_history(cid) async {
    var url = Uri.parse(baseurl +
        'candidate_history?candidate_id=$cid&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_followup_by(creid, status, search, date) async {
    var crid;
    if (creid.toString() == "") {
      crid = GetStorage().read('user_id').toString();
    } else {
      crid = creid.toString();
    }

    var url = Uri.parse(baseurl +
        'cre_followups?candidate_status=$status&search=$search&followup_date=$date&cre_id=$crid&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_sector_list() async {
    var url = Uri.parse(baseurl + 'sector_list');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_lead_source() async {
    var url = Uri.parse(baseurl + 'lead_source');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static update_leadstatus(
      sel_date, lid, l_status, remark, interest_status) async {
    print("api section---------------------------------------");

    String fnlDate = "";
    if (sel_date.toString() == "" || sel_date.toString() == "null") {
      fnlDate = DateTime.now().year.toString() +
          "-" +
          DateTime.now().month.toString() +
          "-" +
          DateTime.now().day.toString();
    } else {
      fnlDate = sel_date;
    }

    // else{
    //
    //   DateTime parseDate = DateFormat("yyyy-MM-dd").parse(sel_date);
    //   var inputDate = DateTime.parse(parseDate.toString());
    //   var outputFormat = DateFormat('yyyy-MM-dd');
    //   var outputDate = outputFormat.format(inputDate);
    //   print("out is-------------------------"+outputDate);
    //   fnlDate = outputDate;
    //
    // }

    print('-------------------date check---------' + fnlDate.toString());

    var url = Uri.parse(baseurl +
        'cre_change_lead_status?lead_id=$lid&lead_status=$l_status&follow_date=$fnlDate&remarks=$remark&interest_status=$interest_status&auth_token=' +
        GetStorage().read('auth_token').toString());
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

// static fet_leadsby_type(search,type,cre_id,from_date,to_date)async{
//   // var url = Uri.parse(baseurl+'cre_leads_by_status?search=$search&followup_date=$date&cre_id=$cre_id&lead_status=$type&auth_token='+GetStorage().read('auth_token').toString());
//   var url = Uri.parse(baseurl+'cre_leads_by_status?search=$search&from_date=$from_date&to_date=$to_date&cre_id=$cre_id&lead_status=$type&auth_token='+GetStorage().read('auth_token').toString());
//   http.Response response = await http.get(url);
//   print(url);
//   try {
//     if (response.statusCode == 200) {
//       String data = response.body;
//       var decodedData = jsonDecode(data);
//       return [decodedData];
//     } else {
//       return 'failed';
//     }
//   } catch (e) {
//     return 'failed';
//   }
// }

  static verify_otp(userid, otp, phone, code) async {
    var url = Uri.parse(
        '${baseurl}login/verify_otp?code=$code&phone=$phone&otp=$otp');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static login(code, phone) async {
    var url = Uri.parse(baseurl + 'login/index?code=$code&phone=$phone');
    http.Response response = await http.get(
      url,
      headers: {"content-type": "application/json"},
    );
    log(url.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return [decodedData];
      } else {
        return 'failed';
      }
    } catch (e) {
      return 'failed';
    }
  }

  static fet_todos_api() async {
    var url = Uri.parse('${api}crm/todos');
    print(url);
    http.Response response = await http.get(
      url,
      headers: {
        "Authorization": "Bearer " + GetStorage().read('auth_token').toString(),
        "content-type": "application/json"
      },
    );
    log(url.toString());
    log(response.statusCode.toString());
    try {
      if (response.statusCode == 200) {
        String data = utf8.decode(response.bodyBytes);
        var decodedData = jsonDecode(data);
        return decodedData;
      } else {
        print("helooooooooo-------------------------");
        print(response.statusCode.toString());
      }
    } catch (e) {
      return 'failed';
    }
  }

  static Future<dynamic> mark_completed(String todo_id) async {
    final authToken = GetStorage().read('auth_token');

    var url = Uri.parse('${api}crm/todos/toggle-complete/$todo_id');
    log(url.toString());

    try {
      http.Response response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $authToken',
          'Content-Type': 'application/json',
        },
      );

      print("Request URL: $url");
      print("Status Code: ${response.statusCode}");
      print("Response Body: ${response.body}");

      if (response.statusCode == 200) {
        var decodedData = jsonDecode(response.body);
        return decodedData; // ✅ return directly
      } else {
        return {'status': false, 'message': 'Request failed'};
      }
    } catch (e) {
      print("Error: $e");
      return {'status': false, 'message': 'An error occurred'};
    }
  }

  // static Future<dynamic> add_todo({
  //   required String telecallerId,
  //   required String dueDate,
  //   required String description,
  //   required String title,
  // }) async {
  //   final String? authToken = GetStorage().read('auth_token');

  //   final Map<String, dynamic> body = {
  //     'telecaller_id': telecallerId,
  //     'due_date': dueDate,
  //     'description': description,
  //     'title': title,
  //   };

  //   var url = Uri.parse('${api}crm/todos/add');

  //   try {
  //     http.Response response = await http.post(
  //       url,
  //       headers: {
  //         'Authorization': 'Bearer $authToken',
  //         'Content-Type': 'application/json',
  //       },
  //       body: jsonEncode(body), // ✅ This was missing
  //     );

  //     print("POST: $url");
  //     print("BODY: $body");
  //     print("STATUS: ${response.statusCode}");

  //     if (response.statusCode == 200) {
  //       var decodedData = jsonDecode(response.body);
  //       return decodedData;
  //     } else {
  //       print('Failed response: ${response.body}');
  //       return {'status': false, 'message': 'Request failed'};
  //     }
  //   } catch (e) {
  //     print('Exception: $e');
  //     return {'status': false, 'message': 'Exception: $e'};
  //   }
  // }

  static Future<dynamic> add_todo({
    required String telecallerId,
    required String dueDate,
    required String description,
    required String title,
  }) async {
    final String? authToken = GetStorage().read('auth_token');
    var url = Uri.parse('${api}crm/todos/add');

    var request = http.MultipartRequest('POST', url)
      ..headers['Authorization'] = 'Bearer $authToken'
      ..fields['due_date'] = dueDate
      ..fields['description'] = description
      ..fields['title'] = title;

    // ✅ Only include telecaller_id if not empty
    if (telecallerId.isNotEmpty) {
      request.fields['telecaller_id'] = telecallerId;
    }

    try {
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      print("POST: $url");
      print("BODY: ${request.fields}");
      print("STATUS: ${response.statusCode}");

      if (response.statusCode == 200) {
        var decodedData = jsonDecode(response.body);
        return decodedData;
      } else {
        print('Failed response: ${response.body}');
        return {'status': false, 'message': 'Request failed'};
      }
    } catch (e) {
      print('Exception: $e');
      return {'status': false, 'message': 'Exception: $e'};
    }
  }
}
