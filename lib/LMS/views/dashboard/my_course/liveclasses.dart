import 'package:edutalim/LMS/controllers/mycoursecontroller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';

class live_class extends StatefulWidget {
  String course_id;

  live_class({required this.course_id});

  @override
  State<live_class> createState() => _live_classState();
}

class _live_classState extends State<live_class> {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());

  @override
  void initState() {
    controller.get_live_classes(widget.course_id.toString());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Mycoursecontroller>(builder: (controller) {
      return Scaffold(
        backgroundColor: textwhiteColor,
        appBar: AppBar(
          backgroundColor: textwhiteColor,
          surfaceTintColor: textwhiteColor,
          leading: Icon<PERSON>utton(
            onPressed: () {
              Get.back();
            },
            icon: Container(padding: const EdgeInsets.all(8), child: const Icon(Icons.arrow_back_ios)),
          ),
          title: Text("Live", style: TextStyle(fontFamily: font_regular, color: textblackColor, fontWeight: FontWeight.bold, fontSize: Get.width / 24)),
          centerTitle: true,
        ),
        body: Container(
          height: Get.height,
          width: Get.width,
          color: primaryColor.withOpacity(0.05),
          child: controller.isLiveClassesLoading
              ? loader()
              : SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 15),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(height: 8, width: 8, decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.red)),
                            SizedBox(width: 5),
                            Text("Current Live Classes", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 24, color: textblackColor)),
                          ],
                        ),
                      ),
                      const SizedBox(height: 15),
                      controller.liveClassData['live_now'].isEmpty
                          ? const SizedBox(
                              height: 200,
                              child: Center(child: Text("No Active Live Class", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 13))),
                            )
                          : Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10),
                              child: ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                padding: const EdgeInsets.all(0),
                                shrinkWrap: true,
                                itemCount: controller.liveClassData['live_now'].length,
                                itemBuilder: (BuildContext context, int index) {
                                  var data = controller.liveClassData['live_now'][index];
                                  return GestureDetector(
                                    onTap: () {
                                      if (data['free'].toString() == '0' || data['free'].toString() == 'off') {
                                      } else {
                                        controller.create_jwt(data);
                                      }
                                    },
                                    child: Stack(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(color: secondaryColor.withOpacity(0.05), borderRadius: const BorderRadius.all(Radius.circular(20)), border: Border.all(color: secondaryColor)),
                                          width: Get.width,
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.center,
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    ClipRRect(
                                                      borderRadius: BorderRadius.circular(10),
                                                      child: Container(
                                                        height: Get.width/6,
                                                        width: Get.width/6,
                                                        constraints: BoxConstraints(maxWidth: Get.width / 6, maxHeight: Get.width / 6),
                                                        child: Image.network(
                                                            data['thumbnail'].toString(), fit: BoxFit.cover,
                                                            errorBuilder: (_,__,___){
                                                              return Image.asset("assets/logo/logo_icon.png");
                                                            },
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(width: 10),
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                        children: [
                                                          Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              SizedBox(
                                                                width: Get.width * .4,
                                                                child: Text("${data["title"]}", style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 25), maxLines: 1),
                                                              ),
                                                              SizedBox(height: 5),
                                                              if (data['instructor_name'].toString().isNotEmpty && data['instructor_name'].toString() != "null")
                                                                Row(
                                                                  children: [
                                                                    ClipRRect(borderRadius: const BorderRadius.all(Radius.circular(500)), child: Image.asset('assets/temp/active.png', width: Get.width * .11)),
                                                                    Container(
                                                                      width: Get.width * .3,
                                                                      padding: const EdgeInsets.symmetric(horizontal: 10),
                                                                      child: Text(data['instructor_name'].toString(),
                                                                          style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 27), maxLines: 1),
                                                                    ),
                                                                  ],
                                                                ),

                                                              Container(
                                                                padding: const EdgeInsets.symmetric(horizontal: 5),
                                                                child: Text(data['course_name'].toString(),
                                                                    style: TextStyle(color: textblackColor, fontWeight: FontWeight.w500, fontSize: Get.width / 27), maxLines: 1),
                                                              ),

                                                              Container(
                                                                padding: const EdgeInsets.symmetric(horizontal: 5),
                                                                child: Text(data['date_time'].toString(),
                                                                    style: TextStyle(color: textblackColor, fontWeight: FontWeight.w500, fontSize: Get.width / 27), maxLines: 1),
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(height: 10),
                                                          Container(
                                                            decoration: BoxDecoration(color: primaryColor, borderRadius: const BorderRadius.all(Radius.circular(5))),
                                                            padding: const EdgeInsets.all(10),
                                                            child: Center(
                                                              child: Text(data["button_label"]?.toString() ?? "Join",
                                                                  style: TextStyle(fontWeight: FontWeight.w600, overflow: TextOverflow.ellipsis, color: textwhiteColor), maxLines: 2, textAlign: TextAlign.center),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        if (data['free'].toString() == '0' || data['free'].toString() == 'off')
                                          Positioned(
                                            right: 150,
                                            top: 30,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.transparent,
                                                borderRadius: BorderRadius.only(topLeft: Radius.circular(10), bottomLeft: Radius.circular(10), topRight: Radius.circular(15), bottomRight: Radius.circular(0)),
                                              ),
                                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                                              child: Icon(Icons.lock, color: textblackColor.withOpacity(.3), size: 50),
                                            ),
                                          ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                      const SizedBox(
                        height: 40,
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 15),
                        child: Text("Upcoming Live Classes", style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 24, color: textblackColor)),
                      ),
                      const SizedBox(height: 15),
                      controller.liveClassData['upcoming'].isEmpty
                          ? const SizedBox(
                              height: 200,
                              child: Center(child: Text("No Upcoming Live Classes", style: TextStyle(fontFamily: 'poppins_regular', color: Colors.black, fontSize: 13))),
                            )
                          : Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10),
                              child: GridView.builder(
                                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(childAspectRatio: 2 / 2.1, crossAxisCount: 2, mainAxisSpacing: 10, crossAxisSpacing: 10),
                                physics: const NeverScrollableScrollPhysics(),
                                padding: const EdgeInsets.all(0),
                                shrinkWrap: true,
                                itemCount: controller.liveClassData['upcoming'].length,
                                itemBuilder: (BuildContext context, int index) {
                                  Map data = controller.liveClassData['upcoming'][index];
                                  return GestureDetector(
                                    onTap: () {},
                                    child: Column(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                              color: textwhiteColor,
                                              borderRadius: const BorderRadius.all(Radius.circular(12)),
                                              boxShadow: [BoxShadow(color: textblackColor.withOpacity(.06), blurRadius: 3, offset: Offset(0, 2))]),
                                          padding: const EdgeInsets.symmetric(vertical: 10),
                                          width: Get.width / 2.1,
                                          child: Container(
                                            padding: const EdgeInsets.only(left: 0),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 10),
                                                  child: Row(
                                                    children: [
                                                      Container(
                                                        color: textwhiteColor,
                                                        child: ClipRRect(borderRadius: const BorderRadius.all(Radius.circular(500)), child: Image.asset('assets/logo/logo.png', width: Get.width * 0.08)),
                                                      ),
                                                      Container(
                                                        width: Get.width * .3,
                                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                                        child: Text(data['instructor_name'] ?? appName, style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 27), maxLines: 1),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Container(padding: const EdgeInsets.symmetric(vertical: 5), child: Divider(color: textblackColor.withOpacity(.09))),
                                                Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 10),
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      const SizedBox(height: 3),
                                                      SizedBox(
                                                        width: Get.width * .6,
                                                        child: Text(data['title'].toString(),
                                                            style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w400, fontSize: Get.width / 27), maxLines: 1, overflow: TextOverflow.ellipsis),
                                                      ),
                                                      const SizedBox(height: 5),
                                                      SizedBox(
                                                        width: Get.width * .6,
                                                        child: Text(data['time'].toString(), style: TextStyle(color: Colors.black54, fontWeight: FontWeight.w400, fontSize: Get.width / 27), maxLines: 2),
                                                      ),
                                                      const SizedBox(height: 10),
                                                      Container(
                                                        width: Get.width * .4,
                                                        decoration: BoxDecoration(
                                                            color: textblackColor.withOpacity(.05), border: Border.all(color: textblackColor.withOpacity(.09)), borderRadius: const BorderRadius.all(Radius.circular(25))),
                                                        padding: const EdgeInsets.symmetric(vertical: 5),
                                                        child: Center(
                                                          child: Text('Upcoming',
                                                              style: TextStyle(fontWeight: FontWeight.w600, overflow: TextOverflow.ellipsis, color: textblackColor), maxLines: 2, textAlign: TextAlign.center),
                                                        ),
                                                      ),
                                                      const SizedBox(height: 10),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                    ],
                  ),
                ),
        ),
      );
    });
  }
}
