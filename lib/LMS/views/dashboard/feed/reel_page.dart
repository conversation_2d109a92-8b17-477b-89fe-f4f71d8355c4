import 'dart:developer';
import 'dart:math';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';

// import 'package:edutalim/view/player/CustomMp4Player/widgets/PlayerErrorWidget.dart';
import 'package:video_player/video_player.dart';

import '../../../controllers/reelcontroller/reel_video_player_controller.dart';
import '../../InteractivePlayer/widgets/PlayerErrorWidget.dart';

class reel_page extends StatefulWidget {
  reel_page({super.key, required this.data, required this.index});

  List data;
  int index;

  @override
  State<reel_page> createState() => _CustomMp4PlayerState();
}

class _CustomMp4PlayerState extends State<reel_page> with SingleTickerProviderStateMixin {
  ReelVideoPlayerGetXController controller = Get.put(ReelVideoPlayerGetXController());

  bool aaa = true, read_more = false;
  late AnimationController _animationcontroller;

  @override
  void initState() {
    setState(() {
      sel_index = widget.index;
    });

    controller.videoFiles = widget.data;
    controller.videoData = widget.data;

    if (controller.videoFiles.isNotEmpty) {
      controller.selectedFile = controller.videoFiles[sel_index];
      controller.playerController = VideoPlayerController.networkUrl(Uri.parse(controller.videoFiles[sel_index]!['media_url'].toString()))
        // controller.playerController = VideoPlayerController.networkUrl(Uri.parse(controller.videoFiles[sel_index]!['video_url'].toString()))
        ..initialize().then((_) {
          setState(() {
            controller.playerController.setLooping(true);
            controller.playerController.play();
            aaa = false;
            // controller.startShowTimer();
          });
        }).catchError((error) {
          controller.isVideoError = true;
          controller.update();
        });

      controller.playerController.addListener(() => controller.onPlayerEvent());
    }

    super.initState();

    _animationcontroller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 10), // Adjust speed
    )..repeat(); // Loops the animation indefinitely
  }

  Duration getDurationFromString(String time) {
    final parts = time.split(':');

    if (parts.length != 3) {
      // log("Exception Invalid format, expected HH:MM:SS");
    }

    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    final seconds = int.parse(parts[2]);

    return Duration(
      hours: hours,
      minutes: minutes,
      seconds: seconds,
    );
  }

  @override
  void dispose() {
    controller.playerController.removeListener(() => controller.onPlayerEvent());
    controller.playerController.dispose();
    controller.isVideoError = false;
    _animationcontroller.dispose();
    super.dispose();
  }

  SwiperController swipercontroller = SwiperController();
  int sel_index = 0;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReelVideoPlayerGetXController>(builder: (controller) {
      return Scaffold(
        backgroundColor: textblackColor,
        body: Stack(
          children: [
            Container(
              color: textblackColor,
              width: Get.width,
              height: Get.height,
              child: Swiper(
                itemBuilder: (BuildContext context, int index) {
                  return aaa
                      ? loader()
                      : Container(
                          child: controller.videoFiles.isEmpty
                              ? Container(
                                  color: textblackColor,
                                  child: Center(
                                    child: Text(
                                      "Can't load ;(",
                                      style: TextStyle(color: textwhiteColor),
                                    ),
                                  ),
                                )
                              : controller.playerController.value.isInitialized
                                  ? Stack(
                                      children: [
                                        GestureDetector(
                                          onTap: () {},
                                          child: SizedBox.expand(
                                            child: FittedBox(
                                              fit: BoxFit.cover,
                                              child: SizedBox(
                                                width: controller.playerController.value.size.width,
                                                height: controller.playerController.value.size.height,
                                                child: VideoPlayer(controller.playerController),
                                              ),
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                          left: 0,
                                          right: 0,
                                          bottom: 0,
                                          top: 0,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                colors: [textblackColor, Colors.transparent, Colors.transparent],
                                                begin: Alignment.bottomCenter,
                                                end: Alignment.topCenter,
                                                stops: [0, 0.5, 1],
                                              ),
                                            ),
                                            child: Column(
                                              children: [
                                                AppBar(
                                                  backgroundColor: Colors.transparent,
                                                  surfaceTintColor: Colors.transparent,
                                                  elevation: 0,
                                                  leading: IconButton(
                                                    onPressed: () {
                                                      Get.back();
                                                    },
                                                    icon: Icon(
                                                      Icons.arrow_back,
                                                      color: textwhiteColor,
                                                    ),
                                                  ),
                                                  centerTitle: true,
                                                  title: Text(
                                                    'Shorts',
                                                    style: TextStyle(fontWeight: FontWeight.w600, color: textwhiteColor, fontSize: Get.width / 20),
                                                  ),
                                                ),
                                                Expanded(
                                                  child: GestureDetector(
                                                    onTap: () {
                                                      controller.playerController.setVolume(0);
                                                    },
                                                    child: Container(),
                                                  ),
                                                ),
                                                Container(
                                                  padding: EdgeInsets.symmetric(horizontal: Get.width / 20),
                                                  child: Column(
                                                    children: [
                                                      Container(
                                                        child: Row(
                                                          children: [
                                                            AnimatedBuilder(
                                                              animation: _animationcontroller,
                                                              builder: (context, child) {
                                                                return Transform.rotate(
                                                                  angle: _animationcontroller.value * 2 * pi, // Rotates 360 degrees
                                                                  child: child,
                                                                );
                                                              },
                                                              child: Container(
                                                                width: Get.width / 10,
                                                                height: Get.width / 10,
                                                                child: ClipRRect(
                                                                  borderRadius: BorderRadius.circular(1000),
                                                                  child: Image.asset('assets/logo/logo_.png'),
                                                                ),
                                                              ),
                                                            ),
                                                            SizedBox(
                                                              width: 8,
                                                            ),
                                                            Expanded(
                                                              child: Column(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                children: [
                                                                  Row(
                                                                    mainAxisSize: MainAxisSize.min,
                                                                    children: [
                                                                      Container(
                                                                        constraints: BoxConstraints(minWidth: 10, maxWidth: Get.width / 1.4),
                                                                        child: Text(appfullName, style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, overflow: TextOverflow.ellipsis,fontSize: Get.width/30), maxLines: 2),
                                                                      ),
                                                                      SizedBox(width: 8),
                                                                      Icon(Icons.verified, color: textwhiteColor, size: Get.width / 30),
                                                                    ],
                                                                  ),
                                                                  Container(
                                                                    padding: EdgeInsets.only(left: 2),
                                                                    constraints: BoxConstraints(minWidth: 10, maxWidth: Get.width / 1.4),
                                                                    child: Text(
                                                                      controller.videoFiles[sel_index]!['title'].toString() == "null" ? appName : controller.videoFiles[sel_index]!['title'].toString(),
                                                                      style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w500, overflow: TextOverflow.ellipsis, fontSize: Get.width / 32),
                                                                      maxLines: 1,
                                                                      overflow: TextOverflow.ellipsis,
                                                                    ),
                                                                  ),

                                                                  SizedBox(height: 6),
                                                                  Container(
                                                                    child: Row(
                                                                      children: [
                                                                        Icon(
                                                                          Icons.audiotrack,
                                                                          color: textwhiteColor,
                                                                          size: Get.width / 25,
                                                                        ),
                                                                        SizedBox(
                                                                          width: 3,
                                                                        ),
                                                                        Container(
                                                                          child: Text(
                                                                            "Original Audio - $appName Original",
                                                                            style: TextStyle(color: textwhiteColor, fontSize: Get.width / 40),
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        height: 10,
                                                      ),
                                                      // Text(controller.videoFiles[sel_index]!['content'].toString().length.toString(),style: TextStyle(color: textwhiteColor,fontSize: Get.width/30),maxLines: 10,),
                                                      if (controller.videoFiles[sel_index]!['description'].toString() != "null")
                                                        GestureDetector(
                                                          onTap: () {
                                                            if (read_more) {
                                                              setState(() {
                                                                read_more = false;
                                                              });
                                                            } else {
                                                              setState(() {
                                                                read_more = true;
                                                              });
                                                            }
                                                          },
                                                          child: Container(
                                                            width: Get.width,
                                                            alignment: Alignment.centerLeft,
                                                            // color: Colors.yellow,
                                                            child: Text(
                                                              controller.videoFiles[sel_index]!['description'].toString().length > 100
                                                                  ? read_more
                                                                      ? "${controller.videoFiles[sel_index]!['description']} .... Show Less"
                                                                      : "${controller.videoFiles[sel_index]!['description'].toString().substring(0, 100)} ....Read More"
                                                                  : controller.videoFiles[sel_index]!['description'].toString(),
                                                              style: TextStyle(color: textwhiteColor, fontSize: Get.width / 30),
                                                              maxLines: 10,
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: Get.height / 30,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        if (controller.playerController.value.isCompleted)
                                          Positioned(
                                            left: 0,
                                            right: 0,
                                            bottom: 0,
                                            top: 0,
                                            child: GestureDetector(
                                              onTap: () {
                                                controller.playerController.play();
                                              },
                                              child: Center(
                                                child: Container(
                                                  decoration: BoxDecoration(color: textwhiteColor.withValues(alpha: 0.2), borderRadius: BorderRadius.circular(1000)),
                                                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                                  child: Row(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      Icon(
                                                        Icons.refresh,
                                                        color: textwhiteColor,
                                                      ),
                                                      SizedBox(
                                                        width: 10,
                                                      ),
                                                      Text(
                                                        "Play Again",
                                                        style: TextStyle(color: textwhiteColor),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    )
                                  : controller.isVideoError
                                      ? PlayerErrorWidget()
                                      : Stack(
                                          alignment: Alignment.center,
                                          children: [
                                            Image.asset(
                                              "assets/logo/logo_icon.png",
                                              width: Get.width / 2,
                                            ),
                                            Container(
                                                color: Colors.black54,
                                                child: const Center(
                                                    child: CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  color: Colors.red,
                                                ))),
                                          ],
                                        ),
                        );
                },
                controller: swipercontroller,
                itemCount: controller.videoFiles.length,
                scrollDirection: Axis.vertical,
                onIndexChanged: (val) {
                  controller.playerController.pause();

                  print(val.toString());

                  setState(() {
                    sel_index = val;
                  });

                  print(controller.videoFiles[sel_index]['media_url'].toString());

                  controller.playerController = VideoPlayerController.networkUrl(Uri.parse(controller.videoFiles[sel_index]!['media_url'].toString()))
                    ..initialize().then((_) {
                      setState(() {
                        controller.playerController.play();
                        // controller.startShowTimer();
                      });
                    }).catchError((error) {
                      controller.isVideoError = true;
                      controller.update();
                    });
                },
              ),
            ),
            if (aaa)
              Positioned(
                left: 0,
                right: 0,
                top: 0,
                child: AppBar(
                  backgroundColor: appbarblackColor,
                  surfaceTintColor: appbarblackColor,
                  automaticallyImplyLeading: true,
                  iconTheme: IconThemeData(
                    color: textwhiteColor,
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }
}
