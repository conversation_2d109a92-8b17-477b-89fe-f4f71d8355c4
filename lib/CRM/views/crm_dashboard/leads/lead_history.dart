import 'package:edutalim/CRM/api/apis.dart';
import 'package:edutalim/CRM/views/call_log/filter_call_log.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class lead_history extends StatefulWidget {
  List data;
  lead_history({required this.data});

  @override
  State<lead_history> createState() => _leads_listState();
}

class _leads_listState extends State<lead_history> {
  List leads = [], history = [];
  bool isLoading = true;

  get_lead_history() async {
    print("called------");
    var data = await CRMApiBaseHandler.fet_laad_history_api(
        widget.data[0]['id'].toString());

    print(data.toString());

    setState(() {
      history = data['data']['history'];
      isLoading = false;
    });
  }

  @override
  void initState() {
    get_lead_history();
    leads = widget.data;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: true,
        iconTheme: IconThemeData(
          color: Colors.black,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "Lead History",
          style: TextStyle(
              fontFamily: 'poppins_regular', color: Colors.black, fontSize: 16),
        ),
      ),
      body: isLoading
          ? loader()
          : leads.isEmpty
              ? Container(
                  width: Get.width,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: Get.width / 4,
                        height: Get.width / 4,
                        child: Image.asset(
                          'assets/icons/no_data.png',
                          width: Get.width / 4,
                          errorBuilder: (a, b, c) {
                            return Image.asset(
                              'assets/images/avater_placeholder.jpg',
                              fit: BoxFit.cover,
                            );
                          },
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Container(
                        child: Text(
                          "No Data Found",
                          style: TextStyle(
                              fontFamily: 'poppins_regular',
                              color: Colors.black,
                              fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                )
              : Container(
                  child: Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.vertical(
                              bottom: Radius.circular(20)),
                          color: Colors.white,
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15, vertical: 13),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: Get.width / 5,
                                        height: Get.width / 5,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color: primaryColor, width: 2),
                                          borderRadius:
                                              BorderRadius.circular(50),
                                        ),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(50),
                                          child: Image.network(
                                            leads[0]['profile_picture']
                                                .toString(),
                                            fit: BoxFit.cover,
                                            errorBuilder: (a, b, c) {
                                              return Image.asset(
                                                'assets/images/avater_placeholder.jpg',
                                                fit: BoxFit.cover,
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Container(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Row(
                                              children: [
                                                Container(
                                                  width: Get.width / 1.5,
                                                  child: Text(
                                                    leads[0]['name'].toString(),
                                                    style: TextStyle(
                                                      fontSize: 15,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: commontextColor,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Container(
                                              width: Get.width / 1.5,
                                              child: Text(
                                                leads[0]['country_code']
                                                        .toString() +
                                                    leads[0]['phone']
                                                        .toString(),
                                                style: TextStyle(
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w500,
                                                  color: commontextColor,
                                                ),
                                              ),
                                            ),
                                            Container(
                                              width: Get.width / 1.5,
                                              child: Text(
                                                leads[0]['email'].toString(),
                                                style: TextStyle(
                                                  fontSize: 11,
                                                  fontWeight: FontWeight.w500,
                                                  color: commontextColor,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(
                                                height:
                                                    8), // Space before button
                                            SizedBox(
                                              height: 30,
                                              child: ElevatedButton(
                                                onPressed: () {
                                                  // Add your action here
                                                  // Navigator.push(
                                                  //   context,
                                                  //   MaterialPageRoute(
                                                  //     builder: (_) =>
                                                  //         FilteredCallLogPage(
                                                  //             number: leads[0]
                                                  //                 ['phone']),
                                                  //   ),
                                                  // );
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor: primaryColor,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                  ),
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 16),
                                                ),
                                                child: const Text(
                                                  'Call Details',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Expanded(
                        child: history.isEmpty
                            ? Container(
                                width: Get.width,
                                color: Colors.white,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.history,
                                      size: Get.width / 3,
                                      color: Colors.black26,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Container(
                                      alignment: Alignment.center,
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 10),
                                      child: Text(
                                        "No History Found",
                                        style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: Get.width / 23),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : ListView.builder(
                                physics: ScrollPhysics(),
                                itemCount: history.length,
                                shrinkWrap: true,
                                itemBuilder: (context, index1) {
                                  return Container(
                                    margin: EdgeInsets.symmetric(
                                        horizontal: 15, vertical: 5),
                                    child: Stack(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(10)),
                                              color: Colors.white),
                                          child: Column(
                                            children: [
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 10,
                                                    vertical: 8),
                                                alignment: Alignment.centerLeft,
                                                decoration: BoxDecoration(
                                                  color: Colors.black12,
                                                  borderRadius:
                                                      BorderRadius.vertical(
                                                          top: Radius.circular(
                                                              10)),
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Container(
                                                      // color: Colors.yellowAccent,
                                                      width: Get.width / 2,
                                                      child: Text(
                                                        "Date : " +
                                                            history[index1][
                                                                    'updated_at']
                                                                .toString(),
                                                        style: TextStyle(
                                                            color:
                                                                Colors.black54,
                                                            fontSize: 12),
                                                      ),
                                                    ),
                                                    Container(
                                                      width: Get.width / 2.8,
                                                      alignment:
                                                          Alignment.centerRight,
                                                      child: Container(
                                                        decoration: BoxDecoration(
                                                            color:
                                                                secondaryColor,
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        50)),
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                horizontal: 8,
                                                                vertical: 5),
                                                        child: Text(
                                                          history[index1][
                                                                  'lead_status']
                                                              .toString(),
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontSize: 11),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(
                                                height: 5,
                                              ),
                                              Container(
                                                alignment: Alignment.centerLeft,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 10),
                                                child: Text(
                                                  "Remark : -",
                                                  style: TextStyle(
                                                      color: Colors.black54,
                                                      fontStyle:
                                                          FontStyle.italic,
                                                      fontSize: 13),
                                                ),
                                              ),
                                              SizedBox(
                                                height: 10,
                                              ),
                                              Container(
                                                alignment: Alignment.centerLeft,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 10),
                                                child: Text(
                                                  history[index1]['remarks']
                                                      .toString(),
                                                  style: TextStyle(
                                                      color: Colors.black54),
                                                ),
                                              ),
                                              SizedBox(
                                                height: 15,
                                              ),
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 10,
                                                    vertical: 5),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.end,
                                                  children: [
                                                    Container(
                                                      child: Text(
                                                        "Updated by : ",
                                                        style: TextStyle(
                                                            color:
                                                                Colors.black38,
                                                            fontSize:
                                                                Get.width / 37),
                                                      ),
                                                    ),
                                                    Container(
                                                      child: Text(
                                                        history[index1]
                                                                ['updated_by']
                                                            .toString(),
                                                        style: TextStyle(
                                                            color: Colors.black,
                                                            fontSize:
                                                                Get.width / 37),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),
    );
  }
}
