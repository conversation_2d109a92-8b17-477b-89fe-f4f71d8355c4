# Keep classes from the Razorpay library
-keep class com.razorpay.** { *; }

# Keep annotation classes
-keep class proguard.annotation.Keep { *; }
-keepclassmembers class proguard.annotation.KeepClassMembers { *; }
-dontwarn com.google.android.apps.nbu.paisa.inapp.client.api.PaymentsClient
-dontwarn com.google.android.apps.nbu.paisa.inapp.client.api.Wallet
-dontwarn com.google.android.apps.nbu.paisa.inapp.client.api.WalletUtils
-dontwarn com.google.devtools.build.android.desugar.runtime.ThrowableExtension
-keep class io.flutter.** { *; }
-keep class com.google.firebase.** { *; }
-dontwarn com.google.android.play.core.splitcompat.SplitCompatApplication
-dontwarn com.google.android.play.core.splitinstall.SplitInstallException
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManager
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManagerFactory
-dontwarn com.google.android.play.core.splitinstall.SplitInstallRequest$Builder
-dontwarn com.google.android.play.core.splitinstall.SplitInstallRequest
-dontwarn com.google.android.play.core.splitinstall.SplitInstallSessionState
-dontwarn com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener
-dontwarn com.google.android.play.core.tasks.OnFailureListener
-dontwarn com.google.android.play.core.tasks.OnSuccessListener
-dontwarn com.google.android.play.core.tasks.Task
-dontwarn proguard.annotation.Keep
-dontwarn proguard.annotation.KeepClassMembers
-dontwarn coil.compose.SingletonAsyncImageKt
-dontwarn com.android.billingclient.api.AcknowledgePurchaseParams$Builder
-dontwarn com.android.billingclient.api.AcknowledgePurchaseParams
-dontwarn com.android.billingclient.api.AcknowledgePurchaseResponseListener
-dontwarn com.android.billingclient.api.BillingClient$Builder
-dontwarn com.android.billingclient.api.BillingClient
-dontwarn com.android.billingclient.api.BillingClientStateListener
-dontwarn com.android.billingclient.api.BillingFlowParams$Builder
-dontwarn com.android.billingclient.api.BillingFlowParams$ProductDetailsParams$Builder
-dontwarn com.android.billingclient.api.BillingFlowParams$ProductDetailsParams
-dontwarn com.android.billingclient.api.BillingFlowParams$SubscriptionUpdateParams$Builder
-dontwarn com.android.billingclient.api.BillingFlowParams$SubscriptionUpdateParams
-dontwarn com.android.billingclient.api.BillingFlowParams
-dontwarn com.android.billingclient.api.BillingResult
-dontwarn com.android.billingclient.api.InAppMessageParams$Builder
-dontwarn com.android.billingclient.api.InAppMessageParams
-dontwarn com.android.billingclient.api.InAppMessageResponseListener
-dontwarn com.android.billingclient.api.InAppMessageResult
-dontwarn com.android.billingclient.api.ProductDetails$PricingPhase
-dontwarn com.android.billingclient.api.ProductDetails$PricingPhases
-dontwarn com.android.billingclient.api.ProductDetails$SubscriptionOfferDetails
-dontwarn com.android.billingclient.api.ProductDetails
-dontwarn com.android.billingclient.api.ProductDetailsResponseListener
-dontwarn com.android.billingclient.api.Purchase
-dontwarn com.android.billingclient.api.PurchasesResponseListener
-dontwarn com.android.billingclient.api.PurchasesUpdatedListener
-dontwarn com.android.billingclient.api.QueryProductDetailsParams$Builder
-dontwarn com.android.billingclient.api.QueryProductDetailsParams$Product$Builder
-dontwarn com.android.billingclient.api.QueryProductDetailsParams$Product
-dontwarn com.android.billingclient.api.QueryProductDetailsParams
-dontwarn com.android.billingclient.api.QueryPurchasesParams$Builder
-dontwarn com.android.billingclient.api.QueryPurchasesParams
-dontwarn com.google.api.client.http.GenericUrl
-dontwarn com.google.api.client.http.HttpHeaders
-dontwarn com.google.api.client.http.HttpRequest
-dontwarn com.google.api.client.http.HttpRequestFactory
-dontwarn com.google.api.client.http.HttpResponse
-dontwarn com.google.api.client.http.HttpTransport
-dontwarn com.google.api.client.http.javanet.NetHttpTransport$Builder
-dontwarn com.google.api.client.http.javanet.NetHttpTransport
-dontwarn com.google.zxing.BarcodeFormat
-dontwarn com.google.zxing.Binarizer
-dontwarn com.google.zxing.BinaryBitmap
-dontwarn com.google.zxing.DecodeHintType
-dontwarn com.google.zxing.EncodeHintType
-dontwarn com.google.zxing.LuminanceSource
-dontwarn com.google.zxing.PlanarYUVLuminanceSource
-dontwarn com.google.zxing.RGBLuminanceSource
-dontwarn com.google.zxing.Reader
-dontwarn com.google.zxing.ReaderException
-dontwarn com.google.zxing.Result
-dontwarn com.google.zxing.ResultMetadataType
-dontwarn com.google.zxing.ResultPoint
-dontwarn com.google.zxing.ResultPointCallback
-dontwarn com.google.zxing.WriterException
-dontwarn com.google.zxing.common.BitMatrix
-dontwarn com.google.zxing.common.DecoderResult
-dontwarn com.google.zxing.common.DetectorResult
-dontwarn com.google.zxing.common.HybridBinarizer
-dontwarn com.google.zxing.qrcode.QRCodeReader
-dontwarn com.google.zxing.qrcode.QRCodeWriter
-dontwarn com.google.zxing.qrcode.decoder.Decoder
-dontwarn com.google.zxing.qrcode.decoder.ErrorCorrectionLevel
-dontwarn com.google.zxing.qrcode.decoder.QRCodeDecoderMetaData
-dontwarn com.google.zxing.qrcode.detector.Detector
-dontwarn com.microsoft.intune.mam.client.app.MAMComponents
-dontwarn com.microsoft.intune.mam.policy.AppPolicy
-dontwarn com.microsoft.intune.mam.policy.MAMEnrollmentManager
-dontwarn com.microsoft.intune.mam.policy.MAMServiceAuthenticationCallback
-dontwarn com.microsoft.intune.mam.policy.NotificationRestriction
-dontwarn com.symbol.emdk.EMDKBase
-dontwarn com.symbol.emdk.EMDKManager$EMDKListener
-dontwarn com.symbol.emdk.EMDKManager$FEATURE_TYPE
-dontwarn com.symbol.emdk.EMDKManager$StatusData
-dontwarn com.symbol.emdk.EMDKManager$StatusListener
-dontwarn com.symbol.emdk.EMDKManager
-dontwarn com.symbol.emdk.EMDKResults$EXTENDED_STATUS_CODE
-dontwarn com.symbol.emdk.EMDKResults$STATUS_CODE
-dontwarn com.symbol.emdk.EMDKResults
-dontwarn com.symbol.emdk.ProfileManager$PROFILE_FLAG
-dontwarn com.symbol.emdk.ProfileManager
-dontwarn com.zipow.cnthirdparty.cnlogin.model.CnLoginProxy
-dontwarn io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
-dontwarn javax.lang.model.element.Element
-dontwarn javax.lang.model.element.ExecutableElement
-dontwarn javax.lang.model.element.Name
-dontwarn javax.lang.model.element.TypeElement
-dontwarn javax.lang.model.type.TypeMirror
-dontwarn kotlinx.android.parcel.Parcelize
-dontwarn kotlinx.parcelize.Parcelize
-dontwarn org.joda.time.Instant
-dontwarn us.zoom.apm.apis.ApmIssue
-dontwarn us.zoom.apm.apis.IApmReporter
-dontwarn us.zoom.apm.apis.IssueType
-dontwarn us.zoom.apm.apis.ZoomHostService$ZoomInitializeListener
-dontwarn us.zoom.apm.apis.ZoomHostService
-dontwarn us.zoom.intunelib.AuthenticationCallback
-dontwarn us.zoom.intunelib.IIntuneLoginAssistant
-dontwarn us.zoom.intunelib.InTuneDownloadPolicyActivity
-dontwarn us.zoom.intunelib.InTuneWelcomeActivity
-dontwarn us.zoom.intunelib.MSALUtil
-dontwarn us.zoom.intunelib.ZmIntuneLoginManager
-dontwarn us.zoom.intunelib.ZmIntuneMamManager
-dontwarn us.zoom.prism.R$attr
-dontwarn us.zoom.prism.R$color
-dontwarn us.zoom.prism.R$dimen
-dontwarn us.zoom.prism.R$drawable
-dontwarn us.zoom.prism.R$id
-dontwarn us.zoom.prism.R$layout
-dontwarn us.zoom.prism.R$plurals
-dontwarn us.zoom.prism.R$string
-dontwarn us.zoom.prism.R$style
-dontwarn us.zoom.prism.R$styleable
-dontwarn us.zoom.thirdparty.dialog.NoBrowserDialog
-dontwarn us.zoom.thirdparty.login.LoginType
-dontwarn us.zoom.thirdparty.login.ThirdPartyLogin
-dontwarn us.zoom.thirdparty.login.ThirdPartyLoginFactory
-dontwarn us.zoom.thirdparty.login.sso.SsoUtil
-dontwarn us.zoom.thirdparty.login.util.CustomTabsHelper
-dontwarn xcrash.ICrashCallback
-dontwarn xcrash.XCrash$InitParameters
-dontwarn xcrash.XCrash
-dontwarn com.google.firebase.FirebaseApp
-dontwarn com.google.firebase.FirebaseOptions$Builder
-dontwarn com.google.firebase.FirebaseOptions
-dontwarn com.google.firebase.messaging.FirebaseMessaging
-dontwarn com.google.firebase.messaging.FirebaseMessagingService
-dontwarn com.google.firebase.messaging.RemoteMessage