import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:video_player/video_player.dart';

class ReelVideoPlayerGetXController extends GetxController {



  int sel_item_index = 0;

  List lessondata = [],
      lessonalldata = [];
  bool islessondataloading = true,
      vidtaploading = true;
  String? chapter_id;


  // refresh_lesson_data() async {
  //   var data = await fet_new_lesssons(chapter_id.toString());
  //   lessondata = data['data'];
  //   lessonalldata = [data];
  //   islessondataloading = false;
  //   vidtaploading = false;
  //   update();
  // }


  // -----------------------------------------------------

  var videoData;
  List videoFiles = [];

  late VideoPlayerController playerController;

  Map? selectedFile;

  bool isQualityChanging = false;
  bool showControl = true;

  late Duration progress;
  late Duration duration;

  double playbackSpeed = 1.0;
  List playbackSpeedList = [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];


  bool isRewinding = false;
  bool isForwarding = false;

  bool isVideoError = false;


  void changeVideo(data) async {
    try {
      // Assign the new video file data
      selectedFile = data;

      // Save the current position
      final Duration? currentPosition = await playerController.position;

      // Pause and dispose of the current player
      await playerController.pause();
      await playerController.dispose();

      // Indicate that the quality is changing
      isQualityChanging = true;
      update();

      // Initialize the new VideoPlayerController
      playerController =
      VideoPlayerController.networkUrl(
        Uri.parse(data['video_url'].toString()),
      )..initialize().then((_) {
        playerController.setLooping(true);
        playerController.play();
      }).catchError((_) {
        isVideoError = true;
        update();
      })
        ..addListener(() => onPlayerEvent());

      // Seek to the previous position if it exists
      if (currentPosition != null) {
        await playerController.seekTo(currentPosition);
      }

      // Play the video and update the UI
      playerController.setLooping(true);
      await playerController.play();
      isQualityChanging = false;
      update();
    } catch (error) {
      log("Error changing video: $error");
      isQualityChanging = false;
      update();
    }
  }


  bool isGreaterThanOrEqual75Percent(String value, String total) {
    int valueInSeconds = _timeToSeconds(value);
    int totalInSeconds = _timeToSeconds(total);

    double threshold = totalInSeconds * 0.75; // 75% of total time
    return valueInSeconds >= threshold;
  }

  int _timeToSeconds(String time) {
    List<String> parts = time.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);
    int seconds = int.parse(parts[2]);
    return (hours * 3600) + (minutes * 60) + seconds;
  }

  void onPlayerEvent() {
    final currentPosition = playerController.value.position;
    final videoDuration = playerController.value.duration;


    if (!Get.context!.mounted) return;
    progress = playerController.value.position;
    duration = playerController.value.duration;
    update();


    // auto next play
    // if(playerController.value.isCompleted)
    if (currentPosition == videoDuration) {
      print('------------------------koiiiii-------------completed---');
      // print(lessondata[sel_item_index + 1]['attachment_type'].toString());
      //
      playerController.pause();
      if (sel_item_index < (lessondata.length - 1)) {
        if (lessondata[sel_item_index + 1]['attachment_type'].toString() == 'url') {
          // if (lessondata[sel_item_index + 1]['free'].toString() == 'on') {
          //   vidtaploading = true;
          //   update();
          //   sel_item_index = sel_item_index + 1;
          //   Future.delayed(const Duration(milliseconds: 20), () {
          //     vidtaploading = false;
          //     update();
          //   });
          //
          //   update();
          // } else {
          // }
        }
      }
    }


    // 75% next video unloack
    if (playerController.value.isPlaying) {
      var sec1 = playerController.value.position.inSeconds > 60
          ? 60
          : playerController.value.position.inSeconds;
      var sec2 = playerController.value.duration.inSeconds > 60
          ? 60
          : playerController.value.duration.inSeconds;
      var currPos = '${playerController.value.position.toString().split(':')[0]}:${playerController.value.position.toString().split(':')[1]}:${playerController.value.position.toString().split(':')[2]
          .toString()
          .split('.')[0]}';
      var toatlHour = '${playerController.value.duration.toString().split(':')[0]}:${playerController.value.duration.toString().split(':')[1]}:${playerController.value.duration.toString().split(':')[2]
          .toString()
          .split('.')[0]}';

      print('hi----' + '-----curr hour  ' + currPos.toString() + "----------total  " + toatlHour.toString());




      // if(currentPosition == videoDuration){
      //
      //   save_video_progress(
      //     videoData['id'].toString(),
      //     playerController.value.duration.toString().split('.').first,
      //     playerController.value.position.toString().split('.').first,
      //   );
      // }

      print("Video is playing: Total Duration - $videoDuration");
      print("Video is playing: Current Position - $currentPosition");
    } else {
      if (playerController.value.position.toString().split('.').first != "00:00:00") {

      }

      print("Video is paused or stopped. Current position: ${currentPosition
          .toString()
          .split('.')
          .first}");
    }
  }


  void onSliderChanged(double value) {
    print('----slided------------------------------------');
    print(playbackSpeed.toString() + "---------s--");

    var newProgress = Duration(milliseconds: value.toInt());
    playerController.seekTo(newProgress);
    progress = newProgress;
    playerController.play();


    update();

    Future.delayed(const Duration(milliseconds: 50), () {
      playerController.setPlaybackSpeed(playbackSpeed);
      update();
    });

    print(playbackSpeed.toString() + "---------e--");
  }


  // void toggleFullScreen(isFullScreen) {
  //   if (isFullScreen) {
  //     Get.back();
  //   } else {
  //     // Get.to(() => FullscreenPlayer())!.then((_){
  //
  //     });
  //   }
  // }


  String formatDuration(Duration duration) {
    final hours = duration.inHours.toString().padLeft(2, '0');
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');

    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }


  Timer? _controlTimer;


  bool isSavingVideoProgress = false;

// save_video_progress(lesson_id, lesson_duration, user_progress) async {
//   isSavingVideoProgress = true;
//   var data = await PlayerApisHandler.save_video_progress_api(
//       lesson_id, lesson_duration, user_progress);
//   // log( data['status'].toString() == "1" ? "Video Progress Saved" : "Error on Saving Video Progress");
//   isSavingVideoProgress = false;
// }


}