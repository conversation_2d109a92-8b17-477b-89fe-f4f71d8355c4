// import 'package:call_log/call_log.dart';
// import 'package:edutalim/components/utils.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
//
//
//
//
// class CallLogPage extends StatefulWidget {
//   @override
//   _CallLogPageState createState() => _CallLogPageState();
// }
//
// class _CallLogPageState extends State<CallLogPage> {
//   List<CallLogEntry> callLogs = [];
//   int missed = 0,incoming = 0,outgoing = 0,other = 0,unknown = 0,contacts = 0;
//
//   counter(type,name)async{
//     setState(() {
//       if(type.toString() == "missed")
//       {
//         missed += 1;
//       }
//       else if(type.toString() == "incoming")
//       {
//         incoming += 1;
//       }
//       else if(type.toString() == "outgoing")
//       {
//         outgoing += 1;
//       }else{
//         other += 1;
//       }
//
//       if(name.toString() == "unknown")
//         {
//           unknown += 1;
//         }
//       else{
//         contacts += 1;
//       }
//
//     });
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     getCallLogs();
//   }
//
//   Future<void> getCallLogs() async {
//     Iterable<CallLogEntry> entries = await CallLog.get();
//     setState(() {
//       callLogs = entries.toList();
//     });
//
//     for(var i=0;i<=callLogs.length;i++){
//       CallLogEntry log = callLogs[i];
//       counter(log.callType.toString().split('.')[1].toString(),log.name ?? 'unknown');
//     }
//
//   }
//
//   String formatTimestamp(int timestamp) {
//     DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
//     String formattedDate = DateFormat('MMM dd, yyyy').format(dateTime);
//     String formattedTime = DateFormat('hh:mm a').format(dateTime);
//     return '$formattedDate - $formattedTime';
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Call Logs',style: TextStyle(color: Colors.black),),
//         backgroundColor: Colors.white,
//         elevation: 0,
//         iconTheme: IconThemeData(color: Colors.black),
//         centerTitle: true,
//       ),
//       body: Container(
//         padding: EdgeInsets.symmetric(vertical: 10),
//         child: Column(
//           children: [
//
//             Container(
//               padding: EdgeInsets.symmetric(horizontal: 10),
//               child: Row(
//                 children: [
//                   Expanded(
//                     child: Container(
//                       padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//                       margin: EdgeInsets.all(5),
//                       color: Colors.white,
//                       child: Column(
//                         children: [
//                           Text("Incoming ",style: TextStyle(color: Colors.black),),
//                           Text(incoming.toString(),style: TextStyle(color: Colors.black,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//                         ],
//                       )
//                     ),
//                   ),
//                   Expanded(
//                     child: Container(
//                         padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//                         margin: EdgeInsets.all(5),
//                         color: Colors.white,
//                         child: Column(
//                           children: [
//                             Text("Outgoing",style: TextStyle(color: Colors.black),),
//                             Text(outgoing.toString(),style: TextStyle(color: Colors.black,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//                           ],
//                         )
//                     ),
//                   ),
//                   Expanded(
//                     child: Container(
//                         padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//                         margin: EdgeInsets.all(5),
//                         color: Colors.white,
//                         child: Column(
//                           children: [
//                             Text("Missed",style: TextStyle(color: Colors.red),),
//                             Text(missed.toString(),style: TextStyle(color: Colors.red,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//                           ],
//                         )
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//
//             Container(
//               padding: EdgeInsets.symmetric(horizontal: 10),
//               child: Row(
//                 children: [
//                   Expanded(
//                     child: Container(
//                         padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//                         margin: EdgeInsets.all(5),
//                         color: Colors.white,
//                         child: Column(
//                           children: [
//                             Text("Contacts",style: TextStyle(color: Colors.green),),
//                             Text(contacts.toString(),style: TextStyle(color: Colors.green,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//                           ],
//                         )
//                     ),
//                   ),
//                   Expanded(
//                     child: Container(
//                         padding: EdgeInsets.symmetric(vertical: 15,horizontal: 15),
//                         margin: EdgeInsets.all(5),
//                         color: Colors.white,
//                         child: Column(
//                           children: [
//                             Text("Unknown",style: TextStyle(color: Colors.orange),),
//                             Text(unknown.toString(),style: TextStyle(color: Colors.orange,fontSize: Get.width/22,fontWeight: FontWeight.w600),),
//                           ],
//                         )
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//
//             Divider(),
//
//             Expanded(
//               child: ListView.builder(
//                 itemCount: callLogs.length,
//                 physics: ScrollPhysics(),
//                 itemBuilder: (context, index) {
//                   CallLogEntry log = callLogs[index];
//                   return Container(
//                     decoration: BoxDecoration(
//                         color: Colors.white,
//                         borderRadius: BorderRadius.circular(12)
//                     ),
//                     margin: EdgeInsets.symmetric(horizontal: 10,vertical: 5),
//                     padding: EdgeInsets.symmetric(vertical: 12,horizontal: 15),
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Row(
//                           children: [
//                             Container(
//                               child:  Icon(Icons.account_circle,color: Colors.black54,size: Get.width/10,),
//                             ),
//                             SizedBox(width: 13,),
//                             Container(
//                               child: Column(
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Container(
//                                     // color: Colors.yellow,
//                                     width: Get.width/1.8,
//                                     child: Text(log.name ?? 'Unknown',style: TextStyle(
//                                         color: log.callType.toString().split('.')[1].toString() == "missed" ? Colors.red : Colors.black,
//                                         fontFamily: 'poppins_regular',fontWeight: FontWeight.w600,fontSize: Get.width/24),),
//                                   ),
//                                   SizedBox(height: 4,),
//                                   Container(
//                                     // color: Colors.yellow,
//                                     width: Get.width/1.8,
//                                     child: Text(log.number.toString(),style: TextStyle(color: Colors.black),),
//                                   ),
//                                   SizedBox(height: 4,),
//                                   Container(
//                                     child: Row(
//                                       children: [
//                                         log.callType.toString().split('.')[1].toString() == "missed" ?
//                                         Icon(Icons.call_missed,color: Colors.red,size: 15,)
//                                             :
//                                         log.callType.toString().split('.')[1].toString() == "outgoing" ?
//                                         Icon(Icons.call_made_outlined,color: Colors.black54,size: 15,)
//                                             :
//                                         log.callType.toString().split('.')[1].toString() == "incoming" ?
//                                         Icon(Icons.call_received,color: Colors.black54,size: 15,)
//                                             :
//                                         Icon(Icons.call_end,color: Colors.black54,size: 15,),
//                                         SizedBox(width: 5,),
//                                         Container(
//                                           child: Text(log.callType.toString().split('.')[1].toString(),style: TextStyle(
//                                               color: log.callType.toString().split('.')[1].toString() == "missed" ? Colors.red : Colors.black
//                                           ),),
//                                         ),
//                                         Container(
//                                           child: Text(" - "+formatDuration(log.duration!).toString(),style: TextStyle(color: Colors.black),),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                   Container(
//                                     // color: Colors.yellow,
//                                     width: Get.width/1.8,
//                                     child: Text(formatTimestamp(int.parse(log.timestamp.toString())).toString(),style: TextStyle(color: Colors.black,fontSize: Get.width/33),),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                         Container(
//                           child: IconButton(
//                             onPressed: (){
//                               print("tested-----------");
//                               launchURL('tel:'+log.number.toString());
//                             },
//                             icon: Icon(Icons.call,color: Colors.green,),
//                           )
//                         ),
//                       ],
//                     ),
//                   );
//                 },
//               ),
//             ),
//           ],
//         )
//       ),
//     );
//   }
// }