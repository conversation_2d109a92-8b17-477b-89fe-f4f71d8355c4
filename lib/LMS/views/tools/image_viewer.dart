import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class image_viewer extends StatelessWidget {
  String url,title;
  image_viewer({required this.url,required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: GestureDetector(
          onTap: (){
            Navigator.pop(context);
          },
          child: Icon(Icons.arrow_back_ios_rounded,color: Colors.black54,size: 30,),
        ),
        iconTheme: IconThemeData(color: Colors.black),
        title: Text(title.toString() ?? "",style: TextStyle(color: Colors.black87,fontSize: Get.width/25),),
      ),
      body: Container(
        child: Center(
          child: Image.network(
            url.toString(),
            fit: BoxFit.fitWidth,
            errorBuilder: (a,b,c){ return Container();
              },
          ),
        )
      ),
    );
  }
}
