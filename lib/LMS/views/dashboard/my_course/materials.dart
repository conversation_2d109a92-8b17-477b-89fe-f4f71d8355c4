import 'dart:developer';

import 'package:edutalim/LMS/views/tools/pdf_viewer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/mycoursecontroller.dart';

class materials extends StatefulWidget {
  String course_id,section_id,lesson_id;
  materials({required this.course_id,required this.section_id, required this.lesson_id});

  // String course_id,section_id,lesson_id;
  // model_exams({required this.course_id,required this.section_id,required this.lesson_id});

  // String subject_id,lesson_id;
  // materials({required this.subject_id,required this.lesson_id});

  @override
  State<materials> createState() => _sessionsState();
}

class _sessionsState extends State<materials> {


  Mycoursecontroller controller = Get.put(Mycoursecontroller());


  @override
  void initState() {

    super.initState();
    controller.getMaterial(widget.course_id.toString(),widget.section_id.toString(),widget.lesson_id.toString());
    log(" Course IDDDDDDDDD : "+widget.course_id.toString());
    // controller.get_tests(course_id, section_id, lesson_id);
    // controller.get_exams(widget.subject_id.toString(),widget.lesson_id.toString());
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Mycoursecontroller>(
        builder: (controller) {
          return Scaffold(
            backgroundColor: textwhiteColor,
            appBar: AppBar(
              backgroundColor: textwhiteColor,
              surfaceTintColor: textwhiteColor,
              elevation: 5,
              centerTitle: true,
              leading:  GestureDetector(
                onTap: (){
                  Get.back();
                },
                child: Container(
                  // decoration: BoxDecoration(
                  //     color: primaryColor,
                  //     borderRadius: BorderRadius.circular(100)
                  // ),
                  margin: EdgeInsets.all(10),
                  child: Icon(Icons.arrow_back,color: textblackColor,),
                ),
              ),
              title: Container(
                child: Text("Materials",style: TextStyle(color: textblackColor,fontWeight: FontWeight.w700,fontFamily: font_regular,fontSize: Get.width/24),),
              ),
            ),
            body: Container(
              height: Get.height,
              width: Get.width,
              color: textwhiteColor,
              child: Column(
                children: [

                  controller.isMaterialLoading?Container(padding: EdgeInsets.symmetric(vertical: 100),child: loader()):
                  controller.materialList.isEmpty?
                  Container(
                    height: 200,
                    child: Center(child: Text("No Data Found",style: TextStyle(fontFamily: 'poppins_regular',color: Colors.black,fontSize: 16),)),
                  ):
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 15),
                      child: ListView.builder(
                        physics: ScrollPhysics(),
                        padding: EdgeInsets.symmetric(vertical: 20),
                        shrinkWrap: true,
                        itemCount: controller.materialList.length,
                        itemBuilder: (BuildContext context, int index) {
                          Map data = controller.materialList[index];
                          return GestureDetector(
                            onTap: (){
                              Get.to(() => pdfviewer_new(pdf_url: data['lesson_url'].toString(), title: data['title'].toString(),id: data['id'].toString(),));
                            },
                            child: Stack(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(bottom: 20),
                                  padding: EdgeInsets.symmetric(horizontal: 10,vertical: 10),
                                  decoration: BoxDecoration(
                                      color:  textwhiteColor,
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(color: textblackColor.withOpacity(0.3),width : 0.8)
                                  ),
                                  child: IntrinsicHeight(
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Image.asset("assets/icons/materials.png",width: Get.width/10,),
                                        const SizedBox(width: 15),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(data['title'].toString(),style: TextStyle(color: textblackColor,fontWeight: FontWeight.w600,fontSize: Get.width/26),maxLines: 2,overflow: TextOverflow.ellipsis,),
                                              Text(data['short_description'].toString(),style: TextStyle(color: textblackColor,fontWeight: FontWeight.w400,fontSize: Get.width/32),maxLines: 2,overflow: TextOverflow.ellipsis,),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(width: 10),
                                        Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon( data['premium_lock'].toString() == "1" ? CupertinoIcons.lock_circle:CupertinoIcons.chevron_forward,color: textblackColor.withOpacity(0.5),size: Get.width/15,),
                                          ],
                                        ),

                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                ],
              ),
            ),
            // floatingActionButton: FloatingActionButton(
            //   shape: RoundedRectangleBorder(
            //     borderRadius: BorderRadius.circular(1000),
            //   ),
            //   backgroundColor: primaryColor,
            //   onPressed: (){
            //     Get.to(()=> webview_page(url: 'https://trogon.info/tutorpro/skyil/home/<USER>/55/3'));
            //   },
            //   child: Icon(Icons.arrow_forward_ios_rounded,color: textwhiteColor,),
            // ),
          );
        }
    );
  }
}
