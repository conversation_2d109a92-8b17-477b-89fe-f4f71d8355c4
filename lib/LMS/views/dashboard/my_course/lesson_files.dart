import 'dart:io';
import 'dart:math';
import 'package:edutalim/LMS/views/tools/pdf_viewer.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/LMS/views/dashboard/my_course/discussion_forum.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:edutalim/components/utils.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';
import '../../../../components/dotted_line.dart';
import '../../../controllers/mycoursecontroller.dart';
import '../../InteractivePlayer/Controller/VideoPlayerController.dart';
import '../../InteractivePlayer/CustomMp4Player.dart';
import '../../InteractivePlayer/better_player.dart';

class lesson_files extends StatefulWidget {
  var lesson;

  lesson_files({required this.lesson});

  @override
  State<lesson_files> createState() => _lesson_filesState();
}

class _lesson_filesState extends State<lesson_files> with SingleTickerProviderStateMixin {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());
  VideoPlayerGetXController vidcontroller = Get.put(VideoPlayerGetXController());

  late TabController _tabController;

  @override
  void initState() {
    vidcontroller.lessonId = widget.lesson['id'].toString();
    print(widget.lesson.toString());
    _tabController = TabController(length: 2, vsync: this);
    vidcontroller.get_lesson_files(widget.lesson['id'].toString());
    vidcontroller.getMaterial(widget.lesson['course_id'] ?? '', widget.lesson['course_id'] ?? '', widget.lesson['id'].toString());
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    vidcontroller.lesson_files = [];
    vidcontroller.sel_index = 0;
    vidcontroller.sel_video = null;
    controller.materialList = [];
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VideoPlayerGetXController>(builder: (controller) {
      return Scaffold(
        backgroundColor: textwhiteColor,
        body: Container(
          width: Get.width,
          height: Get.height,
          decoration: BoxDecoration(color: textwhiteColor),
          child: Column(
            children: [
              AppBar(
                backgroundColor: appbarwhiteColor,
                surfaceTintColor: appbarwhiteColor,
                centerTitle: false,
                title: Text(widget.lesson['title'].toString(), style: TextStyle(color: textblackColor, fontWeight: FontWeight.w500, fontSize: Get.width / 22)),
              ),

              // Video Player Section - Only show when Videos tab is selected
              AnimatedBuilder(
                animation: _tabController,
                builder: (context, child) {
                  return Column(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15),
                          child: controller.isLessonFileLoading
                              ? SizedBox(
                                  height: Get.height / 3.8,
                                  width: Get.width / 1.1,
                                  child: LinearProgressIndicator(
                                    color: textblackColor.withOpacity(0.05),
                                    backgroundColor: textblackColor.withOpacity(0.025),
                                  ),
                                )
                              : controller.selvidindexloading
                                  ? AspectRatio(aspectRatio: 16 / 9, child: Center(child: CircularProgressIndicator(color: Colors.red, strokeWidth: 1)))
                                  : controller.sel_video == null || controller.sel_video['video_links'].isEmpty
                                      ? AspectRatio(
                                          aspectRatio: 16 / 9,
                                          child: Container(
                                            decoration: BoxDecoration(color: Colors.black),
                                            child: Center(child: Text("No Video Found", style: TextStyle(color: Colors.white))),
                                          ),
                                        )
                                      : CustomMp4Player(
                                          video_data: controller.sel_video ?? {"video_links": []},
                                        ),
                        ),
                      ),

                      // Video Info Section
                      controller.isLessonFileLoading
                          ? Container(
                              margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                              height: 20,
                              width: Get.width / 1.5,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(4),
                                child: LinearProgressIndicator(color: textblackColor.withOpacity(0.05), backgroundColor: textblackColor.withOpacity(0.025)),
                              ),
                            )
                          : Container(
                              padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
                              decoration: BoxDecoration(
                                color: textwhiteColor,
                                boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.05), spreadRadius: 1, blurRadius: 10, offset: Offset(0, 5))],
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          padding: EdgeInsets.only(left: 5),
                                          width: Get.width,
                                          child: Text(controller.sel_video == null ? "--" : controller.sel_video['title'] ?? "--", style: TextStyle(fontWeight: FontWeight.w800, fontSize: Get.width / 22)),
                                        ),
                                        Container(
                                          padding: EdgeInsets.only(left: 5),
                                          width: Get.width,
                                          child: Text(controller.sel_video == null ? "--" : controller.sel_video['description'] ?? "---------------", style: TextStyle(fontWeight: FontWeight.w300, fontSize: Get.width / 30)),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(width: 15),
                                ],
                              ),
                            ),
                    ],
                  );
                  // }
                  // return SizedBox.shrink();
                },
              ),

              // Tab Bar
              Container(
                margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                child: TabBar(
                  controller: _tabController,
                  indicator: UnderlineTabIndicator(
                    borderRadius: BorderRadius.circular(1000),
                    borderSide: BorderSide(width: 3.0, color: secondaryColor),
                    insets: EdgeInsets.symmetric(horizontal: 40.0),
                  ),
                  labelColor: secondaryColor,
                  unselectedLabelColor: textblackColor,
                  labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: Get.width / 28),
                  unselectedLabelStyle: TextStyle(fontWeight: FontWeight.normal, fontSize: Get.width / 28),
                  indicatorSize: TabBarIndicatorSize.tab,
                  tabs: [
                    Tab(text: "Videos"),
                    Tab(text: "Materials"),
                  ],
                ),
              ),

              // Tab Content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [

                    // Videos Tab
                    _buildVideosTab(controller),

                    // Materials Tab
                    _buildMaterialsTab(controller),
                    // Container()
                  ],
                ),
              ),
            ],
          ),
        ),
        // floatingActionButton: GestureDetector(
        //   onTap: () {
        //     if (vidcontroller.playerController.value.isPlaying) {
        //       vidcontroller.playerController.pause();
        //     }
        //     Get.to(DiscussionForum());
        //   },
        //   child: SizedBox(
        //     width: Get.width / 3,
        //     child: Image.asset('assets/temp_images/discussion_1.png'),
        //   ),
        // ),
      );
    });
  }

  Widget _buildVideosTab(VideoPlayerGetXController controller) {
    return controller.isLessonFileLoading
        ? loader()
        : SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(height: 20),
                controller.lesson_files.isEmpty
                    ? Container(padding: EdgeInsets.all(80), child: Text("No Video's Found"))
                    : Container(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.all(0),
                          shrinkWrap: true,
                          itemCount: controller.lesson_files.length,
                          itemBuilder: (BuildContext context, int index) {
                            Map data = controller.lesson_files[index];
                            return GestureDetector(
                              onTap: () async {
                                if (data['premium_lock'].toString() != "1") {
                                  // if(data['sequence_lock'].toString() != "1"){
                                  controller.selvidindexloading = true;
                                  controller.update();

                                  await Future.delayed(Duration(milliseconds: 500), () {
                                    controller.sel_index = index;
                                    controller.sel_video = data;
                                    controller.selvidindexloading = false;
                                    controller.update();
                                  });
                                  // } else {
                                  //   toast_warning("Complete Previous Lesson");
                                  // }
                                }
                              },
                              child: IntrinsicHeight(
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: Get.width / 7,
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Expanded(
                                            child: Container(
                                              margin: EdgeInsets.only(bottom: 4),
                                              child: Align(
                                                alignment: Alignment.center,
                                                child: DottedLineVertical(
                                                    height: double.infinity,
                                                    color: index == 0
                                                        ? Colors.transparent
                                                        : data['is_completed'].toString() == "1"
                                                            ? secondaryColor
                                                            : primaryColor.withOpacity(0.1)),
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                            height: Get.width / 15,
                                            width: Get.width / 15,
                                            child: data['is_completed'].toString() == "1"
                                                ? Container(decoration: BoxDecoration(borderRadius: BorderRadius.circular(10000), color: secondaryColor), child: Icon(Icons.check, color: textwhiteColor, size: 15))
                                                : CircularStepProgressIndicator(
                                                    stepSize: 3.5,
                                                    totalSteps: 100,
                                                    unselectedColor: textblackColor.withValues(alpha: 0.02),
                                                    selectedStepSize: 3.5,
                                                    selectedColor: Color(0xff329661),
                                                    roundedCap: (i, bi) {
                                                      return true;
                                                    },
                                                    currentStep: int.parse("0"),
                                                  ),
                                          ),
                                          Expanded(
                                            child: Container(
                                              margin: EdgeInsets.only(top: 4),
                                              child: Align(
                                                alignment: Alignment.center,
                                                child: DottedLineVertical(
                                                    height: double.infinity,
                                                    color: index == controller.lesson_files.length - 1
                                                        ? Colors.transparent
                                                        : data['is_completed'].toString() == "1"
                                                            ? secondaryColor
                                                            : primaryColor.withOpacity(0.1)),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: Container(
                                        margin: EdgeInsets.only(bottom: 10),
                                        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10),
                                          border: Border.all(color: index == controller.sel_index ? secondaryColor.withOpacity(0.4) : textblackColor.withOpacity(0.1)),
                                          color: index == controller.sel_index ? secondaryColor.withOpacity(0.1) : textwhiteColor,
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  SizedBox(width: Get.width, child: Text('Video  - ${index + 1}', style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 30), maxLines: 2)),
                                                  const SizedBox(height: 5),
                                                  SizedBox(width: Get.width, child: Text(data['title'].toString(), style: TextStyle(color: textblackColor, fontWeight: FontWeight.w400, fontSize: Get.width / 25, fontFamily: font_medium))),
                                                  SizedBox(height: 5),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                            data['premium_lock'].toString() == "1"
                                                ? Container(padding: EdgeInsets.all(10), decoration: BoxDecoration(color: Colors.grey.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(100)), child: Icon(Icons.lock_outline, size: 20))
                                                : Icon(controller.sel_index == index ? Icons.pause_circle_filled_rounded : Icons.play_circle_outline_rounded, size: 28)
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
              ],
            ),
          );
  }

  Widget _buildMaterialsTab(VideoPlayerGetXController controller) {
    // You'll need to add lesson_materials array to your controller
    // For now, I'm using a placeholder
    List materials = controller.materialList ?? []; // Add this to your controller

    // print("New Material Length : " + controller.materialList.length.toString());
    return controller.isMaterialLoading
        ? loader()
        : SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(height: 20),
                materials.isEmpty
                    ? Container(
                        padding: EdgeInsets.all(80),
                        child: Column(
                          children: [
                            Icon(Icons.description_outlined, size: 64, color: Colors.grey),
                            SizedBox(height: 16),
                            Text("No Materials Found", style: TextStyle(color: Colors.grey, fontSize: 16)),
                          ],
                        ),
                      )
                    : Container(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: ListView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.all(0),
                          shrinkWrap: true,
                          itemCount: materials.length,
                          itemBuilder: (BuildContext context, int index) {
                            Map material = materials[index];
                            return GestureDetector(
                              onTap: () {
                                if (material['premium_lock'].toString() != "1") {
                                  // if (material['sequence_lock'].toString() != "1") {
                                  if (Platform.isIOS) {
                                    if (vidcontroller.playerController.value.isPlaying) {
                                      vidcontroller.playerController.pause();
                                    } else {
                                      try {
                                        vidcontroller.betterPlayerController.pause();
                                      } catch (e) {
                                        print(e.toString());
                                      }
                                    }
                                  }

                                  Get.to(() => pdfviewer_new(
                                        pdf_url: material['lesson_url'].toString(),
                                        title: material['title'].toString(),
                                        id: material['id'].toString(),
                                      ));
                                  // } else {
                                  //   toast_warning("Complete Previous Lesson");
                                  // }
                                }
                              },
                              child: Container(
                                margin: EdgeInsets.only(bottom: 10),
                                child: Container(
                                  padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(color: textblackColor.withOpacity(0.1)),
                                    color: textwhiteColor,
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(12),
                                        decoration: BoxDecoration(color: secondaryColor.withOpacity(0.1), borderRadius: BorderRadius.circular(8)),
                                        child: Icon(_getMaterialIcon(material['lesson_type'] ?? 'document'), color: secondaryColor, size: 24),
                                      ),
                                      SizedBox(width: 15),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              material['title']?.toString() ?? 'Material ${index + 1}',
                                              style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 25),
                                            ),
                                            SizedBox(height: 5),
                                            Text(material['short_description']?.toString() ?? '--', style: TextStyle(color: textblackColor.withOpacity(0.6), fontSize: Get.width / 30), maxLines: 2, overflow: TextOverflow.ellipsis),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 10),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
              ],
            ),
          );
  }

  IconData _getMaterialIcon(String type) {
    switch (type.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'image':
      case 'jpg':
      case 'png':
        return Icons.image;
      case 'video':
      case 'mp4':
        return Icons.video_file;
      case 'audio':
      case 'mp3':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
        return Icons.archive;
      default:
        return Icons.description;
    }
  }

  void _handleMaterialTap(Map material) {
    // Implement material download/view logic here
    // You might want to open a URL, download a file, or show a preview
    print('Material tapped: ${material['title']}');

    // Example implementation:
    // if (material['url'] != null) {
    //   // Open URL or download file
    //   launch(material['url']);
    // }
  }
}
