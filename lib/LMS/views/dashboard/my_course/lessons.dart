import 'package:dotted_line/dotted_line.dart';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/LMS/views/dashboard/my_course/lesson_files.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';

import 'package:edutalim/components/utils.dart';
import '../../../../components/dotted_line.dart';
import '../../../controllers/mycoursecontroller.dart';
import 'lesson_files_better_player.dart';

class lessons extends StatefulWidget {
  String subject_id, sub_title;

  lessons({required this.subject_id, required this.sub_title});

  @override
  State<lessons> createState() => _lesson_filesState();
}

class _lesson_filesState extends State<lessons> {
  Mycoursecontroller controller = Get.put(Mycoursecontroller());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    controller.get_lessons(widget.subject_id.toString());
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Mycoursecontroller>(builder: (controller) {
      return Scaffold(
        backgroundColor: textwhiteColor,
        body: Container(
          width: Get.width,
          height: Get.height,
          decoration: BoxDecoration(),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/bg/lessons_head.webp'),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Column(
                  children: [
                    AppBar(
                      backgroundColor: appbarblackColor,
                      surfaceTintColor: appbarblackColor,
                      elevation: 0,
                      iconTheme: IconThemeData(
                        color: textwhiteColor,
                      ),
                      title: Text(
                        "Lessons",
                        style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontSize: Get.width / 20),
                      ),
                    ),
                    SizedBox(
                      height: 50,
                    ),
                    Container(
                        alignment: Alignment.centerLeft,
                        // color: Colors.yellow,
                        padding: EdgeInsets.symmetric(horizontal: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              child: Text(
                                widget.sub_title.toString(),
                                style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontSize: Get.width / 22),
                              ),
                            ),
                            SizedBox(
                              height: 5,
                            ),
                            Container(
                              child: Text(
                                controller.isLessonsLoading ? "Loading..." : "${controller.lessons.length} Lessons",
                                style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w400, fontSize: Get.width / 24),
                              ),
                            ),
                            SizedBox(
                              height: Get.height / 20,
                            ),
                          ],
                        )),
                  ],
                ),
              ),
              Expanded(
                child: controller.isLessonsLoading
                    ? loader()
                    : SingleChildScrollView(
                        child: Column(
                          children: [
                            SizedBox(
                              height: 20,
                            ),
                            controller.lessons.isEmpty
                                ? Container(
                                    padding: EdgeInsets.symmetric(horizontal: 80, vertical: 80),
                                    child: Text(
                                      "No Lessons Found",
                                      style: TextStyle(fontFamily: font_semibold, color: Colors.grey),
                                    ),
                                  )
                                : Container(
                                    padding: EdgeInsets.only(right: 20),
                                    child: ListView.builder(
                                      physics: NeverScrollableScrollPhysics(),
                                      padding: EdgeInsets.all(0),
                                      shrinkWrap: true,
                                      itemCount: controller.lessons.length,
                                      itemBuilder: (BuildContext context, int index) {
                                        Map data = controller.lessons[index];

                                        return IntrinsicHeight(
                                          child: Row(
                                            children: [
                                              SizedBox(
                                                width: Get.width / 7,
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.center,
                                                  children: [
                                                    Expanded(
                                                      child: Container(
                                                        margin: EdgeInsets.only(bottom: 4),
                                                        child: Align(
                                                          alignment: Alignment.center,
                                                          child: DottedLineVertical(
                                                            height: double.infinity,
                                                            color: index == 0
                                                                ? Colors.transparent
                                                                : data['sequence_lock'].toString() == "0"
                                                                    ? Color(0xff329661)
                                                                    : primaryColor.withOpacity(0.4),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: Get.width / 15,
                                                      width: Get.width / 15,
                                                      child: data['sequence_lock'].toString() == "0"
                                                          ? Container(
                                                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(10000), color: Color(0xff329661)),
                                                              child: Icon(Icons.check, color: textwhiteColor, size: 15),
                                                            )
                                                          : CircularStepProgressIndicator(
                                                              stepSize: 3.5,
                                                              totalSteps: 100,
                                                              unselectedColor: textblackColor.withValues(alpha: 0.02),
                                                              selectedStepSize: 3.5,
                                                              selectedColor: Color(0xff329661),
                                                              roundedCap: (i, bi) {
                                                                return true;
                                                              },
                                                              currentStep: int.parse(data['progress'].toString()),
                                                            ),
                                                    ),
                                                    Expanded(
                                                      child: Container(
                                                        margin: EdgeInsets.only(top: 4),
                                                        child: Align(
                                                          alignment: Alignment.center,
                                                          child: DottedLineVertical(
                                                              height: double.infinity,
                                                              color: index == controller.lessons.length - 1
                                                                  ? Colors.transparent
                                                                  : data['sequence_lock'].toString() == "0"
                                                                      ? Color(0xff329661)
                                                                      : primaryColor.withOpacity(0.4)),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Expanded(
                                                child: GestureDetector(
                                                  onTap: () {
                                                    Get.to(() => lesson_files(lesson: data))!.then((_){ controller.get_lessons(widget.subject_id.toString());});
                                                    // Get.to(()=> lesson_files_better_player(lesson_id: data['id'].toString(), title: data['title'].toString()));
                                                  },
                                                  child: Container(
                                                    padding: EdgeInsets.all(17),
                                                    margin: EdgeInsets.only(bottom: 10),
                                                    decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(10),
                                                      color: textwhiteColor,
                                                      boxShadow: [
                                                        BoxShadow(color: Colors.black.withOpacity(0.1), spreadRadius: 1, blurRadius: 10, offset: Offset(0, 5)),
                                                      ],
                                                    ),
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Text(data['title'].toString(), style: TextStyle(color: textblackColor, fontWeight: FontWeight.w600, fontSize: Get.width / 25), maxLines: 2),
                                                        SizedBox(height: 15),
                                                        Row(
                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                          children: [
                                                            Row(
                                                              children: [
                                                                Icon(Icons.play_circle_filled_outlined, color: textblackColor.withOpacity(0.3), size: 20),
                                                                SizedBox(width: 7),
                                                                Text("${data['videos_count'].toString()} Videos",
                                                                    style: TextStyle(color: textblackColor.withOpacity(0.5), fontWeight: FontWeight.w400, fontSize: Get.width / 27)),
                                                              ],
                                                            ),
                                                            data['premium_lock'].toString() == "1"
                                                                ? Container(
                                                                    padding: EdgeInsets.all(10),
                                                                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(1000), color: Colors.grey.withValues(alpha: 0.05)),
                                                                    child: Icon(Icons.lock_outline, color: textblackColor.withOpacity(0.8), size: 15),
                                                                  )
                                                                : Icon(Icons.keyboard_arrow_right, color: textblackColor.withOpacity(0.8), size: 25),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                            SizedBox(
                              height: 30,
                            ),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
