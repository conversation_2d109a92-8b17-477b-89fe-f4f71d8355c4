import 'package:edutalim/components/utils.dart';
import 'package:edutalim/LMS/views/chat/controller/chatcontroller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../components/constants.dart';
import 'group_chat_screen.dart';

class ChatListPage extends StatefulWidget{


  @override
  State<StatefulWidget> createState() => _ChatListPage();

}


class _ChatListPage extends State<StatefulWidget>{

  ChatController controller = Get.put(ChatController());


  @override
  Widget build(BuildContext context) {

    return GetBuilder<ChatController>(builder: (controller) {
        return Scaffold(

          backgroundColor: textwhiteColor,


          body:
          controller.isChatListLoading ? loader() :
          Column(
            children: [

              Container(
                decoration: BoxDecoration(
                  color: primaryColor,
                  borderRadius: BorderRadius.vertical(bottom: Radius.circular(35))
                ),
                child: Column(
                  children: [
                    AppBar(
                      backgroundColor: appbarblackColor,
                      surfaceTintColor: appbarblackColor,
                      centerTitle: false,
                      iconTheme: IconThemeData(
                        color: textwhiteColor,
                      ),
                      title: Text('Chats',style: TextStyle(color: textwhiteColor,fontFamily: font_bold,fontSize: Get.width/15),textAlign: TextAlign.center,),
                    ),

                    const SizedBox(height: 10,),


                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 15),
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              width: Get.width,
                              child: TextField(
                                onChanged: (text){ controller.filterChat(text);},
                                controller: controller.searchController,
                                keyboardType: TextInputType.emailAddress,
                                style: TextStyle(fontSize: Get.width/28, color: Colors.black, fontFamily: font_regular),
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(borderSide: const BorderSide(color: Colors.transparent,width: 1), borderRadius: BorderRadius.circular(1000),),
                                  enabledBorder: OutlineInputBorder(borderSide: const BorderSide(color: Colors.transparent,width: 1), borderRadius: BorderRadius.circular(1000),),
                                  filled: true,
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 5),
                                  hintStyle: TextStyle(fontFamily: font_regular,color: Colors.black38,fontSize: Get.width/28),
                                  hintText: "Search",
                                  fillColor: textwhiteColor,
                                  prefixIcon: Container(
                                    padding: const EdgeInsets.all(14),
                                    child: Image.asset('assets/icons/search.png',width: 0,color: Colors.black54,),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          // SizedBox(width: 10,),
                          // GestureDetector(
                          //   child: Container(
                          //     decoration: BoxDecoration(
                          //       borderRadius: BorderRadius.circular(1000),
                          //       color: textwhiteColor,
                          //     ),
                          //     width: Get.width/9,
                          //     height: Get.width/9,
                          //     padding: EdgeInsets.all(10),
                          //     child: Image.asset('assets/icons/sort.png',width: Get.width/20,color: primaryColor.withValues(alpha: 0.5),),
                          //   ),
                          // ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 10,),
                  ],
                ),
              ),

              Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [

                        ListView.separated(
                          padding: const EdgeInsets.only(bottom: 10,top: 10),
                          // itemCount: controller.chatFilterList.length,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: chatlist.length,
                          shrinkWrap: true,
                          itemBuilder: (_,index){

                            var data = chatlist[index];

                            return InkWell(
                              onTap: (){

                                Get.to(() => group_chat_screen(
                                    id: '3',
                                    title: data['name'].toString(),
                                  data: data,
                                  ),
                                  transition: Transition.downToUp,
                                  duration: const Duration(milliseconds: 500),
                                );

                              },
                              child: Container(
                                padding: const EdgeInsets.only(left: 20,right: 20,top: 12,bottom: 12),
                                child: Column(
                                  children: [
                                    IntrinsicHeight(
                                      child: Row(
                                        children: [


                                          ClipRRect(
                                            borderRadius: BorderRadius.circular(1000),
                                            child: Container(
                                              width: Get.width/8,
                                              height:Get.width/8,
                                              child: Image.asset(
                                                data['image'].toString(),
                                                fit: BoxFit.cover,
                                                errorBuilder: (a,b,c){
                                                  return Image.asset("assets/images/avatar_placeholder.png",);
                                                },
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 15,),


                                          // Expanded(
                                          //     child: Column(
                                          //   crossAxisAlignment: CrossAxisAlignment.start,
                                          //   children: [
                                          //
                                          //   Text(data['chat_name'].toString(),style: TextStyle(fontSize: Get.width/30,fontFamily: font_medium),),
                                          //   const SizedBox(height: 5,),
                                          //   Row(
                                          //     children: [
                                          //       // Text("${data['last_msg_user'].toString()} : ",style: TextStyle(fontSize: Get.width/40,fontFamily: font_regular,color: textblackColor.withOpacity(0.5)),),
                                          //       Text("${controller.chat[controller.chat.length-1]['last_message_sender'].toString()} : ",style: TextStyle(fontSize: Get.width/40,fontFamily: font_regular,color: textblackColor.withOpacity(0.5)),),
                                          //       // Text(data['last_msg'].toString(),style: TextStyle(fontSize: Get.width/40,fontFamily: font_regular,color: textblackColor.withOpacity(0.5)),),
                                          //       controller.chat[controller.chat.length-1]['last_message'].toString().isEmpty && controller.chat[controller.chat.length-1]['type'].toString() == "image"
                                          //       ? Icon(Icons.image,size: 12,color: Colors.black45,)
                                          //       : Text(controller.chat[controller.chat.length-1]['last_message_on'].toString(),style: TextStyle(fontSize: Get.width/40,fontFamily: font_regular,color: textblackColor.withOpacity(0.5)),),
                                          //     ],
                                          //   ),
                                          //
                                          // ],),
                                          // ),



                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [

                                                Text(data['name'].toString(),style: TextStyle(fontSize: Get.width/30),),
                                                const SizedBox(height: 5,),
                                                Row(
                                                  children: [
                                                    // Text("${data['last_msg_user'].toString()} : ",style: TextStyle(fontSize: Get.width/40,fontFamily: font_regular,color: textblackColor.withOpacity(0.5)),),
                                                    Text(data['last_message'].toString()
                                                      ,style: TextStyle(fontSize: Get.width/40,fontFamily: font_regular,color: textblackColor.withOpacity(0.5)),),
                                                    // Text(data['last_msg'].toString(),style: TextStyle(fontSize: Get.width/40,fontFamily: font_regular,color: textblackColor.withOpacity(0.5)),),
                                                    // controller.chat[controller.chat.length-1]['last_message'].toString().isEmpty && controller.chat[controller.chat.length-1]['type'].toString() == "image"
                                                    //     ? Icon(Icons.image,size: 12,color: Colors.black45,)
                                                    //     :
                                                    // Text('10-12-2024',
                                                    //         style: TextStyle(fontSize: Get.width/40,fontFamily: font_regular,color: textblackColor.withOpacity(0.5)),),
                                                  ],
                                                ),

                                              ],),
                                          ),



                                          Column(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.end,
                                            mainAxisSize: MainAxisSize.max,
                                            children: [
                                              Text('10:12 AM',style: TextStyle(fontSize: Get.width/40,color: textblackColor.withOpacity(0.7)),),
                                              if(index < 5)
                                              Container(
                                                decoration: BoxDecoration(
                                                  color: Colors.green,
                                                  borderRadius: BorderRadius.circular(1000),
                                                ),
                                                margin: EdgeInsets.only(top: 5),
                                                width: Get.width/19,
                                                height: Get.width/19,
                                                alignment: Alignment.center,
                                                child: Text((index+4).toString(),style: TextStyle(fontSize: Get.width/40,color: textwhiteColor),),
                                              ),
                                            ],
                                          )

                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }, separatorBuilder: (BuildContext context, int index) {
                          return Padding(
                            padding: EdgeInsets.only(left: Get.width/5,right: 10,bottom: 0),
                            child: Container(color: primaryColor.withOpacity(0.5),height: 0.3,),
                          );
                        },
                        ),

                        SizedBox(height: 0,),

                      ],
                    ),
                  ),
              )

            ],
          ),



        );
      }
    );

  }


  List chatlist = [
    {
      'name': 'Lijosh',
      'gender': 'Male',
      'image': 'assets/temp_images/stud_1.png',
      'last_message': "What's the new Lesson plan ? "
    },
    {
      'name': 'Ameer',
      'gender': 'Male',
      'image': 'assets/temp_images/stud_2.png',
      'last_message': 'Hi Where are You'
    },
    // {
    //   'name': 'Liam',
    //   'gender': 'Male',
    //   'image': 'assets/temp_images/stud_3.png',
    //   'last_message': 'I finished my homework!'
    // },
    // {
    //   'name': 'Ava',
    //   'gender': 'Female',
    //   'image': 'assets/temp_images/stud_4.png',
    //   'last_message': 'Let’s draw something fun!'
    // },
    // {
    //   'name': 'Ethan',
    //   'gender': 'Male',
    //   'image': 'assets/temp_images/stud_5.png',
    //   'last_message': 'I built a cool LEGO house!'
    // },
    // {
    //   'name': 'Mia',
    //   'gender': 'Female',
    //   'image': 'assets/temp_images/stud_6.png',
    //   'last_message': 'I got a new coloring book!'
    // },
    // {
    //   'name': 'Emma',
    //   'gender': 'Female',
    //   'image': 'assets/temp_images/stud_7.png',
    //   'last_message': 'Hey! Let’s play later!'
    // },
    // {
    //   'name': 'Mason',
    //   'gender': 'Male',
    //   'image': 'assets/temp_images/stud_8.png',
    //   'last_message': 'Can we go to the park?'
    // },
    // {
    //   'name': 'Isabella',
    //   'gender': 'Female',
    //   'image': 'https://example.com/isabella.jpg',
    //   'last_message': 'I love my teddy bear!'
    // },
    // {
    //   'name': 'Oliver',
    //   'gender': 'Male',
    //   'image': 'https://example.com/oliver.jpg',
    //   'last_message': 'Look! I learned how to ride a bike!'
    // },
    // {
    //   'name': 'Olivia',
    //   'gender': 'Female',
    //   'image': 'https://example.com/olivia.jpg',
    //   'last_message': 'Can we watch cartoons together?'
    // },
    // {
    //   'name': 'Elijah',
    //   'gender': 'Male',
    //   'image': 'https://example.com/elijah.jpg',
    //   'last_message': 'I love ice cream!'
    // }
  ];


}

