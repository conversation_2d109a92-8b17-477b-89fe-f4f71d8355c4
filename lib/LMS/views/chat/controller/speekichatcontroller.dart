import 'dart:async';
import 'package:edutalim/components/constants.dart';
import 'package:edutalim/LMS/views/chat/apis/speekichat_apis.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Speekichatcontroller extends GetxController {
  // PlayerController wavecontroller = PlayerController(); // Initialise

  // prepare_wave(source) async {
  //   // Initialise
  //   // Extract waveform data
  //   final waveformData = await wavecontroller.extractWaveformData(
  //     path: source,
  //     noOfSamples: 100,
  //   );
  //   // Or directly extract from preparePlayer and initialise audio player
  //   await wavecontroller.preparePlayer(
  //     path: source,
  //     shouldExtractWaveform: true,
  //     noOfSamples: 100,
  //     volume: 1.0,
  //   );
  // }

  int? current_audio_index;

  final ScrollController spchatscrollController = ScrollController();

  void scrollToBottom() {
    spchatscrollController
        .jumpTo(spchatscrollController.position.maxScrollExtent);
  }

  String? scriptdata;
  call_create_script(data) async {
    scriptdata = 'Chat Transcript\n\n';
    for (var i = 0; i < data.length; i++) {
      var type;
      print(data[i].toString());
      if (data[i]['sender'].toString() == 'ai') {
        type = 'SPEEKI Ai : ';
      } else {
        type = 'You : ';
      }
      scriptdata =
          scriptdata.toString() + '\n\n' + type + data[i]['message'].toString();
      update();
    }
    return scriptdata;
  }

  bool istranscriptloading = true;
  List transcripts = [];
  call_transcript(chat_id) async {
    istranscriptloading = true;
    // update();
    var data = await SpeekichatApihandler.fet_transcript(chat_id);
    print(data.toString());
    transcripts = [data['data']];
    istranscriptloading = false;
    update();
  }

  bool audioIconLoading = false;
  bool isChatListloading = true;
  List chatList = [];

  chatListData() async {
    isChatListloading = true;
    update();
    var data = await SpeekichatApihandler.chatList();
    chatList = [data['data']];
    isChatListloading = false;
    update();
  }

  bool issummaruloading = true;
  List chatsummary = [];
  call_chat_summary(chat_id) async {
    issummaruloading = true;
    // update();
    var data = await SpeekichatApihandler.fet_chat_summary(chat_id);
    print(data.toString());
    chatsummary = [data];
    issummaruloading = false;
    update();
  }

  bool show_mic_sec = true,
      is_send_enabled = false,
      chat_reponse_waiting = false;
  FocusNode message_fld_nd = FocusNode();
  TextEditingController message_fld_ctr = TextEditingController();
  List my_messages = [];

  List spchatdata = [];
  bool isspchatloading = false
  ;
  call_chats(language_code, chat_id, reload, need_audio_play) async {
    print('chat_called-------');

    if (reload.toString() == 'true') {
      isspchatloading = true;
      update();
    } else {
      chat_reponse_waiting = true;
    }
    var data = await SpeekichatApihandler.chats(language_code, chat_id);
    spchatdata = [data['data']];
    startTimer(100);
    isspchatloading = false;
    chat_reponse_waiting = false;
    update();

    // ----------------last audio handling----------------------------------
    print('last-----------------------------------------sec---------------');
    // print(spchatdata[0]['chat'][spchatdata[0]['chat'].length - 1]['audio']
    //     .toString());

    if (need_audio_play == 'true') {

      if (spchatdata[0]['chat'][spchatdata[0]['chat'].length - 1]['audio']
              .toString()
              .split('.')
              .last ==
          'mp3') {


        print('inside check if-------');
        // current_audio_index = spchatdata[0]['chat'].length - 1;
        //
        // playerStateChangedSubscription =
        //     chat_audioPlayer.onPlayerComplete.listen((state) async {
        //   await chat_audioPlayer.stop();
        //   update();
        // });
        // positionChangedSubscription = chat_audioPlayer.onPositionChanged.listen(
        //   (position) {
        //     chat_position = position;
        //     update();
        //   },
        // );
        // durationChangedSubscription = chat_audioPlayer.onDurationChanged.listen(
        //   (duration) {
        //     chat_duration = duration;
        //     update();
        //   },
        // );
        //
        // chat_audioPlayer.setSource(ap.UrlSource(spchatdata[0]['chat'][spchatdata[0]['chat'].length - 1]['audio']));
        // update();
        //
        // chat_audioPlayer.play(ap.UrlSource(
        //     spchatdata[0]['chat'][spchatdata[0]['chat'].length - 1]['audio']));

        update();
      } else {
        Get.snackbar(
          '', '',
          titleText: Text(
            'Error',
            style: TextStyle(
                color: textwhiteColor,
                // fontFamily: font_semibold,
                fontSize: Get.width / 27),
          ),
          messageText: Text(
            "Can't Speak right now",
            style: TextStyle(
                color: textwhiteColor,
                fontFamily: font_regular,
                fontSize: Get.width / 33),
          ),
          backgroundColor: Colors.red,
          snackPosition: SnackPosition.BOTTOM,
          // duration: Duration(seconds: 1),
        );
      }
    }

    // ----------------last audio handling----------------------------------
  }

  bool showPlayer = false;
  String? audioPath;

  int recordDuration = 0;
  Timer? timer;
  // late final AudioRecorder audioRecorder;
  // StreamSubscription<RecordState>? recordSub;
  // RecordState recordState = RecordState.stop;
  // StreamSubscription<Amplitude>? amplitudeSub;
  // Amplitude? amplitude;

  bool ischataudiosent = false;
  bool is_detection_success = true;
  call_send_chat(chat_id, language_code, type, audio, word) async {
    if (type == 'audio') {
      Map msg = {'type': 'audio', 'message': audio};
      my_messages.add(msg);
      update();
    } else {
      Map msg = {'type': 'word', 'message': word};
      my_messages.add(msg);
      update();
    }

    print('-------1');

    ischataudiosent = true;
    update();
    print('-------2');
    var data = await SpeekichatApihandler.fet_send_chat(
        chat_id, language_code, type, audio, word);
    print('-------3');
    ischataudiosent = false;

    print('-------4');
    if (data['status'].toString() == '1') {
      is_detection_success = true;
      call_chats(language_code, chat_id, 'false', 'true');
      showPlayer = false;
      update();
    } else if (data['status'].toString() == '0') {
      print('empty audio');
      Get.snackbar(
        '', '',
        titleText: Text(
          'Warning',
          style: TextStyle(
              color: textwhiteColor,
              // fontFamily: font_semibold,
              fontSize: Get.width / 27),
        ),
        messageText: Text(
          data['message'].toString(),
          style: TextStyle(
              color: textwhiteColor,
              fontFamily: font_regular,
              fontSize: Get.width / 33),
        ),
        backgroundColor: Colors.orange,
        snackPosition: SnackPosition.BOTTOM,
        // duration: Duration(seconds: 1),
      );
    } else {
      is_detection_success = false;
      update();
      Get.snackbar(
        '', '',
        titleText: Text(
          'Warning',
          style: TextStyle(
              color: textwhiteColor,
              // fontFamily: font_semibold,
              fontSize: Get.width / 27),
        ),
        messageText: Text(
          data['message'].toString(),
          style: TextStyle(
              color: textwhiteColor,
              fontFamily: font_regular,
              fontSize: Get.width / 33),
        ),
        backgroundColor: Colors.orange,
        snackPosition: SnackPosition.BOTTOM,
        // duration: Duration(seconds: 1),
      );

      call_chats(language_code, chat_id, 'false', 'false');
    }

    update();
    print(data.toString());
  }

  late Timer _timer;
  int _start = 1;

  void startTimer(scrol_seconds) {
    _start = 1;
    var oneSec = Duration(milliseconds: int.parse(scrol_seconds.toString()));
    _timer = Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_start == 0) {
          scrollToBottom();
          timer.cancel();
        } else {
          _start--;
          update();
        }
      },
    );
  }

  static const double _controlSize = 56;
  static const double _deleteBtnSize = 24;

  // final chat_audioPlayer = ap.AudioPlayer()..setReleaseMode(ReleaseMode.stop);
  late StreamSubscription<void> playerStateChangedSubscription;
  late StreamSubscription<Duration?> durationChangedSubscription;
  late StreamSubscription<Duration> positionChangedSubscription;
  Duration? chat_position;
  Duration? chat_duration;

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();

    playerStateChangedSubscription.cancel();
    positionChangedSubscription.cancel();
    durationChangedSubscription.cancel();
    // chat_audioPlayer.dispose();
    super.dispose();
  }
}
