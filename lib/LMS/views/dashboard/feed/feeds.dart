// import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:edutalim/LMS/views/dashboard/feed/reel_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

// import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
// import 'package:share_plus/share_plus.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../controllers/feedcontroller.dart';

class feed extends StatefulWidget {
  @override
  State<feed> createState() => _homeState();
}

class _homeState extends State<feed> {
  Feedcontroller controller = Get.put(Feedcontroller());

  @override
  void initState() {
    super.initState();
    controller.get_feed(true);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Feedcontroller>(builder: (controller) {
      return Scaffold(
        backgroundColor: textwhiteColor,
        body: Container(
          width: Get.width,
          height: Get.height,
          decoration: BoxDecoration(
            color: textwhiteColor,
            // image: DecorationImage(
            //   image: AssetImage('assets/bg/feed.png'),
            //   fit: BoxFit.fitWidth,
            // ),
          ),
          child: Column(
            children: [
              Container(
                child: AppBar(
                  backgroundColor: appbarwhiteColor,
                  surfaceTintColor: appbarwhiteColor,
                  title: Text(
                    "Feed",
                    style: TextStyle(fontFamily: font_semibold, fontSize: Get.width / 20),
                  ),
                ),
              ),
              Expanded(
                child: controller.isFeedLoading
                    ? loader()
                    : SingleChildScrollView(
                        child: Column(
                          children: [
                            // Story new
                            if (controller.feedsData[0]["reels"].isNotEmpty)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Column(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Row(
                                              children: [
                                                Image.asset(
                                                  "assets/icons/video.png",
                                                  height: Get.width / 20,
                                                ),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Container(
                                                  child: Text(
                                                    "SHORT VIDEO",
                                                    style: TextStyle(fontWeight: FontWeight.w700, fontSize: Get.width / 22),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            // GestureDetector(
                                            //   onTap: (){
                                            //     // Get.to(all_recommended_courses());
                                            //   },
                                            //   child: Container(
                                            //     color: Colors.transparent,
                                            //     child: Text(
                                            //       "VIEW ALL",
                                            //       style: TextStyle(fontWeight: FontWeight.w500, fontSize: Get.width / 28, color: textblackColor.withOpacity(0.6), decoration: TextDecoration.underline, decorationColor: primaryColor),
                                            //     ),
                                            //   ),
                                            // ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  Column(
                                    children: [
                                      SizedBox(
                                        height: 10,
                                      ),
                                      Container(
                                        // padding: EdgeInsets.symmetric(horizontal: 8),
                                        color: Colors.transparent,
                                        child: Container(
                                          // color: Colors.yellow,
                                          height: 180,
                                          child: ListView.builder(
                                            // padding: EdgeInsets.all(0),
                                            // physics: ScrollPhysics(),
                                            scrollDirection: Axis.horizontal,
                                            shrinkWrap: true,
                                            itemCount: controller.feedsData[0]["reels"].length,
                                            // itemCount: 2,
                                            padding: EdgeInsets.symmetric(horizontal: 10),
                                            itemBuilder: (context, index) {
                                              var data = controller.feedsData[0]["reels"][index];
                                              return GestureDetector(
                                                onTap: () {
                                                  Get.to(() => reel_page(
                                                        data: controller.feedsData[0]["reels"],
                                                        index: index,
                                                        // data: [
                                                        //   {
                                                        //     "id": "26",
                                                        //     "title": "Edu Thalim",
                                                        //     "thumbnail": "https://i.ytimg.com/vi/9e4BGAmdqhw/oar2.jpg?sqp=-oaymwEoCJUDENAFSFqQAgHyq4qpAxcIARUAAIhC2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLDeGOrFLNaroAz0F0Pt-CIyPv79Pw",
                                                        //     "uploaded_video": "",
                                                        //     "description": null,
                                                        //     "author": null,
                                                        //     "video_url": "https://trogon.info/demo/demovideos/thalim/reel_1.mp4"
                                                        //   },
                                                        //   {
                                                        //     "id": "26",
                                                        //     "title": "Edu Thalim",
                                                        //     "thumbnail": "https://i.ytimg.com/vi/9e4BGAmdqhw/oar2.jpg?sqp=-oaymwEoCJUDENAFSFqQAgHyq4qpAxcIARUAAIhC2AEB4gEKCBgQAhgGOAFAAQ==&rs=AOn4CLDeGOrFLNaroAz0F0Pt-CIyPv79Pw",
                                                        //     "uploaded_video": "",
                                                        //     "description": null,
                                                        //     "author": null,
                                                        //     "video_url": "https://trogon.info/demo/demovideos/thalim/reel_2.mp4"
                                                        //   },
                                                        // ],
                                                      ));
                                                },
                                                child: Container(
                                                  // padding: EdgeInsets.symmetric(horizontal: 1),
                                                  margin: EdgeInsets.only(left: index == 0 ? 5 : 5, right: index == controller.feedsData[0]["reels"].length - 1 ? 5 : 5),
                                                  // margin: EdgeInsets.all(10),
                                                  child: Container(
                                                    child: Stack(
                                                      children: [
                                                        ClipRRect(
                                                            borderRadius: BorderRadius.all(Radius.circular(10)),
                                                            child: CachedNetworkImage(
                                                              imageUrl: data['thumbnail_url'].toString(),
                                                              errorWidget: (_, __, ____) {
                                                                return Image.asset("assets/logo/logo.png");
                                                              },
                                                              width: Get.width / 3.5,
                                                              height: 240,
                                                              fit: BoxFit.cover,
                                                            )),
                                                        Positioned(
                                                          left: 0,
                                                          right: 0,
                                                          top: 0,
                                                          bottom: 0,
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              borderRadius: BorderRadius.all(Radius.circular(10)),
                                                              gradient: LinearGradient(
                                                                  colors: [Color(0xFF215155), Color(0xFF35666A).withValues(alpha: 0.02), Colors.transparent],
                                                                  begin: Alignment.bottomCenter,
                                                                  end: Alignment.topCenter,
                                                                  stops: [0, 0.6, 1]),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 10,
                                  )
                                ],
                              ),

                            // feed
                            if (controller.feedsData[0]['feeds'].isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.all(15),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      child: Text(
                                        "LATEST NEWS",
                                        style: TextStyle(fontFamily: font_semibold, fontSize: Get.width / 22),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(horizontal: 0),
                                      margin: EdgeInsets.only(top: 10),
                                      child: ListView.builder(
                                        physics: ScrollPhysics(),
                                        padding: EdgeInsets.all(0),
                                        shrinkWrap: true,
                                        itemCount: controller.feedsData[0]['feeds'].length,
                                        itemBuilder: (BuildContext context, int index) {
                                          Map data = controller.feedsData[0]['feeds'][index];
                                          return GestureDetector(
                                            onTap: () {},
                                            child: Container(
                                              child: Container(
                                                width: Get.width,
                                                decoration: BoxDecoration(
                                                    color: textwhiteColor,
                                                    border: Border.all(color: textblackColor.withOpacity(0.1)),
                                                    borderRadius: BorderRadius.circular(20),
                                                    boxShadow: [BoxShadow(spreadRadius: 1, blurRadius: 5, color: textblackColor.withOpacity(0.1))]),
                                                padding: EdgeInsets.all(10),
                                                margin: EdgeInsets.only(bottom: 10),
                                                child: Column(
                                                  children: [
                                                    if (data['media_type'].toString() != "text")
                                                      Container(
                                                        width: Get.width,
                                                        child: ClipRRect(
                                                          borderRadius: BorderRadius.circular(10),
                                                          child: data['media_type'].toString() == "video"
                                                              ? Image.network(
                                                                  data['thumbnail_url'].toString(),
                                                                  fit: BoxFit.fitWidth,
                                                                  errorBuilder: (a, b, c) {
                                                                    return Image.asset('assets/images/placeholder_banner.webp');
                                                                  },
                                                                )
                                                              : Image.network(
                                                                  data['media_url'].toString(),
                                                                  fit: BoxFit.fitWidth,
                                                                  errorBuilder: (a, b, c) {
                                                                    return Image.asset('assets/images/placeholder_banner.webp');
                                                                  },
                                                                ),
                                                        ),
                                                      ),
                                                    SizedBox(height: 15),
                                                    Container(
                                                      width: Get.width,
                                                      child: Text(data['title'].toString(), style: TextStyle(fontFamily: font_bold, fontSize: Get.width / 24)),
                                                    ),
                                                    SizedBox(height: 10),
                                                    SizedBox(
                                                      width: Get.width,
                                                      child: Text(
                                                        removehtml_tags(data['content'].toString()),
                                                        style: TextStyle(color: textblackColor, fontSize: Get.width / 29, fontWeight: FontWeight.w300, fontFamily: ''),
                                                      ),
                                                    ),
                                                    SizedBox(height: 30),
                                                    Row(
                                                      children: [
                                                        GestureDetector(
                                                          child: Row(
                                                            children: [
                                                              GestureDetector(
                                                                onTap: () {
                                                                  controller.like_feed(data['id'].toString());
                                                                },
                                                                child: Container(
                                                                  decoration: BoxDecoration(
                                                                    color: data['is_liked'].toString() == "1" ? primarylightColor : textwhiteColor,
                                                                    borderRadius: BorderRadius.circular(1000),
                                                                    border: Border.all(color: primaryColor),
                                                                  ),
                                                                  height: Get.width / 13,
                                                                  width: Get.width / 13,
                                                                  padding: EdgeInsets.all(8),
                                                                  child: Image.asset(
                                                                    'assets/icons/like.png',
                                                                    color: data['is_liked'].toString() == "1" ? textwhiteColor : primaryColor,
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                width: 10,
                                                              ),
                                                              Text(
                                                                data['likes_count'].toString(),
                                                                style: TextStyle(fontFamily: font_semibold, fontSize: Get.width / 28),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          width: 25,
                                                        ),
                                                        GestureDetector(
                                                          onTap: () {
                                                            Share.share("Date : ${data['date']}\n${data['title']}\n\n${removehtml_tags(data['content'].toString())}\n\n$appName\nhttps://www.edutalim.com/");
                                                          },
                                                          child: Container(
                                                            color: Colors.transparent,
                                                            width: Get.width / 12,
                                                            child: Image.asset(
                                                              'assets/icons/share_feed.png',
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                            SizedBox(
                              height: Get.height / 6,
                            ),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
