import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:get_storage/get_storage.dart';

class CourseCard extends StatelessWidget {
  final Map<String, dynamic> data;
  final Color textwhiteColor;
  final Color textblackColor;
  final Color secondarylightColor;

  const CourseCard({
    Key? key,
    required this.data,
    required this.textwhiteColor,
    required this.textblackColor,
    required this.secondarylightColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width / 2.1,
      margin: const EdgeInsets.only(right: 10, bottom: 5),
      decoration: BoxDecoration(color: textwhiteColor, borderRadius: BorderRadius.circular(12), boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.2), spreadRadius: 2, blurRadius: 5, offset: Offset(1, 3))]),
      child: Column(
        children: [
          const SizedBox(height: 5),
          Container(
            height: Get.width / 4.4,
            padding: EdgeInsets.symmetric(horizontal: 10),
            child: ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(12)),
              child: Image.network(
                data['thumbnail'].toString(),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Image.asset(width: Get.width, 'assets/images/placeholder_square.png', fit: BoxFit.cover);
                },
              ),
            ),
          ),
          const SizedBox(height: 10),
          Container(
            width: Get.width / 2.1,
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Text(
              data['title'].toString(),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 29, color: textblackColor),
            ),
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox(width: 5),
              Icon(CupertinoIcons.timer, size: 14, color: textblackColor),
              Text(' Duration : ', style: TextStyle(fontWeight: FontWeight.w500, fontSize: Get.width / 33, color: textblackColor)),
              Expanded(child: Text(data['course_duration'] ?? '-', style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 33, color: textblackColor))),
            ],
          ),
          if(GetStorage().read('pay_version').toString() == "false")
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: DottedLine(
              direction: Axis.horizontal,
              alignment: WrapAlignment.center,
              lineLength: double.infinity,
              lineThickness: 1.0,
              dashLength: 4.0,
              dashColor: secondarylightColor.withOpacity(0.7),
              dashGapLength: 3.0,
              dashGapColor: Colors.transparent,
            ),
          ),

          if(GetStorage().read('pay_version').toString() == "false")
          Container(
            width: Get.width / 2.1,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              children: [
                Text("₹ ${data['offer_price']}", maxLines: 1, overflow: TextOverflow.ellipsis, style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 30, color: textblackColor)),
                const SizedBox(width: 5),
                Text("₹ ${data['price']}",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(fontWeight: FontWeight.w600, fontSize: Get.width / 30, color: Color(0xFFAFAFAF), decoration: TextDecoration.lineThrough, decorationColor: textblackColor)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
