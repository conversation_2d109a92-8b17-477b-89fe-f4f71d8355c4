import 'package:edutalim/components/constants.dart';
import 'package:edutalim/LMS/views/chat/controller/chatcontroller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class group_chat_screen extends StatefulWidget {
  String id, title;
  Map data;

  group_chat_screen({required this.id, required this.title, required this.data});

  @override
  State<group_chat_screen> createState() => _chat_screenState();
}

class _chat_screenState extends State<group_chat_screen> {
  ChatController controller = Get.put(ChatController());

  @override
  void initState() {
    // controller.scrollToBottom();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChatController>(builder: (controller) {
      return GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          appBar: AppBar(
            backgroundColor: textwhiteColor,
            surfaceTintColor: textwhiteColor,
            // leading: GestureDetector(
            //   onTap: () {
            //     Get.back();
            //   },
            //   child: Container(
            //     padding: EdgeInsets.all(5),
            //     child: Icon(Icons.arrow_back_ios,color: textblackColor.withOpacity(0.5),),
            //   ),
            // ),
            title: Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(1000),
                    child: Container(
                      width: Get.width / 8,
                      height: Get.width / 8,
                      child: Image.asset(
                        widget.data['image'].toString(),
                        fit: BoxFit.cover,
                        errorBuilder: (a, b, c) {
                          return Image.asset(
                            "assets/images/avatar_placeholder.png",
                          );
                        },
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        width: Get.width / 1.7,
                        child: Text(
                          widget.data['name'].toString(),
                          style: TextStyle(
                              color: textblackColor,
                              // fontFamily: font_medium,
                              fontSize: Get.width / 26),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      SizedBox(
                        height: 3,
                      ),
                      Container(
                        alignment: Alignment.centerLeft,
                        width: Get.width / 1.7,
                        child: Text(
                          'John, Alia, julie, Joseph, Andrew, Anfal, john..',
                          style: TextStyle(color: textblackColor, fontFamily: font_regular, fontSize: Get.width / 36),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            actions: [
              // Image.asset(
              //   'assets/icons/more_vert.png',
              //   height: Get.width / 19,
              // ),
              // SizedBox(
              //   width: 15,
              // ),
            ],
          ),
          body: Container(
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    color: primaryColor.withOpacity(0.1),
                    child: Stack(
                      children: [
                        SingleChildScrollView(
                          controller: controller.chatScrollController,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 15),
                            child: ListView.builder(
                              physics: const ScrollPhysics(),
                              padding: EdgeInsets.only(bottom: Get.height / 5.5),
                              shrinkWrap: true,
                              itemCount: controller.chat.length,
                              itemBuilder: (BuildContext context, int index) {
                                var data = controller.chat[index];
                                return GestureDetector(
                                    onTap: () {},
                                    child: Container(
                                      alignment: data['send'].toString() != "you" ? Alignment.centerLeft : Alignment.centerRight,
                                      margin: EdgeInsets.symmetric(vertical: 12),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment: data['send'].toString() != "you" ? CrossAxisAlignment.start : CrossAxisAlignment.end,
                                        children: [
                                          // receiver profile-----------------------------------
                                          // if (data['send'].toString() != "you")
                                          //   Container(
                                          //     margin:
                                          //         EdgeInsets.only(right: 13),
                                          //     child: Image.asset(
                                          //       'assets/temp_img/profile.png',
                                          //       width: Get.width / 10,
                                          //     ),
                                          //   ),
                                          Container(
                                            decoration: BoxDecoration(
                                              color: data['send'].toString() != "you" ? secondaryColor.withOpacity(0.8) : textwhiteColor,
                                              borderRadius: BorderRadius.only(
                                                topRight: Radius.circular(20),
                                                bottomRight: Radius.circular(data['send'].toString() != "you" ? 20 : 0),
                                                topLeft: Radius.circular(data['send'].toString() != "you" ? 0 : 20),
                                                bottomLeft: Radius.circular(20),
                                              ),
                                            ),
                                            constraints: BoxConstraints(
                                              maxWidth: Get.width / 1.35,
                                            ),
                                            padding: EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10),
                                            child: IntrinsicWidth(
                                              child: Column(
                                                crossAxisAlignment: data['send'].toString() == 'you' ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                                                children: [
                                                  // if(index.isOdd)
                                                  // Text(
                                                  //   data['send'].toString(),
                                                  //   style: TextStyle(
                                                  //       color: textblackColor,
                                                  //       // fontFamily: font_medium,
                                                  //       fontSize: Get.width / 40),
                                                  //   textAlign: TextAlign.end,
                                                  // ),
                                                  // SizedBox(
                                                  //   height: 3,
                                                  // ),

                                                  if (data['file'].toString() != "null")
                                                    Column(
                                                      children: [
                                                        const SizedBox(
                                                          height: 5,
                                                        ),
                                                        GestureDetector(
                                                          onTap: () {
                                                            // controller.photo_view(context, data['file']);
                                                          },
                                                          child: ClipRRect(
                                                            borderRadius: BorderRadius.circular(15),
                                                            child: Image.file(
                                                              data['file'],
                                                              height: Get.width / 2,
                                                              width: Get.width / 2,
                                                              fit: BoxFit.cover,
                                                            ),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                          height: 5,
                                                        ),
                                                      ],
                                                    ),
                                                  Row(
                                                    mainAxisAlignment: data['send'].toString() == 'you' ? MainAxisAlignment.end : MainAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        data['message'].toString(),
                                                        style: TextStyle(color: textblackColor, fontFamily: font_regular, fontSize: Get.width / 30),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(
                                                    height: 3,
                                                  ),
                                                  Text(
                                                    '10:36 AM',
                                                    style: TextStyle(color: textblackColor.withOpacity(0.4), fontFamily: font_regular, fontSize: Get.width / 45),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),

                                          // sender profile imagfe ----------------------------

                                          // if (data['send'].toString() == "you")
                                          //   Container(
                                          //     height: Get.width / 10,
                                          //     width: Get.width / 10,
                                          //     margin: EdgeInsets.only(left: 13),
                                          //     child: ClipRRect(
                                          //       borderRadius: BorderRadius.circular(1000),
                                          //       child: Image.asset(
                                          //         'assets/temp_img/profile.png',
                                          //         width: Get.width / 10,
                                          //       ),
                                          //     ),
                                          //   ),


                                        ],
                                      ),
                                    ));
                              },
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 15,
                          left: 0,
                          right: 0,
                          child: Column(
                            children: [
                              if (controller.showAttachmentSection)
                                Stack(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(color: Colors.transparent, borderRadius: const BorderRadius.all(Radius.circular(10)), boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 1)]),
                                      margin: const EdgeInsets.symmetric(horizontal: 20),
                                      padding: const EdgeInsets.only(left: 20, right: 20, top: 20, bottom: 10),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              // controller.getFromGallery();
                                            },
                                            child: Column(
                                              children: [
                                                Container(
                                                  padding: EdgeInsets.all(15),
                                                  decoration: BoxDecoration(
                                                    color: primaryColor.withOpacity(0.5),
                                                    borderRadius: BorderRadius.circular(15),
                                                  ),
                                                  child: Icon(
                                                    Icons.image_rounded,
                                                    size: 25,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                                const SizedBox(
                                                  height: 5,
                                                ),
                                                Text(
                                                  'Image',
                                                  style: TextStyle(fontSize: Get.width / 38),
                                                )
                                              ],
                                            ),
                                          ),

                                          const SizedBox(
                                            width: 20,
                                          ),

                                          GestureDetector(
                                            onTap: () {
                                              // controller.getFromCamera();
                                            },
                                            child: Column(
                                              children: [
                                                Container(
                                                  padding: EdgeInsets.all(15),
                                                  decoration: BoxDecoration(
                                                    color: primaryColor.withOpacity(0.5),
                                                    borderRadius: BorderRadius.circular(15),
                                                  ),
                                                  child: Icon(
                                                    CupertinoIcons.camera_circle_fill,
                                                    size: 25,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                                const SizedBox(
                                                  height: 5,
                                                ),
                                                Text(
                                                  'Camera',
                                                  style: TextStyle(fontSize: Get.width / 38),
                                                )
                                              ],
                                            ),
                                          ),

                                          const SizedBox(
                                            width: 20,
                                          ),

                                          Column(
                                            children: [
                                              Container(
                                                padding: EdgeInsets.all(15),
                                                decoration: BoxDecoration(
                                                  color: primaryColor.withOpacity(0.5),
                                                  borderRadius: BorderRadius.circular(15),
                                                ),
                                                child: Icon(
                                                  Icons.audio_file,
                                                  size: 25,
                                                  color: Colors.white,
                                                ),
                                              ),
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              Text(
                                                'Audio',
                                                style: TextStyle(fontSize: Get.width / 38),
                                              )
                                            ],
                                          ),

                                          // const SizedBox(width: 15,),
                                          //
                                          // Column(
                                          //   children: [
                                          //     Container(
                                          //       padding: EdgeInsets.all(15),
                                          //       decoration: BoxDecoration(
                                          //           color: primaryColor.withOpacity(0.5),
                                          //           borderRadius: BorderRadius.circular(15)
                                          //       ),
                                          //       child: Icon(Icons.video_file,size: 25,color: Colors.white,),
                                          //     ),
                                          //     const SizedBox(height: 5,),
                                          //     Text('Video',style: TextStyle(fontSize: Get.width/38),)
                                          //   ],
                                          // )
                                        ],
                                      ),
                                    ),
                                    Positioned(
                                        top: 2,
                                        right: 22,
                                        child: GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              controller.showAttachmentSection = false;
                                            });
                                          },
                                          child: Container(
                                            padding: EdgeInsets.all(5),
                                            child: Icon(Icons.close_rounded),
                                          ),
                                        ))
                                  ],
                                ),
                              if (controller.imageFile != null)
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: const BorderRadius.all(Radius.circular(15)),
                                    // boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1),blurRadius: 1)]
                                  ),
                                  padding: EdgeInsets.all(10),
                                  margin: EdgeInsets.all(10),
                                  child: Stack(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Image.file(controller.imageFile!, height: Get.width, width: Get.width),
                                      ),
                                      Positioned(
                                        bottom: 55,
                                        right: 10,
                                        child: GestureDetector(
                                          onTap: () {
                                            setState(() {
                                              controller.imageFile = null;
                                            });
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.3), blurRadius: 5)]),
                                            padding: const EdgeInsets.all(6),
                                            child: Icon(Icons.close_rounded, color: textblackColor),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        bottom: 10,
                                        right: 10,
                                        child: GestureDetector(
                                          onTap: () {
                                            controller.sendMessage('image', controller.msgTextController.text);
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.3), blurRadius: 5)]),
                                            padding: const EdgeInsets.all(6),
                                            child: Icon(Icons.check_rounded, color: textblackColor),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),



                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 10),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Container(
                                        width: Get.width,
                                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(1000), color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 5)]),
                                        margin: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                        padding: EdgeInsets.symmetric(horizontal: 5),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: TextField(
                                                onTap: () {
                                                  setState(() {
                                                    controller.showAttachmentSection = false;
                                                  });
                                                },
                                                onChanged: (text) {
                                                  setState(() {
                                                    controller.isText = text.isEmpty ? false : true;
                                                  });
                                                },
                                                maxLines: null,
                                                keyboardType: TextInputType.text,
                                                controller: controller.msgTextController,
                                                style: TextStyle(fontSize: Get.width / 28, color: Colors.black),
                                                decoration: InputDecoration(
                                                  focusedBorder: OutlineInputBorder(
                                                    borderSide: BorderSide(color: Colors.transparent, width: 1),
                                                    borderRadius: BorderRadius.circular(10.0),
                                                  ),
                                                  enabledBorder: OutlineInputBorder(
                                                    borderSide: BorderSide(color: Colors.transparent, width: 1),
                                                    borderRadius: BorderRadius.circular(10.0),
                                                  ),
                                                  filled: true,
                                                  hintStyle: TextStyle(fontFamily: font_regular, color: Colors.black38, fontSize: Get.width / 28),
                                                  hintText: "Type your message here",
                                                  fillColor: Colors.transparent,
                                                  contentPadding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20),
                                                ),
                                              ),
                                            ),
                                            // GestureDetector(
                                            //   onTap: () {
                                            //     setState(() {
                                            //       controller.showAttachmentSection =
                                            //       controller.showAttachmentSection
                                            //           ? false
                                            //           : true;
                                            //     });
                                            //   },
                                            //   child: SizedBox(
                                            //     height: 50,
                                            //     width: 40,
                                            //     child: Padding(
                                            //       padding: const EdgeInsets.symmetric(
                                            //           horizontal: 5.0),
                                            //       child: GestureDetector(
                                            //         child: const Icon(
                                            //           Icons.attachment_rounded,
                                            //           size: 25,
                                            //         ),
                                            //       ),
                                            //     ),
                                            //   ),
                                            // ),

                                            // controller.isText
                                            //     ? GestureDetector(
                                            //         onTap: () {
                                            //           controller.sendMessage(
                                            //               'text',
                                            //               controller
                                            //                   .msgTextController.text);
                                            //         },
                                            //         child: SizedBox(
                                            //           height: 50,
                                            //           width: 40,
                                            //           child: Padding(
                                            //             padding:
                                            //                 const EdgeInsets.symmetric(
                                            //                     horizontal: 5.0),
                                            //             child: GestureDetector(
                                            //               child: const Icon(
                                            //                 Icons.send_rounded,
                                            //                 size: 25,
                                            //               ),
                                            //             ),
                                            //           ),
                                            //         ),
                                            //       )
                                            //     : SizedBox(
                                            //         height: 50,
                                            //         width: 40,
                                            //         child: Padding(
                                            //           padding:
                                            //               const EdgeInsets.symmetric(
                                            //                   horizontal: 5.0),
                                            //           child: GestureDetector(
                                            //             child: const Icon(
                                            //               Icons.keyboard_voice_rounded,
                                            //               size: 25,
                                            //             ),
                                            //           ),
                                            //         ),
                                            //       )
                                          ],
                                        ),
                                      ),
                                    ),
                                    // SizedBox(width: 10,),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: primaryColor,
                                        borderRadius: BorderRadius.circular(1000),
                                      ),
                                      width: Get.width / 9,
                                      height: Get.width / 9,
                                      // padding: EdgeInsets.all(10),
                                      alignment: Alignment.center,
                                      child: Icon(
                                        Icons.send,
                                        color: textwhiteColor,
                                        size: 20,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

// List chat = [
//   'Hi!',
//   'Hello!',
//   'How can I improve my pronunciation?',
//   'Practice tongue twisters and mimic native speakers.',
//   'How do I sound more natural?',
//   'Work on intonation and mimic actors from movies.',
//   'How can I understand different accents?',
//   'Listen to various accents through podcasts and use subtitles.',
//   'Any tips for learning idioms?',
//   'Learn idioms in context by reading and listening.'
// ];
}
