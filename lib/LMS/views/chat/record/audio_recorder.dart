// import 'dart:async';
// import 'package:audioplayers/audioplayers.dart';
// import 'package:prepscale/components/constants.dart';
// import 'package:prepscale/controller/chat/speekichatcontroller.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:lottie/lottie.dart';
// import 'package:record/record.dart';
// import 'platform/audio_recorder_platform.dart';
// import 'package:audioplayers/audioplayers.dart' as ap;
//
// class Recorder extends StatefulWidget {
//   final void Function(String path) onStop;
//   final String sel_country, chat_id;
//
//   const Recorder({
//     super.key,
//     required this.onStop,
//     required this.sel_country,
//     required this.chat_id,
//   });
//
//   @override
//   State<Recorder> createState() => _RecorderState();
// }
//
// class _RecorderState extends State<Recorder> with AudioRecorderMixin {
//   int _recordDuration = 0;
//   Timer? _timer;
//   late final AudioRecorder _audioRecorder;
//   StreamSubscription<RecordState>? _recordSub;
//   RecordState _recordState = RecordState.stop;
//   StreamSubscription<Amplitude>? _amplitudeSub;
//   Amplitude? _amplitude;
//   final _audioPlayer = ap.AudioPlayer()..setReleaseMode(ReleaseMode.stop);
//
//   void _playAudio() async {
//     try {
//       await _audioPlayer.play(AssetSource("assets/sound/recording_stop.mp3"));
//     } catch (e) {
//       print("Error playing audio: $e");
//     }
//   }
//
//   @override
//   void initState() {
//     _audioRecorder = AudioRecorder();
//
//     _recordSub = _audioRecorder.onStateChanged().listen((recordState) {
//       _updateRecordState(recordState);
//     });
//
//     _amplitudeSub = _audioRecorder
//         .onAmplitudeChanged(const Duration(milliseconds: 300))
//         .listen((amp) {
//       setState(() => _amplitude = amp);
//     });
//
//     super.initState();
//   }
//
//   Speekichatcontroller controller = Get.put(Speekichatcontroller());
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<Speekichatcontroller>(builder: (controller) {
//       return Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: <Widget>[
//               _buildRecordStopControl(),
//             ],
//           ),
//
//           SizedBox(
//             height: 5,
//           ),
//           // _buildText(),
//
//           // SizedBox(height: Get.height/17,),
//
//           // if (_amplitude != null) ...[
//           //   const SizedBox(height: 40),
//           //   Text('Current: ${_amplitude?.current ?? 0.0}'),
//           //   Text('Max: ${_amplitude?.max ?? 0.0}'),
//           // ],
//           //
//         ],
//       );
//     });
//   }
//
//   @override
//   void dispose() {
//     _timer?.cancel();
//     _recordSub?.cancel();
//     _amplitudeSub?.cancel();
//     _audioRecorder.dispose();
//     super.dispose();
//   }
//
//   Widget _buildRecordStopControl() {
//     return _recordState == RecordState.stop
//         ? GestureDetector(
//             child: Container(
//               height: Get.width / 4,
//               width: Get.width / 4,
//               child:
//                   Image.asset('assets/icons/chat_mike.png', fit: BoxFit.cover),
//             ),
//             onTap: () {
//               if (_recordState == RecordState.stop) {
//                 print('button pressed: 2');
//                 _playAudio();
//                 _start();
//               }
//             },
//           )
//         : GestureDetector(
//             child: Container(
//               height: Get.width / 3,
//               width: Get.width / 3,
//               child: Lottie.asset('assets/lottie/voice_recording_1.json',
//                   fit: BoxFit.cover),
//             ),
//             onTap: () {
//               if (_recordState == RecordState.stop) {
//                 print('button pressed: 3');
//                 _start();
//               } else {
//                 print('button pressed: 4');
//                 _stop();
//               }
//             },
//           );
//   }
//
//   Future<void> _start() async {
//     try {
//       if (await _audioRecorder.hasPermission()) {
//         const encoder = AudioEncoder.aacLc;
//
//         if (!await _isEncoderSupported(encoder)) {
//           return;
//         }
//
//         final devs = await _audioRecorder.listInputDevices();
//         debugPrint(devs.toString());
//
//         const config = RecordConfig(encoder: encoder, numChannels: 1);
//
//         // Record to file
//         await recordFile(_audioRecorder, config);
//
//         // Record to stream
//         // await recordStream(_audioRecorder, config);
//
//         _recordDuration = 0;
//
//         _startTimer();
//       }
//     } catch (e) {
//       if (kDebugMode) {
//         print(e);
//       }
//     }
//   }
//
//   Future<void> _stop() async {
//     final path = await _audioRecorder.stop();
//
//     if (path == null) {
//       // if path null
//     } else {
//       widget.onStop(path);
//       // downloadWebData(path);
//
//       controller.call_send_chat(
//           widget.chat_id, widget.sel_country, 'audio', path, '');
//     }
//   }
//
//   Future<void> _pause() => _audioRecorder.pause();
//
//   // Future<void> _resume() => _audioRecorder.resume();
//
//   void _updateRecordState(RecordState recordState) {
//     setState(() => _recordState = recordState);
//
//     switch (recordState) {
//       case RecordState.pause:
//         _timer?.cancel();
//         break;
//       case RecordState.record:
//         _startTimer();
//         break;
//       case RecordState.stop:
//         _timer?.cancel();
//         _recordDuration = 0;
//         break;
//     }
//   }
//
//   Future<bool> _isEncoderSupported(AudioEncoder encoder) async {
//     final isSupported = await _audioRecorder.isEncoderSupported(
//       encoder,
//     );
//
//     if (!isSupported) {
//       debugPrint('${encoder.name} is not supported on this platform.');
//       debugPrint('Supported encoders are:');
//
//       for (final e in AudioEncoder.values) {
//         if (await _audioRecorder.isEncoderSupported(e)) {
//           debugPrint('- ${e.name}');
//         }
//       }
//     }
//
//     return isSupported;
//   }
//
//   // Widget _buildPauseResumeControl() {
//   //   if (_recordState == RecordState.stop) {
//   //     return const SizedBox.shrink();
//   //   }
//   //
//   //   late Icon icon;
//   //   late Color color;
//   //
//   //   if (_recordState == RecordState.record) {
//   //     icon = const Icon(Icons.pause, color: Colors.red, size: 30);
//   //     color = Colors.red.withOpacity(0.1);
//   //   } else {
//   //     final theme = Theme.of(context);
//   //     icon = const Icon(Icons.play_arrow, color: Colors.red, size: 30);
//   //     color = theme.primaryColor.withOpacity(0.1);
//   //   }
//   //
//   //   return ClipOval(
//   //     child: Material(
//   //       color: color,
//   //       child: InkWell(
//   //         child: SizedBox(width: 56, height: 56, child: icon),
//   //         onTap: () {
//   //           (_recordState == RecordState.pause) ? _resume() : _pause();
//   //         },
//   //       ),
//   //     ),
//   //   );
//   // }
//
//   Widget _buildText() {
//     if (_recordState != RecordState.stop) {
//       return _buildTimer();
//     }
//     return Text(
//       "Tap to speak",
//       style: TextStyle(color: textblackColor, fontFamily: font_regular),
//     );
//   }
//
//   Widget _buildTimer() {
//     final String minutes = _formatNumber(_recordDuration ~/ 60);
//     final String seconds = _formatNumber(_recordDuration % 60);
//     return Text(
//       '$minutes : $seconds',
//       style: const TextStyle(color: Colors.red),
//     );
//   }
//
//   String _formatNumber(int number) {
//     String numberStr = number.toString();
//     if (number < 10) {
//       numberStr = '0$numberStr';
//     }
//
//     return numberStr;
//   }
//
//   void _startTimer() {
//     _timer?.cancel();
//
//     _timer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
//       setState(() => _recordDuration++);
//     });
//   }
// }
