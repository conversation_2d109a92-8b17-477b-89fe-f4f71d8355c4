import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:lottie/lottie.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../../controllers/commoncontroller.dart';

class feedback extends StatefulWidget {
  String type, id;

  feedback({required this.type, required this.id});

  @override
  State<feedback> createState() => _switch_courseState();
}

class _switch_courseState extends State<feedback> {
  Commoncontroller controller = Get.put(Commoncontroller());

  TextEditingController feedback = TextEditingController();
  double rating = 0;

  submitfeedback_fn()async{

    if(feedback.text.isEmpty){
      toast_info('Enter Feedback');
    }
    else {
      // var data = await controller.fet_add_feedback(widget.id.toString(),rating,feedback.text.toString(),);
      // print("feedback add resp-----------$data");
      //
      // setState(() {
      //   controller.isLoading = false;
      // });

      // if(data[0]['status'].toString() == "1")
      // {
      //   toast_success('Thank you for your valuable feedback. Feedback submitted to $appName Successfully');
        toast_success('Feedback submitted ');
        Get.back();
      // }else{
      //   toast_error('Something went wrong');
      // }
    }



  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Commoncontroller>(builder: (controller) {
      return Scaffold(
        backgroundColor: textwhiteColor,
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Container(
            height: Get.height,
            decoration: BoxDecoration(
                // image: DecorationImage(
                //     fit: BoxFit.cover,
                //   opacity: 0.05,
                //   image: AssetImage('assets/bg/bg2.webp'),
                // ),
                color: Colors.black.withOpacity(0.03)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // appbar----------
                AppBar(
                  backgroundColor: appbarwhiteColor,
                  surfaceTintColor: appbarwhiteColor,
                  elevation: 0,
                  leading: GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        color: Colors.transparent,
                        // padding: EdgeInsets.only(left: 10),
                        child: Icon(Icons.arrow_back),
                      )),
                  iconTheme: IconThemeData(color: Colors.black),
                ),

                Expanded(
                  child: SingleChildScrollView(
                    physics: ScrollPhysics(),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 20,
                        ),
                        Container(
                          child: Text(
                            "Feel Free to Provide Your Feedback",
                            style: TextStyle(),
                          ),
                        ),
                        SizedBox(
                          height: 20,
                        ),
                        Container(
                          width: Get.width / 2.0,
                          child: Image.asset('assets/icons/feedback.webp'),
                        ),
                        SizedBox(
                          height: 20,
                        ),
                        Container(
                            // color: Colors.white,
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "Rating :",
                                    style: TextStyle(fontWeight: FontWeight.w600, color: Colors.black),
                                  ),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Container(
                                  alignment: Alignment.center,
                                  child: RatingBar.builder(
                                    initialRating: 0,
                                    minRating: 0,
                                    direction: Axis.horizontal,
                                    allowHalfRating: true,
                                    itemCount: 5,
                                    itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
                                    itemBuilder: (context, _) => Icon(Icons.star, color: secondaryColor),
                                    onRatingUpdate: (rat) {
                                      setState(() {
                                        rating = rat;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            )),
                        SizedBox(height: 20),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Review :",
                            style: TextStyle( fontWeight: FontWeight.w600, color: Colors.black),
                          ),
                        ),
                        Container(
                          width: MediaQuery.of(context).size.width / 1.2,
                          // height: 40,
                          child: TextField(
                            keyboardType: TextInputType.multiline,
                            minLines: 5,
                            //Normal textInputField will be displayed
                            maxLines: 5,
                            // when user presses enter it will adapt to it
                            style: TextStyle(fontSize: 15.0, height: 1.5, color: Colors.black),
                            controller: feedback,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.black26, width: 0.5),
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.black26, width: 0.5),
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              filled: true,
                              hintStyle: TextStyle(color: const Color(0xFFA1A3AB)),
                              hintText: "Type your feedback here ..",
                              fillColor: Colors.white70,
                              contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 20),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Container(
                          child: cust_elevatedbutton(
                            width: Get.width / 1.2,
                            onPressed: () {
                              print("sndhsvdgsvdhsds");

                              submitfeedback_fn();
                            },
                            borderRadius: BorderRadius.circular(10),
                            height: 50,
                            gradient: LinearGradient(
                              colors: [
                                primaryColor,
                                secondaryColor,
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            child:
                                // controller.isLoading ? Center(
                                //   child: Container(
                                //     height: 20,
                                //     width: 20,
                                //     child: CircularProgressIndicator(color: Colors.white,),
                                //   ),
                                // ) :
                                Text(
                              "Submit",
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.white, fontFamily: font_bold),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(
                  height: 20,
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
