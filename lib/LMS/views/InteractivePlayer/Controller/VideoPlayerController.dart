import 'dart:async';
import 'dart:developer';
import 'package:better_player_plus/better_player_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:video_player/video_player.dart';
import '../../../../components/utils.dart';
import '../../../api/main_apis.dart';
import '../FullScreenMp4Player.dart';

class VideoPlayerGetXController extends GetxController {

  String lessonId = "";

  List lesson_files = [];
  bool isLessonFileLoading = false;

  bool selvidindexloading = false;
  var sel_video;
  int sel_index = 0;


  get_lesson_files(lesson_id) async {
    isLessonFileLoading = true;
    var data = await ApiBaseHandler.lessonFiles(lesson_id);
    lesson_files = data['data'];
    log('lesson_files----------------$lesson_files');
    if (lesson_files.isNotEmpty) {
      sel_index = 0;
      sel_video = lesson_files[0];
    }
    isLessonFileLoading = false;
    update();
  }


  bool isMaterialLoading = true;
  List materialList = [];

  getMaterial(course_id, section_id, lesson_id) async {
    log("Log Dataaaaa : $course_id");
    log("Log Dataaaaa : $section_id");
    log("Log Dataaaaa : $lesson_id");

    isMaterialLoading = true;
    var data = await ApiBaseHandler.materials(course_id, section_id, lesson_id);

    if (data['status'].toString() == "true" || data['status'].toString() == "1") {
      materialList = data['data'];
      // log(materialList.toString());
    } else {
      toast_info(data['message'].toString());
    }

    isMaterialLoading = false;
    update();
  }



// ---------------------better player section-------------------------

  late BetterPlayerController betterPlayerController;


  void onPlayerEventBetter(event,) {
    final currentPosition = betterPlayerController.videoPlayerController?.value.position;
    final videoDuration = betterPlayerController.videoPlayerController?.value.duration;

    final videoPlayer = betterPlayerController.videoPlayerController;

    if (videoPlayer == null || !videoPlayer.value.initialized) {
      return;
    }

    if (!Get.context!.mounted) return;
    progress =  videoPlayer.value.position;
    duration =  videoPlayer.value.duration!;
    update();


    // auto next play
    // if(playerController.value.isCompleted)

    if (videoPlayer.value.position.toString().split('.').first != "00:00:00") {
      if(currentPosition == videoDuration) {
        startShowTimer();

        print('------------------------koiiiii-------------completed---');
        // print(lessondata[sel_item_index+1]['attachment_type'].toString());

        // event.pause();

      }
    }
  }


// ---------------------better player section-------------------------



  int? detected_position_index;

  List videoFiles = [];
  Map videoData = {};
  late VideoPlayerController playerController;

  bool isVideoError = false;

  Map? selectedFile;
  bool isQualityChanging = false;
  bool showControl = true;
  late Duration progress;
  late Duration duration;
  double playbackSpeed = 1.0;
  List playbackSpeedList = [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];
  bool isRewinding = false;
  bool isForwarding = false;
  // bool isPausedForInteraction = false;
  bool isShowingTheIntraction = false;
  int? selectedMultipleChoiceOption;
  bool isInteractionIsSubmitted = false;
  bool isAllraedyShowed = false;

  Timer? _controlTimer;



  // -------------------------Player Event Start-----------------------------------
  void onPlayerEvent() {
    final currentPosition = playerController.value.position;
    final videoDuration = playerController.value.duration;


    // print('curr posi-------------------------'+currentPosition.toString());
    // print("|");
    // print('total dur-------------------------'+videoDuration.toString());

    if (!Get.context!.mounted) return;

    progress = currentPosition;
    duration = videoDuration;
    update();

    if (playerController.value.isPlaying) {


      // log(videoData.toString());




      // print("-----------R-----"+formatDurationString(currentPosition.toString().split('.')[0].toString()));
      // print(videoData['positions'].toString());



      // show interactive UI
      // contnuesly checking the any action in the current position
      if(!isShowingTheIntraction && videoData['positions'].contains(formatDurationString(currentPosition.toString().split('.')[0].toString()).toString()))
        {

          // an action detected

          print("-------------yeah found ---------------on--"+formatDurationString(currentPosition.toString().split('.')[0].toString()).toString());
          playerController.pause();

          // detected actions in player array index
          // use this index to choose currenspong action from the 'actions_in_player' array
          detected_position_index = videoData['positions'].indexOf(formatDurationString(currentPosition.toString().split('.')[0].toString()).toString());
          isShowingTheIntraction = true;
          update();
        }
    }

    if(playerController.value.isCompleted){
      updateLessonFileProgress(videoData['id'].toString());
    }
  }
  // -------------------------Player Event END -----------------------------------



  // -------------------------New sec Start -----------------------------------
  String formatDurationString(String durationStr) {
    List<String> parts = durationStr.split(':');
    // Ensure there are always 3 parts: hours, minutes, seconds
    while (parts.length < 3) {
      parts.insert(0, '0'); // pad with leading zeros if needed
    }
    // Pad each part to 2 digits
    final formatted = parts.map((part) => part.padLeft(2, '0')).join(':');
    return formatted;
  }
  // -------------------------New sec END -----------------------------------

  void changeVideo(data) async {
    try {
      isVideoError = false;
      selectedFile = data;
      final Duration? currentPosition = await playerController.position;
      await playerController.pause();
      isQualityChanging = true;
      update();
      playerController = VideoPlayerController.networkUrl(Uri.parse(data['video_url'].toString()))
        ..initialize().then((_) {
          isQualityChanging = false;
          playerController.addListener(onPlayerEvent);
          if (currentPosition != null) {
            playerController.seekTo(currentPosition).then((_) {
              update();
            });
            playerController.play();
            update();
          }
          playerController.play();
        }).catchError((error) {
          log("Error initializing video: $error");
          isVideoError = true;
          update();
        });
    } catch (error) {
      log("Error changing video: $error");
    }
  }


  updateLessonFileProgress(lesson_file_id) async {
    var data = await ApiBaseHandler.update_lesson_file_progress(lesson_file_id.toString());
    if(data['status'].toString() == "1" ||data ['status'].toString() == "true"){
      get_lesson_files(lessonId);
    }
  }




  forward5Sec() {
    Duration? current = playerController.value.position;
    current = current + const Duration(seconds: 5);
    playerController.seekTo(current);
    playerController.play();
    startShowTimer();
    update();
  }

  rewind5Sec() {
    Duration? current = playerController.value.position;
    current = current - const Duration(seconds: 5);
    playerController.seekTo(current);
    playerController.play();
    startShowTimer();
    update();
  }


  updateProgress() {
    if (!Get.context!.mounted) return;
    progress = playerController.value.position;
    duration = playerController.value.duration;
    update();
  }

  void onSliderChanged(double value) {
    var newProgress = Duration(milliseconds: value.toInt());
    playerController.seekTo(newProgress);
    progress = newProgress;
    playerController.play();
    startShowTimer();
    update();
  }


  Duration parseSimpleDuration(String input) {
    final parts = input.split(':'); // [HH, MM, SS]
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    final seconds = int.parse(parts[2]);
    return Duration(hours: hours, minutes: minutes, seconds: seconds);
  }

  void toggleFullScreen(bool isFullScreen) {
    if (isFullScreen) {
      Get.back();
    } else {
      Get.to(() => FullscreenPlayer());
    }
  }

  String formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  void togglePlayPause() {
    if (playerController.value.isPlaying) {
      playerController.pause();
    } else {
      playerController.play();
    }
    startShowTimer();
    update();
  }

  void startShowTimer() {
    _controlTimer?.cancel();
    showControl = true;
    update();
    _controlTimer = Timer(const Duration(seconds: 3), () {
      showControl = false;
      update();
    });
  }
}



class YourMultipleChoiceWidget extends StatelessWidget {
  final Function(String) onOptionSelected;

  const YourMultipleChoiceWidget({required this.onOptionSelected, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Quick Question"),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text("What is 2 + 2?"),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: () => onOptionSelected("3"),
            child: const Text("3"),
          ),
          ElevatedButton(
            onPressed: () => onOptionSelected("4"),
            child: const Text("4"),
          ),
          ElevatedButton(
            onPressed: () => onOptionSelected("5"),
            child: const Text("5"),
          ),
        ],
      ),
    );
  }
}