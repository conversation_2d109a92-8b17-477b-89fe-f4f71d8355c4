import 'package:flutter/material.dart';

class DeleteMessageDialogue extends StatelessWidget {
  DeleteMessageDialogue({super.key, required this.onPressed});
  void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_outlined,
            color: Colors.red,
            size: 35,
          )
        ],
      ),
      content: const Text(
        'Are you sure you want to delete this message?',
        style: TextStyle(fontSize: 12),
      ),
      actions: [
        TextButton(
          child: const Text('Cancel'),
          onPressed: () {
            Navigator.of(context).pop(); // Close the dialog
          },
        ),
        TextButton(
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
            onPressed: onPressed),
      ],
    );
  }
}
