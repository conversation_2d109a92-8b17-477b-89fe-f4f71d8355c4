import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:edutalim/components/utils.dart';
import 'package:edutalim/LMS/views/dashboard/account/analysis.dart';
import 'package:edutalim/LMS/views/dashboard/account/feedback_pg.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:share_plus/share_plus.dart';

import 'package:edutalim/components/constants.dart';
import 'package:edutalim/components/utils.dart';
import '../../../../views/auth/login.dart';
import '../../../../controllers/commoncontroller.dart';
import '../../tools/web_view_pg.dart';
import '../dashboard.dart';

class account extends StatefulWidget {
  @override
  State<account> createState() => _accountState();
}

class _accountState extends State<account> {
  Commoncontroller controller = Get.put(Commoncontroller());

  // Reporttcontroller controller2 = Get.put(Reporttcontroller());

  @override
  void initState() {
    // TODO: implement initState

    controller.getProfile();
    super.initState();
    // controller.get_userdata();
    // controller2.get_reports();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<Commoncontroller>(builder: (controller) {
      return Scaffold(
        backgroundColor: textwhiteColor,
        extendBodyBehindAppBar: true,
        // appBar: AppBar(
        //   // systemOverlayStyle: SystemUiOverlayStyle(statusBarColor: primaryColor),
        //   title: Container(
        //     child: Text("Profile",style: TextStyle(color: textblackColor,fontFamily: 'poppins_bold',fontWeight: FontWeight.w700,fontSize: Get.width/22),),
        //   ),
        //   // backgroundColor: primaryColor,
        //   // surfaceTintColor: primaryColor,
        //   // backgroundColor: Color(0xffD9E9E8).withOpacity(.7),
        //   // surfaceTintColor: textwhiteColor,
        //   automaticallyImplyLeading: false,
        //   centerTitle: true,
        //   // actions: [
        //   //   IconButton(
        //   //       onPressed: (){},
        //   //       icon: Icon(Icons.settings,color: textwhiteColor,)
        //   //   ),
        //   //   SizedBox(width: 10,),
        //   // ],
        // ),
        body: SafeArea(
          top: false,
          child: Container(
              // height: Get.height,
              width: Get.width,
              color: textwhiteColor,
              child: controller.isProfileLoading
                  ? loader()
                  : Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(bottom: 20),
                          decoration: BoxDecoration(image: DecorationImage(image: AssetImage('assets/bg/profile.png'), fit: BoxFit.cover)),
                          child: Container(
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(bottom: 15),
                            child: AppBar(
                              title: Text(
                                "Profile",
                                style: TextStyle(color: textwhiteColor, fontFamily: 'poppins_bold', fontWeight: FontWeight.w700, fontSize: Get.width / 22),
                              ),
                              backgroundColor: Colors.transparent,
                              surfaceTintColor: Colors.transparent,
                              automaticallyImplyLeading: false,
                              centerTitle: true,
                            ),
                          ),
                        ),

                        Column(
                          children: [
                            SizedBox(height: 20),
                            GestureDetector(
                              onTap: () {},
                              child: Container(
                                color: Colors.transparent,
                                child: Stack(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        // border: Border.all(color: textwhiteColor,width: 3),
                                        borderRadius: BorderRadius.circular(1000),
                                      ),
                                      width: Get.width / 5.5,
                                      height: Get.width / 5.5,
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(1000),
                                        child: CachedNetworkImage(
                                          imageUrl: controller.profilePage['user']['profile_image'].toString(),
                                          errorWidget: (_, __, ___) {
                                            return Image.asset('assets/images/avatar_placeholder.png', width: Get.width / 4.5, height: Get.width / 4.5, fit: BoxFit.cover);
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              controller.profilePage['user']['name'].toString(),
                              style: TextStyle(color: textblackColor, fontFamily: font_bold, fontWeight: FontWeight.w600, fontSize: Get.width / 24),
                            ),
                            Text(
                              controller.profilePage['user']['phone'].toString(),
                              style: TextStyle(color: textblackColor, fontFamily: font_regular, fontWeight: FontWeight.w400, fontSize: Get.width / 28),
                            ),
                          ],
                        ),

                        SizedBox(
                          height: 20,
                        ),
                        // GestureDetector(
                        //   onTap: () {},
                        //   child: Container(
                        //     height: 35,
                        //     width: Get.width / 2.6,
                        //     decoration: BoxDecoration(
                        //       borderRadius: BorderRadius.circular(30),
                        //       color: textwhiteColor,
                        //       gradient: LinearGradient(colors: [
                        //         secondarylightColor,
                        //         secondaryColor.withOpacity(.7),
                        //       ], begin: Alignment.centerLeft, end: Alignment.centerRight),
                        //     ),
                        //     child: Row(
                        //       mainAxisAlignment: MainAxisAlignment.center,
                        //       children: [
                        //         Text(
                        //           "Edit Profile",
                        //           style: TextStyle(color: textwhiteColor, fontWeight: FontWeight.w600, fontFamily: font_bold, fontSize: Get.width / 30),
                        //         ),
                        //         SizedBox(
                        //           width: 10,
                        //         ),
                        //         Icon(
                        //           Icons.edit,
                        //           color: textwhiteColor,
                        //           size: Get.width / 22,
                        //         )
                        //         // Image.asset('assets/icons/edit.png',width: Get.width/22,)
                        //       ],
                        //     ),
                        //   ),
                        // ),
                        // SizedBox(
                        //   height: 20,
                        // ),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Container(
                              width: Get.width,
                              margin: EdgeInsets.symmetric(horizontal: 5),
                              padding: EdgeInsets.all(10),
                              child: Column(
                                children: [
                                  SizedBox(height: 20),

                                  // if(GetStorage().read('pay_version').toString() == "false")
                                  // GestureDetector(
                                  //     onTap: () {
                                  //       toast_info("Certificate Not Found...");
                                  //     },
                                  //     child: ItemCard(icon_path: "assets/icons/certificates.png", title: "Download Certificate")),

                                  GestureDetector(
                                      onTap: () {
                                        Share.share(share_description.toString());
                                      },
                                      child: ItemCard(icon_path: "assets/icons/share1.png", title: "Share the App")),

                                  // if (GetStorage().read('pay_version').toString() == "false")
                                    GestureDetector(
                                        onTap: () {
                                          // Get.to(() => FeedbackPage());
                                          Get.to(() => AnalysisPage());
                                        },
                                        child: ItemCard(icon_path: "assets/icons/analytics.png", title: "Analysis")),

                                  GestureDetector(
                                      onTap: () {
                                        // Get.to(() => FeedbackPage());
                                        Get.to(() => feedback(type: 'app', id: ''));
                                      },
                                      child: ItemCard(icon_path: "assets/icons/feedback.png", title: "Feedback")),

                                  GestureDetector(
                                      onTap: () {
                                        // launchURL("tel:${controller.profilePage['support_phone'].toString()}");
                                        Get.to(webview_page(url: 'https://www.edutalim.com/contact/'));
                                      },
                                      child: ItemCard(icon_path: "assets/icons/support.png", title: "Support")),

                                  GestureDetector(
                                      onTap: () {
                                        Get.to(() => webview_page(url: controller.profilePage['privacy_policy'].toString()));
                                      },
                                      child: ItemCard(icon_path: "assets/icons/privacypolicy.png", title: "Privacy Policy")),

                                  GestureDetector(
                                      onTap: () {
                                        logout_confirm(context);
                                      },
                                      child: ItemCard(icon_path: "assets/icons/logout.png", title: "Logout")),

                                  SizedBox(
                                    height: 10,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
        ),
      );
    });
  }

  void logout_confirm(BuildContext context) {
    showCupertinoDialog(
        context: context,
        builder: (context) {
          return CupertinoAlertDialog(
            title: const Text("Logout ?"),
            content: const Wrap(
              alignment: WrapAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 20,
                    ),

                    // Lottie.asset('assets/lottie/warning.json',width: Get.width/2,repeat: false),
                    Text("Are you sure you want to Logout ?"),
                    SizedBox(
                      height: 20,
                    ),
                  ],
                )
              ],
            ),
            actions: <Widget>[
              CupertinoDialogAction(
                  isDefaultAction: true,
                  // isDestructiveAction: true,
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    "Cancel",
                    style: TextStyle(color: Colors.blue),
                  )),
              CupertinoDialogAction(
                  textStyle: TextStyle(color: Colors.red),
                  isDefaultAction: true,
                  onPressed: () async {
                    GetStorage().write('home_index', '0');
                    GetStorage().write('login_status', 'false');
                    Get.offAll(Login());
                  },
                  child: Text("Logout")),
            ],
          );
        });
  }
}

class ItemCard extends StatelessWidget {
  String icon_path, title;
  bool isLogout = false;

  ItemCard({required this.icon_path, required this.title});

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container(
      color: Colors.transparent,

      padding: EdgeInsets.all(12),
      // margin: EdgeInsets.symmetric(horizontal: 10),
      margin: EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          Image.asset(
            icon_path,
            height: Get.width / 20,
            width: Get.width / 20,
            color: primarylightColor.withValues(alpha: .9),
          ),
          const SizedBox(
            width: 25,
          ),
          Expanded(
              child: Text(
            title,
            style: TextStyle(color: textblackColor, fontFamily: font_medium, fontSize: Get.width / 24),
          )),
          const SizedBox(
            width: 20,
          ),
          Icon(
            CupertinoIcons.right_chevron,
            size: 14,
          )
        ],
      ),
    );
  }
}
