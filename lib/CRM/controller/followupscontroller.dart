import 'package:edutalim/CRM/api/apis.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FollowUpController extends GetxController {
  late String member_id;
  List followups = [], alldata = [];
  String? sel_status, sel_country, sel_university, sel_source, sel_team;
  TextEditingController searchfld = TextEditingController();

  bool isLoading = false;

  get_followups() async {
    isLoading = false;

    // var data = await CRMApiBaseHandler.fet_followups(member_id.toString() == "null" ? "" : member_id.toString(), from_date == "All" ? '' : from_date.toString(), to_date == "All" ? '' : to_date.toString(),
    //     sel_country ?? '', sel_university ?? '', sel_source ?? '', sel_team ?? '');

    // followups = [data[0]['data']];
    // alldata = [data[0]['data']];

    followups = [
      {
        "current": [
          {
            "id": "3035",
            "title": "Kiran",
            "gender": "male",
            "age": "21",
            "code": "91",
            "phone": "8590431168",
            "whatsapp": "",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": null,
            "lead_status_id": "3",
            "lead_status": "",
            "remarks": "",
            "followup_date": null,
            "lead_source_id": "3",
            "lead_source": "",
            "address": "",
            "telecaller_id": "119",
            "telecaller": "",
            "place": "",
            "created_at": "2025-05-19 15:46:22",
            "updated_at": "2025-05-19 15:46:22"
          },
          {
            "id": "3032",
            "title": "Faeez",
            "gender": "male",
            "age": "18",
            "code": "91",
            "phone": "9349987734",
            "whatsapp": "",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": null,
            "lead_status_id": "3",
            "lead_status": "",
            "remarks": "",
            "followup_date": null,
            "lead_source_id": "3",
            "lead_source": "",
            "address": "",
            "telecaller_id": "119",
            "telecaller": "",
            "place": "",
            "created_at": "2025-05-19 15:41:12",
            "updated_at": "2025-05-19 15:41:12"
          },
          {
            "id": "3031",
            "title": "Abdul Bayis",
            "gender": "male",
            "age": "19",
            "code": "91",
            "phone": "9633712165",
            "whatsapp": "",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": null,
            "lead_status_id": "3",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-24",
            "lead_source_id": "1",
            "lead_source": "",
            "address": "",
            "telecaller_id": "119",
            "telecaller": "",
            "place": "",
            "created_at": "2025-05-19 15:40:09",
            "updated_at": "2025-05-19 15:40:09"
          },
          {
            "id": "3030",
            "title": "Sangeeth ",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "8129949682",
            "whatsapp": "",
            "email": "",
            "qualification": "Plus two",
            "country_id": "0",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": null,
            "lead_status_id": "3",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-26",
            "lead_source_id": "3",
            "lead_source": "",
            "address": "",
            "telecaller_id": "119",
            "telecaller": "",
            "place": "",
            "created_at": "2025-05-19 15:35:51",
            "updated_at": "2025-05-19 15:35:51"
          },
          {
            "id": "3028",
            "title": "Yesudas",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "9544509894",
            "whatsapp": "",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": null,
            "lead_status_id": "3",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-22",
            "lead_source_id": "3",
            "lead_source": "",
            "address": "",
            "telecaller_id": "119",
            "telecaller": "",
            "place": "Kollam",
            "created_at": "2025-05-19 15:33:19",
            "updated_at": "2025-05-19 15:33:19"
          }
        ],
        "upcoming": [
          {
            "id": "3031",
            "title": "Abdul Bayis",
            "gender": "male",
            "age": "19",
            "code": "91",
            "phone": "9633712165",
            "whatsapp": "",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": null,
            "lead_status_id": "3",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-24",
            "lead_source_id": "1",
            "lead_source": "",
            "address": "",
            "telecaller_id": "119",
            "telecaller": "",
            "place": "",
            "created_at": "2025-05-19 15:40:09",
            "updated_at": "2025-05-19 15:40:09"
          },
          {
            "id": "3030",
            "title": "Sangeeth ",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "8129949682",
            "whatsapp": "",
            "email": "",
            "qualification": "Plus two",
            "country_id": "0",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": null,
            "lead_status_id": "3",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-26",
            "lead_source_id": "3",
            "lead_source": "",
            "address": "",
            "telecaller_id": "119",
            "telecaller": "",
            "place": "",
            "created_at": "2025-05-19 15:35:51",
            "updated_at": "2025-05-19 15:35:51"
          },
          {
            "id": "3028",
            "title": "Yesudas",
            "gender": "male",
            "age": "0",
            "code": "91",
            "phone": "9544509894",
            "whatsapp": "",
            "email": "",
            "qualification": "",
            "country_id": "0",
            "country": "",
            "university_id": "6",
            "university": "",
            "interest_status": null,
            "lead_status_id": "3",
            "lead_status": "",
            "remarks": "",
            "followup_date": "2025-05-22",
            "lead_source_id": "3",
            "lead_source": "",
            "address": "",
            "telecaller_id": "119",
            "telecaller": "",
            "place": "Kollam",
            "created_at": "2025-05-19 15:33:19",
            "updated_at": "2025-05-19 15:33:19"
          }
        ],
        "telecallers": [
          {
            "id": "0",
            "name": "all"
          },
          {
            "id": "30",
            "name": "Rajaprasad"
          },
          {
            "id": "31",
            "name": "Shijas"
          },
          {
            "id": "32",
            "name": "Sudheer Cheruvady"
          },
          {
            "id": "33",
            "name": "Noufal"
          },
          {
            "id": "35",
            "name": "Yasir "
          },
          {
            "id": "36",
            "name": "Rilwan"
          },
          {
            "id": "37",
            "name": "Hamza"
          },
          {
            "id": "38",
            "name": "Sudheer "
          },
          {
            "id": "39",
            "name": "Md Saleem"
          },
          {
            "id": "40",
            "name": "Raju Seabastian"
          },
          {
            "id": "41",
            "name": "Sajid"
          },
          {
            "id": "42",
            "name": "Nasrudhin"
          },
          {
            "id": "43",
            "name": "Unnikrishnan Kinavoor"
          },
          {
            "id": "44",
            "name": "Harshana PK"
          },
          {
            "id": "45",
            "name": "Mishab"
          },
          {
            "id": "46",
            "name": "Abdul Azeez"
          },
          {
            "id": "47",
            "name": "Najeeb P"
          },
          {
            "id": "48",
            "name": "Jeeshma "
          },
          {
            "id": "49",
            "name": "Nimisha Raj"
          },
          {
            "id": "51",
            "name": "Bushra"
          },
          {
            "id": "52",
            "name": "Rishfan"
          },
          {
            "id": "53",
            "name": "Abdulla Kutty VP"
          },
          {
            "id": "54",
            "name": "Shafeera"
          },
          {
            "id": "55",
            "name": "Murali"
          },
          {
            "id": "56",
            "name": "Sanoop M"
          },
          {
            "id": "57",
            "name": "Lijo"
          },
          {
            "id": "58",
            "name": "Majid"
          },
          {
            "id": "59",
            "name": "Faisal"
          },
          {
            "id": "60",
            "name": "Jaseela KA"
          },
          {
            "id": "61",
            "name": "Abitha"
          },
          {
            "id": "62",
            "name": "Abdussamad"
          },
          {
            "id": "63",
            "name": "Ameer Trogon"
          },
          {
            "id": "64",
            "name": "Binzy "
          },
          {
            "id": "65",
            "name": "Sreeja AR"
          },
          {
            "id": "66",
            "name": "Velayudhan"
          },
          {
            "id": "67",
            "name": "Muthu V"
          },
          {
            "id": "68",
            "name": "Fayis"
          },
          {
            "id": "74",
            "name": "Ranjith AK"
          },
          {
            "id": "75",
            "name": "Ashir"
          },
          {
            "id": "76",
            "name": "Nisha"
          },
          {
            "id": "77",
            "name": "Ajumi"
          },
          {
            "id": "78",
            "name": "Neethu"
          },
          {
            "id": "79",
            "name": "Gajath JP"
          },
          {
            "id": "80",
            "name": "Ishan Amrutha Prasad"
          },
          {
            "id": "81",
            "name": "Nishad"
          },
          {
            "id": "82",
            "name": "Rajaprasad"
          },
          {
            "id": "87",
            "name": "trogon"
          },
          {
            "id": "90",
            "name": "Syam Prateesh"
          },
          {
            "id": "93",
            "name": "Ameen "
          },
          {
            "id": "101",
            "name": "Nimisha"
          },
          {
            "id": "104",
            "name": "Britco Dubai"
          },
          {
            "id": "116",
            "name": "Fathima Bushra NE"
          },
          {
            "id": "117",
            "name": "Deepika"
          },
          {
            "id": "118",
            "name": "Sebin Sebastian "
          },
          {
            "id": "119",
            "name": "Ranjitha"
          },
          {
            "id": "120",
            "name": "Shaheena"
          },
          {
            "id": "132",
            "name": "Irshad"
          },
          {
            "id": "133",
            "name": "Muhmmed Sinan"
          }
        ],
        "country": [
          {
            "id": "6",
            "title": "Georgia"
          },
          {
            "id": "7",
            "title": "Uzbekistan"
          },
          {
            "id": "8",
            "title": "Philippines"
          },
          {
            "id": "9",
            "title": "Russia"
          },
          {
            "id": "10",
            "title": "Kazakhstan"
          },
          {
            "id": "11",
            "title": "Kyrgyzstan"
          },
          {
            "id": "12",
            "title": "Moldova"
          },
          {
            "id": "13",
            "title": "Others"
          }
        ],
        "university": [
          {
            "id": "6",
            "title": "Georgian American University GAU"
          },
          {
            "id": "7",
            "title": "Georgian National University SEU"
          },
          {
            "id": "8",
            "title": "University of Georgia UG"
          },
          {
            "id": "9",
            "title": "East European University EEU"
          },
          {
            "id": "10",
            "title": "Brokenshire School of Medicine"
          },
          {
            "id": "11",
            "title": "Others"
          },
          {
            "id": "12",
            "title": "Batumi State University"
          },
          {
            "id": "13",
            "title": "Alte University"
          },
          {
            "id": "14",
            "title": "Akaki State University"
          },
          {
            "id": "15",
            "title": "Caucasus University"
          },
          {
            "id": "16",
            "title": "Tbilisi Medical Academy TMA"
          },
          {
            "id": "17",
            "title": "Tbilisi State Medical University TSMU"
          },
          {
            "id": "18",
            "title": "Tbilisi State University TSU"
          },
          {
            "id": "19",
            "title": "Bukhara State Medical Institute"
          },
          {
            "id": "20",
            "title": "Andijan State Medical Institute"
          },
          {
            "id": "21",
            "title": "Samarkand State Medical Institute"
          },
          {
            "id": "22",
            "title": "Tashkent Medical Academy"
          },
          {
            "id": "23",
            "title": "Fargana State University"
          },
          {
            "id": "24",
            "title": "Kazakh National Medical University"
          },
          {
            "id": "25",
            "title": "Al-Farabi Kazakh National University"
          },
          {
            "id": "26",
            "title": "Caspian University"
          },
          {
            "id": "27",
            "title": "Nicolae Testemitanu State University"
          }
        ],
        "team": [
          {
            "id": "5",
            "title": "Guidance centre of smartphone technology TIR",
            "team_lead_id": "46"
          },
          {
            "id": "6",
            "title": "Unique institute smartphone technology KSD",
            "team_lead_id": "45"
          },
          {
            "id": "7",
            "title": "Zwixt institute of advanced smartphone technology KLM",
            "team_lead_id": "30"
          },
          {
            "id": "8",
            "title": "Empowering gen technologies KKL",
            "team_lead_id": "66"
          },
          {
            "id": "9",
            "title": "Ibeau technologies KDY",
            "team_lead_id": "32"
          },
          {
            "id": "10",
            "title": "Research institute of wireless technology MJI",
            "team_lead_id": "33"
          },
          {
            "id": "11",
            "title": "Frontier wireless care institute NBR",
            "team_lead_id": "81"
          },
          {
            "id": "12",
            "title": "Cell tell institute of mobile phone technology PMNA",
            "team_lead_id": "35"
          },
          {
            "id": "13",
            "title": "Britco LLP Deira DXB",
            "team_lead_id": "36"
          },
          {
            "id": "14",
            "title": "Hyderabad institute of smartphone technology HYD",
            "team_lead_id": "41"
          },
          {
            "id": "15",
            "title": "Circuit tree smartphone institute GWY",
            "team_lead_id": null
          },
          {
            "id": "16",
            "title": "IMPT DELHI",
            "team_lead_id": "53"
          },
          {
            "id": "17",
            "title": "UD-Technical Ho, Kinfra",
            "team_lead_id": "90"
          },
          {
            "id": "18",
            "title": "Right career institute of technology VTK",
            "team_lead_id": "48"
          },
          {
            "id": "19",
            "title": "Techfluent smartphone engineering insitute OTP",
            "team_lead_id": "37"
          },
          {
            "id": "20",
            "title": "Globtech institute of future technology KNR",
            "team_lead_id": "47"
          },
          {
            "id": "21",
            "title": "Techno savvy institute PYN",
            "team_lead_id": "39"
          },
          {
            "id": "22",
            "title": "RTC institute of entrepreneurship ALV",
            "team_lead_id": "82"
          },
          {
            "id": "23",
            "title": "Hotspot institute of wireless comminication CLT",
            "team_lead_id": "38"
          },
          {
            "id": "24",
            "title": "Intelligent career institute of technology TCR",
            "team_lead_id": "40"
          },
          {
            "id": "26",
            "title": "Circuit tree smartphone institute GWY",
            "team_lead_id": "42"
          }
        ],
        "lead_status": [
          {
            "id": "1",
            "title": "Pending"
          },
          {
            "id": "2",
            "title": "Enrolled"
          },
          {
            "id": "3",
            "title": "Follow-up"
          },
          {
            "id": "4",
            "title": "Interested"
          },
          {
            "id": "5",
            "title": "Not-interested"
          },
          {
            "id": "7",
            "title": "Financial problem"
          }
        ],
        "lead_source": [
          {
            "id": "1",
            "title": "Google Lead Ad"
          },
          {
            "id": "2",
            "title": "Facebook Instagram Inquiry"
          },
          {
            "id": "3",
            "title": "Instagram"
          },
          {
            "id": "4",
            "title": "LinkedIn"
          },
          {
            "id": "5",
            "title": "Seminar"
          },
          {
            "id": "6",
            "title": "Reference "
          },
          {
            "id": "7",
            "title": "Data Collection"
          },
          {
            "id": "8",
            "title": "Website inquiry"
          },
          {
            "id": "10",
            "title": "Scholl seminar"
          },
          {
            "id": "11",
            "title": "Public seminar"
          },
          {
            "id": "12",
            "title": "Webinar"
          },
          {
            "id": "13",
            "title": "Service centre"
          },
          {
            "id": "14",
            "title": "Notice "
          },
          {
            "id": "15",
            "title": "Home visiting"
          },
          {
            "id": "16",
            "title": "Kudumbasree"
          },
          {
            "id": "17",
            "title": "Local club"
          },
          {
            "id": "18",
            "title": "Organaisation"
          },
          {
            "id": "19",
            "title": "Exhibition"
          },
          {
            "id": "20",
            "title": "Alumni"
          },
          {
            "id": "21",
            "title": "Public advertisement"
          },
          {
            "id": "22",
            "title": "TV advertisement"
          },
          {
            "id": "23",
            "title": "Data collection"
          },
          {
            "id": "24",
            "title": "News paper ad"
          },
          {
            "id": "25",
            "title": "Hoardings/sun pack "
          },
          {
            "id": "26",
            "title": "Just dial"
          },
          {
            "id": "27",
            "title": "Free class"
          },
          {
            "id": "28",
            "title": "Vlog/reals"
          },
          {
            "id": "29",
            "title": "Industrial visit"
          },
          {
            "id": "30",
            "title": "WhatsAp"
          },
          {
            "id": "31",
            "title": "Tele caller"
          },
          {
            "id": "32",
            "title": "Phone calls"
          },
          {
            "id": "33",
            "title": "SMS"
          },
          {
            "id": "34",
            "title": "Key partner"
          },
          {
            "id": "35",
            "title": "Transfer by BAI"
          },
          {
            "id": "36",
            "title": "Current students"
          },
          {
            "id": "37",
            "title": "Parents"
          },
          {
            "id": "38",
            "title": "Assigned by Ho"
          },
          {
            "id": "39",
            "title": "C/o"
          },
          {
            "id": "40",
            "title": "Walk in"
          }
        ],
        "courses": [
          {
            "id": "0",
            "title": "all"
          },
          {
            "id": "1",
            "title": "Digital Marketing"
          },
          {
            "id": "2",
            "title": "CDT"
          },
          {
            "id": "3",
            "title": "Short term"
          },
          {
            "id": "4",
            "title": "BCST"
          },
          {
            "id": "5",
            "title": "BCHP"
          },
          {
            "id": "6",
            "title": "BSFP"
          },
          {
            "id": "7",
            "title": "BSSP"
          },
          {
            "id": "8",
            "title": "BMAC"
          },
          {
            "id": "9",
            "title": "BAHP"
          },
          {
            "id": "10",
            "title": "BSTC"
          }
        ]
      }
    ];
    alldata = followups;
    isLoading = false;
    update();
  }

  TextEditingController follow_from_date = TextEditingController();
  TextEditingController follow_to_date = TextEditingController();

  String from_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";

  String to_date = "${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}";

  DateTime? fromDate;
  DateTime? toDate;
}
