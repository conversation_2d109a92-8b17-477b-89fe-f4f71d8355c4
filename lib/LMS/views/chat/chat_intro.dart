//
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// class chat_intro extends StatefulWidget {
//   @override
//   State<chat_intro> createState() => _loginState();
// }
//
// class _loginState extends State<chat_intro> {
//   Commoncontroller controller = Get.put(Commoncontroller());
//   String? sel_country;
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<Commoncontroller>(builder: (controller) {
//       return GestureDetector(
//         onTap: () {
//           FocusScope.of(context).unfocus();
//         },
//         child: Scaffold(
//           backgroundColor: textwhiteColor,
//           body: SingleChildScrollView(
//             child: Container(
//               child: Column(
//                 children: [
//                   Stack(
//                     children: [
//                       Container(
//                         child: Stack(
//                           children: [
//                             Image.asset(
//                               'assets/bg/chat_head.png',
//                               fit: BoxFit.fitWidth,
//                             ),
//                             Positioned(
//                               left: 0,
//                               right: 0,
//                               bottom: 0,
//                               top: 0,
//                               child: Container(
//                                 alignment: Alignment.center,
//                                 child: Center(
//                                   child: Container(
//                                     child: Image.asset(
//                                       'assets/logo/logo_text_w.png',
//                                       width: Get.width / 5,
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       AppBar(
//                         backgroundColor: appbarblackColor,
//                         surfaceTintColor: appbarblackColor,
//                         automaticallyImplyLeading: true,
//                         iconTheme: IconThemeData(color: textwhiteColor),
//                       ),
//                     ],
//                   ),
//
//                   SizedBox(
//                     height: 20,
//                   ),
//
//                   Container(
//                     child: Text(
//                       'Welcome to\nSpeeki Talk'.toUpperCase(),
//                       style: TextStyle(
//                           color: textblackColor,
//                           fontFamily: font_semibold,
//                           fontSize: Get.width / 19),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//
//                   SizedBox(
//                     height: Get.height / 40,
//                   ),
//
//                   Container(
//                     alignment: Alignment.center,
//                     padding: EdgeInsets.symmetric(horizontal: 20),
//                     child: Text(
//                       'Your personal AI language coach. Engage in real-time conversations and improve your English speaking skills with instant feedback. Our AI is here to help you practice and gain confidence in everyday dialogues, all at your own pace.',
//                       style: TextStyle(
//                           color: textblackColor.withOpacity(0.5),
//                           fontFamily: font_regular,
//                           fontSize: Get.width / 29),
//                       textAlign: TextAlign.center,
//                     ),
//                   ),
//                   // ----------------------------basic sec -----------------------------
//
//                   SizedBox(
//                     height: Get.height / 10,
//                   ),
//
//                   Container(
//                     padding: EdgeInsets.symmetric(horizontal: 20),
//                     child: Row(
//                       children: [
//                         Expanded(
//                           child: GestureDetector(
//                             onTap: () {
//                               setState(() {
//                                 sel_country = 'uk';
//                               });
//                             },
//                             child: Container(
//                               decoration: BoxDecoration(
//                                   border: Border.all(
//                                       color: textblackColor.withOpacity(0.2),
//                                       width: 1),
//                                   borderRadius: BorderRadius.circular(10)),
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: 10, vertical: 15),
//                               child: Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   Row(
//                                     children: [
//                                       Image.asset(
//                                         'assets/icons/uk_flag.png',
//                                         width: Get.width / 16,
//                                       ),
//                                       SizedBox(
//                                         width: 12,
//                                       ),
//                                       Container(
//                                         child: Text(
//                                           'British',
//                                           style: TextStyle(
//                                               color: textblackColor,
//                                               fontFamily: font_regular,
//                                               fontSize: Get.width / 27),
//                                           textAlign: TextAlign.center,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   Image.asset(
//                                       'assets/icons/onboard_active_slider.png',
//                                       width: Get.width / 19,
//                                       color: sel_country == 'uk'
//                                           ? primaryColor
//                                           : Colors.black26),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(
//                           width: 15,
//                         ),
//                         Expanded(
//                           child: GestureDetector(
//                             onTap: () {
//                               setState(() {
//                                 sel_country = 'us';
//                               });
//                             },
//                             child: Container(
//                               decoration: BoxDecoration(
//                                   border: Border.all(
//                                       color: textblackColor.withOpacity(0.2),
//                                       width: 1),
//                                   borderRadius: BorderRadius.circular(10)),
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: 10, vertical: 15),
//                               child: Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 crossAxisAlignment: CrossAxisAlignment.center,
//                                 children: [
//                                   Row(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.center,
//                                     children: [
//                                       Image.asset(
//                                         'assets/icons/us_flag.png',
//                                         width: Get.width / 16,
//                                       ),
//                                       SizedBox(
//                                         width: 12,
//                                       ),
//                                       Container(
//                                         child: Text(
//                                           'American',
//                                           style: TextStyle(
//                                               color: textblackColor,
//                                               fontFamily: font_regular,
//                                               fontSize: Get.width / 27),
//                                           textAlign: TextAlign.center,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   Image.asset(
//                                       'assets/icons/onboard_active_slider.png',
//                                       width: Get.width / 19,
//                                       color: sel_country == 'us'
//                                           ? primaryColor
//                                           : Colors.black26),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//
//                   SizedBox(
//                     height: Get.height / 10,
//                   ),
//
//                   Container(
//                     child: cust_elevatedbutton(
//                       width: Get.width / 2,
//                       onPressed: () {
//                         if (sel_country != null) {
//                           Get.to(chat_screen(
//                             sel_country:
//                                 sel_country == 'us' ? 'en-us' : 'en-gb',
//                           ));
//                         } else {
//                           Get.snackbar('', '',
//                               titleText: Text(
//                                 'Country',
//                                 style: TextStyle(
//                                     color: textblackColor,
//                                     fontFamily: font_semibold,
//                                     fontSize: Get.width / 27),
//                               ),
//                               messageText: Text(
//                                 'Please choose a country to continue..',
//                                 style: TextStyle(
//                                     color: textblackColor,
//                                     fontFamily: font_regular,
//                                     fontSize: Get.width / 33),
//                               ),
//                               backgroundColor: secondaryColor.withOpacity(0.5),
//                               snackPosition: SnackPosition.BOTTOM);
//                         }
//                       },
//                       borderRadius: BorderRadius.circular(10),
//                       height: 60,
//                       gradient: LinearGradient(
//                         colors: [
//                           Colors.white,
//                           Colors.white,
//                         ],
//                         begin: Alignment.centerLeft,
//                         end: Alignment.centerRight,
//                       ),
//                       child: Text(
//                         "Let's Start",
//                         style: TextStyle(
//                             fontFamily: font_medium, color: textwhiteColor),
//                       ),
//                     ),
//                   ),
//
//                   SizedBox(
//                     height: Get.height / 20,
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       );
//     });
//   }
// }
