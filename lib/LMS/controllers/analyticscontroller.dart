import 'dart:developer';

import 'package:edutalim/LMS/api/main_apis.dart';
import 'package:edutalim/components/utils.dart';
import 'package:get/get.dart';

class AnalysisController extends GetxController {
  int? selectedActiveCourseIndex = 0;
  int? selectedActiveSubjectIndex= 0;

  int? selectedCompletedCourseIndex = 0;
  int? selectedCompletedSubjectIndex= 0;

  bool isAnalysisLoading = true;
  List activeCourses = [];
  List completedCourse = [];

  getAnalysis() async {
    try {
      isAnalysisLoading = true;
      update();


      var data = await ApiBaseHandler.analysis();
      activeCourses = data['data']['courses']['active_courses'];
      completedCourse = data['data']['courses']['completed_courses'];

    } catch (e) {
      log(e.toString());
      toast_error("Something went wrong...");
    } finally {
      isAnalysisLoading = false;
      update();
    }
  }
}
