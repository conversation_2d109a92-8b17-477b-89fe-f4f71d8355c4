import 'dart:async';
import 'dart:io';
import 'package:flutter/animation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../CRM/views/crm_dashboard/crm_dashboard.dart';
import '../views/auth/login.dart';
import '../views/auth/onboarding/onboarding.dart';
import '../LMS/api/main_apis.dart';
import '../components/constants.dart';
import '../LMS/views/dashboard/dashboard.dart';
import '../LMS/views/tools/force_update.dart';

class Commoncontroller extends GetxController {
  bool isProfileLoading = true;
  var profilePage;

  void getProfile() async {
    isProfileLoading = true;
    var data = await ApiBaseHandler.profile();
    profilePage = data['data'];

    isProfileLoading = false;
    update();
  }

  List userdata = [], plans = [];
  bool isloading = true;

  Map appversion = {};
  bool is_appversion_loading = true;

  get_appversion() async {
    var data = await ApiBaseHandler.appVersion();
    update();
    appversion = data['data'];
    print('appversion-------------$appversion');
    if (Platform.isIOS) {
      if (appversion['ios']['payment_version'].toString() == ios_version.toString()) {
        GetStorage().write('pay_version', 'true');
      } else {
        GetStorage().write('pay_version', 'false');
      }
    } else {
      if (appversion['android']['payment_version'].toString() == android_version.toString()) {
        GetStorage().write('pay_version', 'true');
      } else {
        GetStorage().write('pay_version', 'false');
      }
    }
    print('iOS-----');
    if (GetPlatform.isIOS && int.parse(dot_replace(appversion['ios']['force_update'].toString())) > int.parse(dot_replace(ios_version.toString())) ||
        GetPlatform.isAndroid && int.parse(dot_replace(appversion['android']['force_update'].toString())) > int.parse(dot_replace(android_version.toString()))) {
      Get.offAll(() => force_update(data: [data]));
    } else {
      startTimer();
    }

    is_appversion_loading = false;
    update();
  }

  dot_replace(string) {
    return string.toString().replaceAll('.', '').toString();
  }

  startTimer() async {
    var duration = const Duration(seconds: 2);
    return Timer(duration, () {
      Get.offAll(
          () => GetStorage().read('onboarding').toString() == "false"
              ? GetStorage().read('login_status').toString() == "true"
                  ? GetStorage().read('role_id').toString() == "2"
                      ? dashboard()
                      : crm_dashboard()
                  : Login()
              : onboarding(),
          transition: Transition.noTransition,
          duration: Duration(seconds: 0));
    });
  }
}
