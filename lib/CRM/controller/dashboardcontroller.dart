import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../api/apis.dart';

class Dashboardcontroller extends GetxController{

  bool homeLoading = false;
  Map homedata = {};

  fethomedata(loadable) async {
    if(loadable)
      {
        homeLoading = true;
        update();
      }
    var data = await CRMApiBaseHandler.fet_homepage_data();
    // print("----------------poderkka--------------------"+data.toString());
    homedata = data['data'];
    homeLoading = false;
    update();
  }


}