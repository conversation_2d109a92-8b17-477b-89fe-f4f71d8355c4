// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:speekiai/components/constants.dart';

// class ChatAppBar extends StatelessWidget {
//   ChatAppBar({super.key,required this.title});
//   String title;

//   @override
//   PreferredSizeWidget build(BuildContext context) {
//     return AppBar(
//       backgroundColor: textwhiteColor,
//       surfaceTintColor: textwhiteColor,
//       leading: GestureDetector(
//         onTap: () {
//           Get.back();
//         },
//         child: Container(
//           padding: EdgeInsets.all(10),
//           child: Image.asset('assets/icons/close.png'),
//         ),
//       ),
//       title: Container(
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.start,
//           children: [
//             Container(
//                 child: Image.asset(
//               'assets/logo/logo_plain.png',
//               width: Get.width / 13,
//             )),
//             SizedBox(
//               width: 10,
//             ),
//             Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Container(
//                   alignment: Alignment.centerLeft,
//                   width: Get.width / 1.7,
//                   child: Text(
//                     title.toString(),
//                     style: TextStyle(
//                         color: textblackColor,
//                         fontFamily: font_medium,
//                         fontSize: Get.width / 26),
//                     textAlign: TextAlign.center,
//                   ),
//                 ),
//                 SizedBox(
//                   height: 3,
//                 ),
//                 Container(
//                   alignment: Alignment.centerLeft,
//                   width: Get.width / 1.7,
//                   child: Text(
//                     'John, Alia, julie, Joseph, Andrew, Anfal, john..',
//                     style: TextStyle(
//                         color: primaryColor,
//                         fontFamily: font_regular,
//                         fontSize: Get.width / 33),
//                     textAlign: TextAlign.center,
//                   ),
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//       actions: [
//         Image.asset(
//           'assets/icons/more_vert.png',
//           height: Get.width / 19,
//         ),
//         SizedBox(
//           width: 15,
//         ),
//       ],
//     );
//   }
// }
